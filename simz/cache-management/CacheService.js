/**
 *
 *
 * @param embedding
 * @param response
 */

class CacheService {
  constructor({ redisClient }) {
    this.redisClient = redisClient;
  }
}

// async function storeInRedisCacheWithTTL(embedding, response) {
//   const key = JSON.stringify(embedding);
//   await setAsync(key, response);
// }

async function storeResponseWithAudioInCache(embedding, responseText, audioFilePath) {
  console.log('Embedding value:', embedding);
  const key = JSON.stringify(embedding);

  const responseObject = {
    content: responseText, // Store the response text
    audio_file_name: audioFilePath, // Path to the generated audio file
    experisAt: new Date().toISOString(), // Store the timestamp
  };
  // await setAsync(key, JSON.stringify(responseObject));
  return responseObject;
}

module.exports = { CacheService, storeResponseWithAudioInCache };

//
//
//
//

//

//
// async function synthesizeSpeechAndSaveToFile(text, filename, chooseVoice = 'Nick') {
//     text = text.trim();
//     if (text.endsWith('.')) {
//         text = text.concat('..');
//     } else {
//         text = text.concat('...');
//     }
//     console.log('Synthesizing speech:', text);
//
//     let voice_id = 'IP2bcEBbLDKEhcl0Klv0'; // Default voice (Nick)
//     if (chooseVoice === 'Soft') {
//         voice_id = 'vFnQhrpeWx4hMde1AOP1';
//     } else if (chooseVoice === 'Dakota') {
//         voice_id = 'P7x743VjyZEOihNNygQ9';
//     } else if (chooseVoice === 'Maia') {
//         voice_id = 'd09rnXspMxHrYaelnKgY';  // Maia
//     }
//
//     voice_id = 'd09rnXspMxHrYaelnKgY';  // Maia
//
//     const wsUrl = `wss://api.elevenlabs.io/v1/text-to-speech/${voice_id}/stream-input?model_id=eleven_turbo_v2_5`;
//     const socket = new WebSocket(wsUrl);
//
//     socket.onopen = function () {
//         const bosMessage = {
//             "text": " ",
//             "voice_settings": {
//                 "stability": 0.60,
//                 "similarity_boost": 0.99,
//             },
//             "xi_api_key": process.env.ELEVENLABS_API_KEY,
//         };
//         socket.send(JSON.stringify(bosMessage));
//
//         const textMessage = {
//             "text": text,
//             "try_trigger_generation": true,
//         };
//         socket.send(JSON.stringify(textMessage));
//
//         const eosMessage = {
//             "text": ""
//         };
//         socket.send(JSON.stringify(eosMessage));
//     };
//
//     socket.onmessage = function (event) {
//         const response = JSON.parse(event.data);
//         if (response.audio) {
//             const audioChunk = Buffer.from(response.audio, 'base64');
//             fs.appendFileSync(`./cache/${filename}`, audioChunk);
//         }
//
//         if (response.isFinal) {
//             const convertedFilePath = path.join(__dirname, `./cache/${filename}_converted.wav`);
//             convertAndTruncateAudio(`./cache/${filename}`, './audio/converted_sound_effect_CallCenter.mp3', 960, convertedFilePath, () => {
//                 // Audio is now saved
//                 console.log(`Audio generated and saved to ${convertedFilePath}`);
//             });
//         }
//     };
//
//     socket.onerror = function (error) {
//         console.error(`WebSocket Error: ${error}`);
//         // ws.send(JSON.stringify({
//         //   "event": "error",
//         //   "message": "Failed to synthesize speech."
//         // }));
//     };
// }
//
// // Function to convert and truncate audio
// async function convertAndTruncateAudio(inputFilePath, noiseFilePath, bytesToTruncate, outputFilePath, callback) {
//     const tempMixedFilePath = path.join(path.dirname(outputFilePath), path.basename(outputFilePath, path.extname(outputFilePath)) + '_mixed.wav');
//     const tempConvertedFilePath = path.join(path.dirname(outputFilePath), path.basename(outputFilePath, path.extname(outputFilePath)) + '_converted.wav');
//
//     const processAudio = (sourceFilePath) => {
//         ffmpeg(sourceFilePath)
//             .audioCodec('pcm_mulaw')
//             .audioFrequency(8000)
//             .output(tempConvertedFilePath)
//             .on('end', function () {
//                 console.log('Audio file converted to Mu-law successfully.');
//                 fs.readFile(tempConvertedFilePath, (err, data) => {
//                     if (err) {
//                         console.error('Failed to read converted audio file:', err);
//                         return callback(err);
//                     }
//                     const truncatedData = data.slice(bytesToTruncate, data.length - 720);
//                     fs.writeFile(outputFilePath, truncatedData, (err) => {
//                         if (err) {
//                             console.error('Failed to write truncated audio file:', err);
//                             return callback(err);
//                         }
//                         console.log('Truncated audio file saved successfully.');
//                         callback();
//                     });
//                 });
//             })
//             .on('error', function (err) {
//                 console.log('Error during conversion:', err);
//                 callback(err);
//             })
//             .run();
//     };
//
//     processAudio(inputFilePath);
// }
//
// async function handleTextAndAudioProcessing(text, response) {
//     const embedding = await openai.embeddings.create({
//         model: "text-embedding-ada-002",
//         input: text,
//     }).then(response => response.data[0].embedding);
//
//     // Generate and save the audio file
//     const filename = `audio_${Date.now()}.wav`;
//     await synthesizeSpeechAndSaveToFile(response.content, filename, null, null);
//
//     // Store text, audio path, and other details in Redis
//     const audioFilePath = `./cache/${filename}`;
//     const storeResponse = await storeResponseWithAudioInCache(embedding, response.content, audioFilePath);
//
//     console.log(`Processed text and saved response with audio: ${audioFilePath}`);
//
//     return storeResponse;
// }
// async function getEmbeddingsBatch(queries) {
//     const embeddingResponse = await openai.embeddings.create({
//         model: "text-embedding-ada-002",
//         input: queries,
//     });
//     return embeddingResponse.data.map(d => d.embedding); // Return array of embeddings
// }
//
//
// async function checkRedisCache(embedding, threshold = 0.8) {
//     const cacheKeys = await keysAsync('*'); // Get all keys in Redis
//     let bestMatch = null;
//     let highestSimilarity = threshold; // Initialize with the threshold
//
//     for (const key of cacheKeys) {
//         const cachedEmbedding = JSON.parse(key); // Parse embedding back from key
//         if (cachedEmbedding.length === embedding.length) { // Ensure same length
//             const similarity = cosineSimilarity(cachedEmbedding, embedding);
//             // console.log("Similarity:", similarity);
//
//             if (similarity > highestSimilarity) {
//                 highestSimilarity = similarity;
//                 const cachedResponse = await getAsync(key); // Get the cached response
//                 bestMatch = cachedResponse; // Store the best matching response
//             }
//         }
//     }
//
//     if (bestMatch) {
//         console.log("Best match found with similarity:", highestSimilarity);
//         return bestMatch; // Return the best matching cached response
//     }
//
//     return null; // No match found above the threshold
// }
//
// // update the value in redis cache
// async function updateRedisCache(key, value) {
//     return new Promise((resolve, reject) => {
//             redisClient.set(key, value, (err, reply) => {
//                     if (err) return reject(err);
//                     resolve(reply);
//                 }
//             );
//         }
//     );
// }
//
// // get the value from redis cache
// async function getFromRedisCache(key) {
//     return new Promise((resolve, reject) => {
//             redisClient.get(key, (err, value) => {
//                     if (err) return reject(err);
//                     resolve(value);
//                 }
//             );
//         }
//     );
// }
//
// // Example usage
// async function handleStoreAndCheckEmbedding() {
//
//     await handleTextAndAudioProcessing("How long will it take to settle the case?", { content: "Based on what I've seen, this can take from months to years to resolve and settlements vary from 10,000 to $100,000; there are several factors that determine this, so I would definitely ask the firm these types of questions as they would have a better idea.", experisAt: '' });
//
//     const passcase = 'How long will it take to settle the case?';
//
//     const passcaseEmbedding = await getEmbeddingsBatch([passcase]);
//     console.log("Passcase embedding:", passcaseEmbedding[0].length);
//
//     console.log("Embedding stored.");
//
//     const cachedResponse = await checkRedisCache(passcaseEmbedding[0]);
//
//     if (cachedResponse) {
//
//         console.log("Found similar query:", cachedResponse);
//         // get the value from redis cache
//     } else {
//
//         console.log("No similar query found.");
//
//     }
//
// }
// Call the async function

// handleStoreAndCheckEmbedding();
