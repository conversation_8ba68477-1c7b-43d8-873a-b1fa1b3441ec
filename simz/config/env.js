// config/env.js
require('dotenv').config();

const env = {
  port: process.env.PORT || 30000,
  useLLM: process.env.USE_LLM || 'openaiLLM', // Default to OpenAI if not specified

  // Twilio configuration
  twilio: {
    accountSid: process.env.TWILIO_ACCOUNT_SID || '',
    authToken: process.env.TWILIO_AUTH_TOKEN || '',
    phoneNumber: process.env.TWILIO_PHONE_NUMBER || '',
  },

  // OpenAI configuration
  openai: {
    apiKey: process.env.OPENAI_API_KEY || '',
  },

  // SendGrid configuration
  sendgrid: {
    apiKey: process.env.SENDGRID_API_KEY || '',
  },

  // Azure configuration
  azure: {
    apiKey: process.env.AZURE_API_KEY || '',
    apiKeyCNUS: process.env.AZURE_API_KEY_CNUS || '',
    openaiApiKey: process.env.AZURE_OPENAI_API_KEY || '',
    region: process.env.AZURE_REGION || 'westus',
    speechKey: process.env.AZURE_SPEECH_KEY || '',
    speechRegion: process.env.AZURE_SPEECH_REGION || 'westus',
  },

  // Google configuration
  google: {
    applicationCredentials: process.env.GOOGLE_APPLICATION_CREDENTIALS || '',
    placesApiKey: process.env.GOOGLE_PLACES_API_KEY || '',
  },

  // Elevenlabs configuration
  elevenlabs: {
    apiKey: process.env.ELEVENLABS_API_KEY || '',
  },

  // Deepgram configuration
  deepgram: {
    apiKey: process.env.DEEPGRAM_API_KEY || '',
  },

  // Grok configuration
  grok: {
    apiKey: process.env.GROK_API_KEY || '',
    baseURL: process.env.GROK_API_BASE_URL || '',
  },

  // Gemini configuration
  gemini: {
    apiKey: process.env.GEMINI_API_KEY || '',
  },

  // Database configuration
  db: {
    host: process.env.MYSQL_HOST || 'localhost',
    user: process.env.MYSQL_USER || 'root',
    password: process.env.MYSQL_PASSWORD || '',
    database: process.env.MYSQL_DATABASE || 'your_database_name',
  },

  server: {
    url: process.env.SERVER_URL || 'https://related-marten-sterling.ngrok-free.app',
  },
};

// Validate required environment variables
const requiredEnvVars = [
  'TWILIO_ACCOUNT_SID',
  'TWILIO_AUTH_TOKEN',
  'TWILIO_PHONE_NUMBER',
  'OPENAI_API_KEY',
  'AZURE_OPENAI_API_KEY',
  'AZURE_SPEECH_KEY',
  'ELEVENLABS_API_KEY',
  'DEEPGRAM_API_KEY',
  'GROK_API_KEY',
  'MYSQL_HOST',
  'MYSQL_USER',
  'MYSQL_PASSWORD',
  'MYSQL_DATABASE',
];

requiredEnvVars.forEach((varName) => {
  if (!process.env[varName]) {
    throw new Error(`Missing required environment variable: ${varName}`);
  }
});

module.exports = env;
