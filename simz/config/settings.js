// config/settings.js
// App's setting （port number, etc.）

const env = require('./env');

/**
 * Application settings and defaults.
 */
const settings = {
  port: env.port,
  logLevel: process.env.LOG_LEVEL || 'info', // Default log level
  defaultVoice: process.env.CHOOSE_VOICE || 'Maia', // Default voice for TTS
  withBackgroundNoise: process.env.WITH_BGN === 'true' || false, // Default background noise setting
  waitClientResponseTime: 45000, // 45 seconds, as used in CallService
};

module.exports = settings;
