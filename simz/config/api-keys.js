// config/api-keys.js
const env = require('./env');

/**
 * Centralized API keys for the application.
 */
const apiKeys = {
  twilio: {
    accountSid: env.twilio.accountSid,
    authToken: env.twilio.authToken,
  },
  openai: {
    apiKey: env.openai.apiKey,
  },
  azure: {
    openaiApiKey: env.azure.openaiApiKey,
    speechKey: env.azure.speechKey,
  },
  elevenlabs: {
    apiKey: env.elevenlabs.apiKey,
  },
  deepgram: {
    apiKey: env.deepgram.apiKey,
  },
  grok: {
    apiKey: env.grok.apiKey,
  },
  gemini: {
    apiKey: env.gemini.apiKey,
  },
};

module.exports = apiKeys;
