const { CallRepository } = require('../../call-management/repositories/CallRepository');
const bcrypt = require('bcrypt');

class UserService {
  constructor() {
    this.callRepository = new CallRepository();
  }

  async validateUser(username, password) {
    const [users] = await this.callRepository.mysqlPool.query(
      'SELECT User_ID, Username, Password_Hash FROM Users WHERE Username = ?',
      [username]
    );
    const user = users[0];
    if (user && (await bcrypt.compare(password, user.Password_Hash))) {
      console.log('[UserService] User found and password is correct');
      return user;
    }
    console.log('[UserService] User not found or password is incorrect');
    return null;
  }

  async createUser(username, password, email = null) {
    // 检查用户名是否已存在
    const [existing] = await this.callRepository.mysqlPool.query(
      'SELECT Username FROM Users WHERE Username = ?',
      [username]
    );
    if (existing.length > 0) {
      throw new Error('Username already exists');
    }

    const hashedPassword = await bcrypt.hash(password, 10); // saltRounds = 10
    const userId = `user_${Date.now()}`; // 简单生成唯一 ID，可用 UUID 替代

    // 插入用户数据，填充所有必填字段，允许可选字段为默认值
    await this.callRepository.mysqlPool.query(
      `INSERT INTO Users (
        User_ID, Username, Email, Active, Password_Hash, Role, Last_Login, Created_At, Updated_At
      ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [userId, username, email, '1', hashedPassword, 'Member', null]
    );

    return { User_ID: userId, Username: username };
  }

  async getUserById(userId) {
    const [users] = await this.callRepository.mysqlPool.query(
      'SELECT * FROM Users WHERE User_ID = ?',
      [userId]
    );
    return users[0] || null;
  }
}

module.exports = { UserService };
