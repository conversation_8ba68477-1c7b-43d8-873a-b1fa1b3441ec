const { UserService } = require('../services/UserService');
const jwt = require('jsonwebtoken');

class AuthController {
  constructor() {
    this.userService = new UserService();
  }

  async login(req, res) {
    const { username, password } = req.body;
    try {
      const user = await this.userService.validateUser(username, password);
      if (!user) {
        return res.status(401).json({ message: 'Invalid credentials' });
      }
      const token = jwt.sign(
        { userId: user.User_ID, username: user.Username },
        process.env.JWT_SECRET,
        { expiresIn: '3h' }
      );
      const userTag = `[user: ${user.Username || username}]`;
      console.log(`${userTag} [AuthController] User logged in`);
      res.json({ token });
    } catch (error) {
      const userTag = `[user: ${username}]`;
      console.error(`${userTag} [AuthController] Login error:`, error);
      res.status(500).json({ message: '<PERSON><PERSON> failed', error: error.message });
    }
  }

  async register(req, res) {
    const { username, password, email } = req.body;
    try {
      if (!username || !password) {
        return res.status(400).json({ message: 'Username and password are required' });
      }
      const user = await this.userService.createUser(username, password, email);
      const userTag = `[user: ${username}]`;
      console.log(`${userTag} [AuthController] User registered successfully`);
      res.status(201).json({ message: 'User registered successfully', userId: user.User_ID });
    } catch (error) {
      const userTag = `[user: ${username}]`;
      console.error(`${userTag} [AuthController] Register error:`, error);
      if (error.message === 'Username already exists') {
        return res.status(409).json({ message: error.message });
      }
      res.status(500).json({ message: 'Registration failed', error: error.message });
    }
  }
}

module.exports = { AuthController };
