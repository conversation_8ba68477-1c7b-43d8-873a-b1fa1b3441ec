module.exports = {
  apps: [{
    name: 'twilio-app',
    script: 'index.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
    },
    error_file: process.env.PM2_ERROR_LOG || './logs/twilio-app-error.log',
    out_file: process.env.PM2_OUT_LOG || './logs/twilio-app-out.log',
    log_file: process.env.PM2_COMBINED_LOG || './logs/twilio-app-combined.log',
    time: true,
    // 健康检查配置
    exp_backoff_restart_delay: 100,
    // 最大重启次数，避免无限重启
    max_restarts: 10,
    // 忽略监视的目录
    ignore_watch: ['node_modules', 'logs', '.git'],
    // 应用延迟启动时间，单位毫秒
    wait_ready: true,
    // 应用退出的正常状态码，如果应用退出时状态码在此列表中，将不会重启
    kill_timeout: 3000,
    // 强制退出应用的等待时间
    listen_timeout: 5000,
    // 在调用 stop 方法时等待的时间
    source_map_support: true
  }]
}; 