// pipeline {
//   agent {
//     node {
//       label 'built-in'
//     }


//pipeline {
  //agent { node { label 'built-in' } }

  //tools {
    //nodejs 'Node 20'
  //}

  //environment {
    //DEPLOY_DIR = '/home/<USER>/deploy/simz'
  //}

  //stages {
    //stage('Prepare Deploy Directory') {
      //steps {
        //script {
          //withCredentials([usernamePassword(credentialsId: '1ed35cf8-705c-4f6e-8f0a-aa0055fc0052', usernameVariable: 'GIT_USER', passwordVariable: 'GIT_PASS')]) {
            //sh """
              //mkdir -p \$DEPLOY_DIR
              //cd \$DEPLOY_DIR

              //if [ ! -d ".git" ]; then
                //echo "Cloning repo."
                //echo "Running..."
                //echo "Cloning repo."
                //git clone -b test https://\$GIT_USER:\$<EMAIL>/thelegalleads/simz.git .
              //else
                //echo "Repo exists, pulling latest"
                //git remote set-url origin https://\$GIT_USER:\$<EMAIL>/thelegalleads/simz.git
                //git fetch origin test
                //git checkout test
                //git reset --hard origin/test
              //fi
            //"""
          //}
        //}
      //}
    //}

    //stage('Install & Build') {
      //steps {
        //dir("${env.DEPLOY_DIR}") {
        //sh '''
            //export CI=false
            //npm install
        //'''
        //}
      //}
    //}

    //stage('Start PM2') {
      //steps {
        //dir("${env.DEPLOY_DIR}") {
          //sh '''
            //pm2 delete all || true
            //pm2 start npm --name simz-backend
          //'''
        //}
      //}
    //}
  //}
//}

pipeline {
  agent { node { label 'built-in' } }

  tools {
    nodejs 'Node 20'
  }

  environment {
    DEPLOY_DIR     = "${env.BRANCH_NAME == 'main' ? '/home/<USER>/simz' : '/home/<USER>/deploy/simz'}"
    CREDENTIALS_ID = "${env.BRANCH_NAME == 'main' ? '126879ea-371c-42ea-add8-8471f3140345' : '1ed35cf8-705c-4f6e-8f0a-aa0055fc0052'}"
    GIT_BRANCH     = "${env.BRANCH_NAME ?: 'test'}"
  }

  stages {

    stage('Print Env Variables') {
      steps {
        sh '''
          echo "DEPLOY_DIR = $DEPLOY_DIR"
          echo "CREDENTIALS_ID = $CREDENTIALS_ID"
          echo "GIT_BRANCH = $GIT_BRANCH"
        '''
      }
    }


    stage('Prepare Deploy Directory') {
      steps {
        script {
          withCredentials([usernamePassword(credentialsId: "${env.CREDENTIALS_ID}", usernameVariable: 'GIT_USER', passwordVariable: 'GIT_PASS')]) {
            sh """
              mkdir -p \$DEPLOY_DIR
              cd \$DEPLOY_DIR
              
              git config --global --add safe.directory \$DEPLOY_DIR

              if [ ! -d ".git" ]; then
                echo "Cloning repo."
                git clone -b \$GIT_BRANCH https://\$GIT_USER:\$<EMAIL>/thelegalleads/simz.git .
              else
                echo "Repo exists, pulling latest"
                git remote set-url origin https://\$GIT_USER:\$<EMAIL>/thelegalleads/simz.git
                git fetch origin \$GIT_BRANCH
                git checkout \$GIT_BRANCH
                git reset --hard origin/\$GIT_BRANCH
              fi
            """
          }
        }
      }
    }

    stage('Install & Build') {
      steps {
        dir("${env.DEPLOY_DIR}") {
          sh '''
            export CI=false
            npm install
          '''
        }
      }
    }

    stage('Start PM2') {
      steps {
        dir("${env.DEPLOY_DIR}") {
          sh '''
            pm2 restart 0
          '''
        }
      }
    }
  }
}