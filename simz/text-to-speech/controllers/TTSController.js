// text-to-speech/controllers/TTSController.js
class TTSController {
  constructor(wss, ttsService, callService) {
    this.wss = wss;
    this.ttsService = ttsService;
    this.callService = callService;
  }

  async synthesizeText(req, res) {
    const { text, filename, streamSid } = req.body;
    try {
      const state = this.callService.getCallState(streamSid);
      const ws = state.getState('ws');
      if (!ws) {
        return res.status(400).json({ error: 'No active WebSocket connection for this streamSid' });
      }
      await this.ttsService.synthesizeSpeech(
        text,
        filename || `response_${Date.now()}.wav`,
        ws,
        streamSid,
        false,
        state.getState('withBGN') || false
      );
      res.json({ message: 'Text synthesized successfully' });
    } catch (error) {
      console.error('[TTSController] Error in synthesizeText:', error);
      res.status(500).json({ error: 'Failed to synthesize text' });
    }
  }
}

module.exports = { TTSController };
