// text-to-speech/services/ElevenLabsTTSService.js
const WebSocket = require('ws');
const path = require('path');
const ffmpeg = require('fluent-ffmpeg');
const ffmpegInstaller = require('@ffmpeg-installer/ffmpeg');
const fs = require('fs');

ffmpeg.setFfmpegPath(ffmpegInstaller.path);

class ElevenLabsTTSService {
  constructor() {
    this.chooseVoice = process.env.CHOOSE_VOICE || 'Maia';
    this.apiKey = process.env.ELEVENLABS_API_KEY;
    this.dependencies = {};
    this.voiceCache = {};

    this.voiceMap = {
      Nick: 'IP2bcEBbLDKEhcl0Klv0',
      Soft: 'vFnQhrpeWx4hMde1AOP1',
      Dakota: 'P7x743VjyZEOihNNygQ9',
      Maia: 'd09rnXspMxHrYaelnKgY',
      Cassidy: '56AoDkrOh6qfVPDXZ7Pt',
      <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: '1SM7GgM6IMuvQlz2BwM3',
      <PERSON>_natural: 'OYTbf65OHHFELVut7v2H',
      <PERSON>_Realistic: 'J5iaaqzR5zn6HFG4jV3b',
      Alexandra: 'kdmDKE6EkgrWrrykO9Qt',
    };
    this.voiceId = this.voiceMap[this.chooseVoice] || this.voiceMap['Maia'];
    console.log('[ElevenLabsTTSService] Initialized');
    if (!this.apiKey) {
      console.error('[ElevenLabsTTSService] ELEVENLABS_API_KEY is not set');
    }
  }

  getVoiceForRoute(streamSid) {
    const callService = this.dependencies.callService;
    if (callService) {
      const callState = callService.getCallState(streamSid);
      const promptData = callState.getState('promptData');

      if (promptData?.promptType === 'simz-steakhouse') {
        return 'Joseph_Realistic';
      }
      return 'Maia';
    } else {
      return 'Maia';
    }
  }

  setDependencies(deps) {
    this.dependencies = { ...this.dependencies, ...deps };
  }

  async synthesizeSpeech(
    text,
    filename,
    ws,
    streamSid,
    cannotBeCut,
    withBGN,
    callback,
    agentId = null,
    monitoringClients = null
  ) {
    console.log('[ElevenLabsTTSService] Synthesizing:🟣', text, 'streamSid:', streamSid);

    let currentVoice;
    if (this.voiceCache[streamSid]) {
      currentVoice = this.voiceCache[streamSid];
    } else {
      currentVoice = this.getVoiceForRoute(streamSid);
      this.voiceCache[streamSid] = currentVoice;
    }
    const voiceId = this.voiceMap[currentVoice];
    console.log('[ElevenLabsTTSService] Using voice:', currentVoice, 'voice_id:', voiceId);
    let voiceSettings = {};
    if (currentVoice === 'Alexandra') {
      voiceSettings = {
        stability: 0.7,
        similarity_boost: 0.78,
        speed: 1.15,
      };
    } else if (currentVoice === 'Joseph_Realistic') {
      voiceSettings = {
        stability: 0.75,
        similarity_boost: 0.85,
        speed: 1.15,
      };
    } else if (currentVoice === 'Nick') {
      voiceSettings = {
        stability: 1,
        similarity_boost: 0.75,
        speed: 1,
      };
    } else {
      voiceSettings = {
        stability: 0.62,
        similarity_boost: 0.65,
        speed: 1,
      };
    }
    // Pre-process text
    text = text
      .replace(/\bblvd\b/gi, 'boulevard')
      .replace('Ave', 'Avenue')
      .trim();
    if (text.endsWith('.')) {
      text = text.concat('..');
    } else {
      text = text.concat('');
    }

    const wsUrl = `wss://api.elevenlabs.io/v1/text-to-speech/${voiceId}/stream-input?model_id=eleven_turbo_v2&optimize_streaming_latency=3&output_format=ulaw_8000`;
    const socket = new WebSocket(wsUrl);

    let audioBuffer = Buffer.alloc(0);

    socket.onopen = () => {
      console.log('[ElevenLabsTTSService] WebSocket connection opened');
      const bosMessage = {
        text: ' ',
        voice_settings: voiceSettings,
        xi_api_key: this.apiKey,
      };
      socket.send(JSON.stringify(bosMessage));

      const textMessage = {
        text: text,
        try_trigger_generation: true,
      };
      socket.send(JSON.stringify(textMessage));

      const eosMessage = { text: '' };
      socket.send(JSON.stringify(eosMessage));
    };

    socket.onmessage = async (event) => {
      const response = JSON.parse(event.data);

      if (response.audio) {
        const audioChunk = Buffer.from(response.audio, 'base64');
        audioBuffer = Buffer.concat([audioBuffer, audioChunk]);
      }

      if (response.isFinal) {
        if (withBGN) {
          const tempPath = path.join(__dirname, '../../audios', `temp_${Date.now()}.wav`);
          const outputPath = path.join(__dirname, '../../audios', `mixed_${filename}`);
          fs.writeFileSync(tempPath, audioBuffer);
          await this.addBackgroundNoise(tempPath, outputPath);
          await this.sendAudio(ws, streamSid, outputPath, callback);
          fs.unlinkSync(tempPath);
        } else {
          if (agentId && monitoringClients.has(agentId)) {
            monitoringClients.get(agentId).forEach((clientInfo, clientWs) => {
              if (clientWs.readyState === 1 && clientInfo.listen) {
                clientWs.send(
                  JSON.stringify({ type: 'MONITOR_AUDIO_AI', callSid: agentId, audio: audioBuffer })
                );
              }
            });
          }

          this.sendAudioBuffer(ws, streamSid, audioBuffer, callback, text, filename);
        }
      }
    };

    socket.onerror = (error) => {
      console.error('[ElevenLabsTTSService] WebSocket Error:', error);
      if (callback) callback(error);
    };

    socket.onclose = (event) => {
      if (event.wasClean) {
        console.info(
          `[ElevenLabsTTSService] WebSocket closed cleanly, code=${event.code}, reason=${event.reason}`
        );
      } else {
        console.warn('[ElevenLabsTTSService] WebSocket connection died');
        if (callback) {
          console.warn('[ElevenLabsTTSService] Calling callback due to connection failure');
          callback(new Error('WebSocket connection died'));
        }
      }
    };
  }

  async addBackgroundNoise(inputPath, outputPath) {
    return new Promise((resolve, reject) => {
      ffmpeg()
        .input(inputPath)
        .inputOptions(['-f mulaw', '-ar 8000', '-ac 1'])
        .input(
          path.join(__dirname, '../../audios/converted_sound_effect_CallCenter2_mulaw_modified.wav')
        )
        .complexFilter('amix=inputs=2:duration=first:dropout_transition=2')
        .audioCodec('pcm_mulaw')
        .outputOptions(['-ar 8000', '-ac 1', '-f mulaw'])
        .on('error', (err) => reject(err))
        .on('end', () => resolve())
        .save(outputPath);
    });
  }

  sendAudioBuffer(ws, streamSid, audioBuffer, callback, text, filename) {
    const base64Audio = audioBuffer.toString('base64');
    console.log(
      '[ElevenLabsTTSService] Sending audio buffer, streamSid:',
      streamSid,
      'size:',
      audioBuffer.length
    );
    let audioDuration = (audioBuffer.length / 8000) * 1000 - 1300;
    if (audioDuration < 0) {
      audioDuration = 400;
    }
    // const audioDuration = (audioBuffer.length / (8000)) * 1000 - 1300;
    const startTime = Date.now();

    const callService = this.dependencies.callService;
    if (callService) {
      const callState = callService.getCallState(streamSid);
      callState.addToAudioTimeline(text, filename, startTime, audioDuration);
    } else {
      console.error('[ElevenLabsTTSService] callService is not defined in dependencies');
    }

    ws.send(
      JSON.stringify({
        event: 'media',
        streamSid,
        media: { payload: base64Audio },
      }),
      (err) => {
        if (err) {
          console.error('[ElevenLabsTTSService] Error sending audio:', err);
        } else {
          console.log('[ElevenLabsTTSService] Audio sent successfully');
        }
      }
    );

    console.log('[ElevenLabsTTSService] Audio buffer sent, duration:', audioDuration);
    setTimeout(() => {
      if (callback) callback();
    }, audioDuration);
  }

  async sendAudio(ws, streamSid, audioPath, callback) {
    fs.readFile(audioPath, (err, data) => {
      if (err) {
        console.error('[ElevenLabsTTSService] Failed to read audio file:', err);
        if (callback) callback(err);
        return;
      }
      const base64Audio = data.toString('base64');
      ws.send(
        JSON.stringify({
          event: 'media',
          streamSid,
          media: { payload: base64Audio },
        })
      );
      const audioDuration = (data.length / (8000 * 2)) * 1000;
      console.log('[ElevenLabsTTSService] Audio sent, duration:', audioDuration);
      setTimeout(() => {
        if (callback) callback();
      }, audioDuration);
    });
  }

  clearVoiceCache(streamSid) {
    if (streamSid && this.voiceCache[streamSid]) {
      delete this.voiceCache[streamSid];
    }
  }
}

module.exports = { TTSService: ElevenLabsTTSService };
