# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.*.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
output.log
log.txt

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# IDEs and editors
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Build output
dist/
build/

# Audio files
audios/

# Cache
.cache/
.npm/

# Temporary files
tmp/
temp/

# Debug
.debug/

# Local development
*.local 

# Claude readme
CLAUDE.md