const mysql = require('mysql2/promise');
const mongoose = require('mongoose');
// const Redis = require('ioredis');
const path = require('path');
const fs = require('fs').promises;

require('dotenv').config(); // 加载 .env 文件

/**
 * Bootstraps the application by initializing databases, Redis, and other resources.
 * @returns {Promise<void>} Resolves when bootstrap is complete, rejects on error
 */
async function bootstrap() {
  try {
    // 1. Initialize MySQL (RDS) for Store Calls Metadata
    const mysqlPool = mysql.createPool({
      host: process.env.MYSQL_HOST || 'localhost',
      user: process.env.MYSQL_USER || 'admin',
      password: process.env.MYSQL_PASSWORD || '',
      database: process.env.MYSQL_DATABASE || 'simz',
      port: 3306, // 默认 MySQL 端口
      connectionLimit: 10, // 连接池限制
    });

    // Test MySQL connection
    const mysqlConnection = await mysqlPool.getConnection();
    await mysqlConnection.ping();
    mysqlConnection.release();
    console.log('[bootstrap] MySQL database connected successfully');

    // 2. Initialize MongoDB (Atlas) for Store Transcript
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('[bootstrap] MongoDB connected successfully');

    // 3. Initialize Redis (for caching)
    // const redis = new Redis({
    //   host: process.env.REDIS_HOST || 'localhost',
    //   port: process.env.REDIS_PORT || 6379,
    //   password: process.env.REDIS_PASSWORD || '',
    // });

    // Test Redis connection
    // await redis.ping();
    console.log('[bootstrap] Redis connected successfully');

    // 4. Verify audios directory exists and is writable
    const audiosDir = path.join(__dirname, 'audios');
    try {
      await fs.access(audiosDir, fs.constants.W_OK);
    } catch (error) {
      await fs.mkdir(audiosDir, { recursive: true });
      console.log('[bootstrap] Created audios directory:', audiosDir);
    }

    // 5. Export connections for use in services
    global.db = {
      mysqlPool,
      mongoose,
      // redis, // Redis
    };

    console.log('[bootstrap] Application bootstrapped successfully');
  } catch (error) {
    console.error('❗[bootstrap] Bootstrap failed:', error);
    throw error;
  }
}

module.exports = { bootstrap };
