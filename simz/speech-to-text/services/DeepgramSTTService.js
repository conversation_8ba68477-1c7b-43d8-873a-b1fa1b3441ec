const { createClient, LiveTranscriptionEvents } = require('@deepgram/sdk');

/**
 * Manages Speech-to-Text functionality using Deepgram.
 */
class DeepgramSTTService {
  constructor(apiKey) {
    console.log(`[DeepgramSTTService] Initializing with API key: ${apiKey.substring(0, 5)}...`);
    this.deepgram = createClient(apiKey);
    this.connections = new Map(); // Map to store Deepgram connections by streamSid
    this.callbacks = new Map(); // Map to store transcript callbacks
    this.vadCallbacks = new Map(); // Map to store VAD event callbacks
    this.dependencies = {};
    this.callService = null; // CallService instance
    this.wsConnections = new Map(); // Map to store WebSocket connections
    this.accumulatedTranscripts = new Map(); // Map to store accumulated transcripts by streamSid
    this.timeoutTimers = new Map(); // Map to store timeout timers by streamSid
  }

  setDependencies(deps) {
    this.dependencies = { ...this.dependencies, ...deps };
    if (deps.callService) {
      console.log('[DeepgramSTTService] CallService dependency successfully set');
      this.callService = deps.callService;
    } else {
      console.log('[DeepgramSTTService] WARNING: CallService dependency NOT set');
    }
  }

  // Use Nova-3 model for better accuracy
  startTranscription(streamSid, onTranscriptCallback, onSpeechStartCallback, ws) {
    const connection = this.deepgram.listen.live({
      model: 'nova-3-medical',
      language: 'en-US',
      encoding: 'mulaw',
      sample_rate: 8000,
      interim_results: true,
      vad_events: true,
      endpointing: 100,
      smart_format: false,
      diarize: false,
      punctuate: true,
      // keyterm:[
      //   "Adult T-cell lymphoma/leukemia",
      //   "Aggressive NK-cell leukemia",
      //   "Anaplastic Large Cell Lymphoma",
      //   "Anaplastic large T/null-cell lymphoma",
      //   "B-Cell Lymphoma",
      //   "B-lymphoblastic leukemia",
      //   "B-lymphoblastic lymphoma",
      //   "B-prolymphocytic leukemia",
      //   "Blastic mantle cell lymphoma",
      //   "Blastic plasmacytoid dendritic cell neoplasm",
      //   "Burkitt lymphoma",
      //   "Central Nervous System Lymphoma",
      //   "Chronic lymphocytic leukemia",
      //   "Composite lymphoma",
      //   "Cutaneous CD4-positive small/medium T-cell lymphoma",
      //   "Cutaneous CD8-positive aggressive epidermotropic cytotoxic T-cell lymphoma",
      //   "Cutaneous Marginal Zone Lymphoma",
      //   "Dendritic/histiocytic neoplasm",
      //   "Diffuse follicular lymphoma",
      //   "Diffuse large B-cell lymphoma",
      //   "Diffuse mantle cell lymphoma",
      //   "Diffuse Mixed Cell Lymphoma",
      //   "EBV-driven lymphoproliferative disorder",
      //   "Enteropathy-type T-cell lymphoma",
      //   "Extranodal marginal zone B-cell Lymphoma",
      //   "Follicular lymphoma",
      //   "Hairy Cell Lymphoma or Leukemia",
      //   "Hepatosplenic T-cell lymphoma",
      //   "Histiocytic Lymphoma",
      //   "Langherns cell histiocytosis",
      //   "Lymphoblastic Lymphoma",
      //   "Lymphoma",
      //   "Lymphomatoid granulomatosis",
      //   "Lymphoplasmacytic lymphoma",
      //   "Malignant lymphoma unclassifiable",
      //   "Mantle cell lymphoma",
      //   "Mast cell disease",
      //   "Mucosa-Associated Lymphoid Tissue Lymphoma",
      //   "Mycosis fungoides",
      //   "Nasal NK-T-cell lymphoma",
      //   "NK-cell granular lymphocytic proliferation",
      //   "Nodal marginal zone B-cell lymphoma",
      //   "Nodular mantle cell lymphoma",
      //   "Non-Hodgkin's Lymphoma",
      //   "Peripheral gamma-delta T-cell lymphoma",
      //   "Peripheral T-cell lymphoma",
      //   "Plasma cell dyscrasia",
      //   "Plasmacytoma",
      //   "Post-transplantation lymphoproliferative disorder",
      //   "Primary Central Nervous System Lymphoma",
      //   "Primary cutaneous CD30-positive lymphoproliferative disorder",
      //   "Sezary syndrome",
      //   "Small lymphocytic lymphoma",
      //   "Splenic diffuse red pulp small B-cell lymphoma",
      //   "Splenic marginal zone B-cell lymphoma",
      //   "Subcutaneous panniculitis-like T-cell lymphoma",
      //   "T-cell granular lymphocytic proliferation",
      //   "T-Cell Lymphoma",
      //   "T-cell lymphoproliferative disorder of childhood, Epstein-Barr virus positive",
      //   "T-lymphoblastic leukemia",
      //   "T-lymphoblastic lymphoma",
      //   "T-prolyrnphocytic leukemia",
      //   "Unclassifieable B-cell lymphoma",
      //   "Waldenstrom Macroglobulinemia",
      //   "Kidney Cancer",
      //   "Liver Cancer",
      //   "Renal Cell Carcinoma",
      //   "Testicular Cancer",
      //   "Ulcerative Colitis",
      //   "Benign",
      //   "Cerebral Meningioma",
      //   "Malignant",
      //   "Meningioma",
      //   "Parkinson's Disease",
      //   "Ovarian",
      //   "Ovarian Cancer",
      //   "Gastrointestinal injury",
      //   "GI",
      //   "GI Injury",
      //   "NEC",
      //   "Necrotizing Enterocolitis",
      //   "SBS",
      //   "Short Bowel Syndrome"
      // ]
    });

    const keepAliveInterval = setInterval(() => {
      connection.keepAlive();
    }, 10 * 1000);

    this.connections.set(streamSid || 'default', connection);
    if (ws) {
      this.wsConnections.set(streamSid || 'default', ws);
    }
    if (onTranscriptCallback) {
      this.callbacks.set(streamSid || 'default', onTranscriptCallback);
    }
    if (onSpeechStartCallback) {
      this.vadCallbacks.set(streamSid || 'default', onSpeechStartCallback);
    }

    connection.on(LiveTranscriptionEvents.Open, () => {
      console.log(
        `[DeepgramSTTService] Deepgram connection open for streamSid: ${streamSid || 'default'}`
      );
    });

    connection.on(LiveTranscriptionEvents.Transcript, (data) => {
      if (data.channel.alternatives[0].transcript) {
        const transcript = data.channel.alternatives[0].transcript.trim();

        // Any transcript data (including non-final transcript) indicates user speech
        if (transcript !== '') {
          // console.log(
          //   `[DeepgramSTTService] Transcript detected: ${transcript}, is_final: ${data.is_final}`
          // );

          // Stop audio playback immediately when any transcript is detected
          if (this.callService) {
            const ws = this.wsConnections.get(streamSid || 'default');
            if (ws) {
              // console.log(
              //   `[DeepgramSTTService] Transcript detected, stopping audio playback for streamSid: ${streamSid || 'default'}`
              // );
              this.callService.stopPlayingAudioAndClearAudioQueue(ws, streamSid || 'default');

              // Record user interruption time
              const currentTime = Date.now();
              const currentState = this.callService.getCallState(streamSid || 'default');
              if (currentState) {
                currentState.updateState({ userSpeechDetectedAt: currentTime });
              }
            }
          }

          // Check if transcript ends with sentence-ending punctuation
          const endsWithPunctuation = /[.!?]$/.test(transcript);

          // Get or initialize accumulated transcript for this stream
          const streamKey = streamSid || 'default';
          let accumulatedTranscript = this.accumulatedTranscripts.get(streamKey) || '';

          if (data.is_final) {
            // Add space between accumulated text and new transcript if needed
            if (accumulatedTranscript && !accumulatedTranscript.endsWith(' ')) {
              accumulatedTranscript += ' ';
            }
            accumulatedTranscript += transcript;

            if (endsWithPunctuation) {
              // Process the complete sentence
              console.log(
                `[DeepgramSTTService] Final Transcript with punctuation:🔵 ${accumulatedTranscript}`
              );
              const callback = this.callbacks.get(streamKey);
              if (callback) callback(accumulatedTranscript);
              // Clear accumulated transcript after processing
              this.accumulatedTranscripts.delete(streamKey);
              // Clear any existing timeout timer
              if (this.timeoutTimers.has(streamKey)) {
                clearTimeout(this.timeoutTimers.get(streamKey));
                this.timeoutTimers.delete(streamKey);
              }
            } else {
              // Store the accumulated transcript and wait for more
              this.accumulatedTranscripts.set(streamKey, accumulatedTranscript);
              console.log(
                `[DeepgramSTTService] Accumulated transcript without punctuation, waiting for more: ${accumulatedTranscript}`
              );

              // Set a timeout to force callback after 3 seconds if no punctuation is received
              if (this.timeoutTimers.has(streamKey)) {
                clearTimeout(this.timeoutTimers.get(streamKey));
              }
              const timeoutTimer = setTimeout(() => {
                console.log(
                  `[DeepgramSTTService] Timeout reached for stream ${streamKey}, forcing callback`
                );
                const callback = this.callbacks.get(streamKey);
                if (callback) callback(accumulatedTranscript);
                this.accumulatedTranscripts.delete(streamKey);
                this.timeoutTimers.delete(streamKey);
              }, 4000);
              this.timeoutTimers.set(streamKey, timeoutTimer);
            }
          }
        }
      }
    });

    // Listen for VAD events to detect when user starts speaking
    connection.on(LiveTranscriptionEvents.Metadata, (data) => {
      if (data.type === 'MetadataEvent' && data.metadata && data.metadata.speech_activity) {
        if (data.metadata.speech_activity.speech_start) {
          console.log(
            `[DeepgramSTTService] Speech activity detected for streamSid: ${streamSid || 'default'}`
          );

          // Stop audio playback immediately when speech is detected
          if (this.callService) {
            console.log(`[DeepgramSTTService] CallService instance found`);
            const ws = this.wsConnections.get(streamSid || 'default');
            if (ws) {
              console.log(
                `[DeepgramSTTService] WebSocket connection found for streamSid: ${streamSid || 'default'}`
              );
              console.log(
                `[DeepgramSTTService] Directly stopping audio playback for streamSid: ${streamSid || 'default'}`
              );
              this.callService.stopPlayingAudioAndClearAudioQueue(ws, streamSid || 'default');

              // Record user interruption time
              const currentTime = Date.now();
              const currentState = this.callService.getCallState(streamSid || 'default');
              if (currentState) {
                currentState.updateState({ userSpeechDetectedAt: currentTime });
              } else {
                console.log(
                  `[DeepgramSTTService] WARNING: Could not get call state for streamSid: ${streamSid || 'default'}`
                );
              }
            } else {
              console.log(
                `[DeepgramSTTService] WARNING: WebSocket connection NOT found for streamSid: ${streamSid || 'default'}`
              );
            }
          } else {
            console.log(`[DeepgramSTTService] WARNING: CallService instance NOT found`);
          }

          // Still notify WebSocketController
          const vadCallback = this.vadCallbacks.get(streamSid || 'default');
          if (vadCallback) {
            console.log(
              `[DeepgramSTTService] Calling VAD callback for streamSid: ${streamSid || 'default'}`
            );
            vadCallback();
          } else {
            console.log(
              `[DeepgramSTTService] WARNING: VAD callback NOT found for streamSid: ${streamSid || 'default'}`
            );
          }
        }
      }
    });

    connection.on(LiveTranscriptionEvents.Close, () => {
      console.log(
        `[DeepgramSTTService] Deepgram connection closed for streamSid: ${streamSid || 'default'}`
      );
      this.connections.delete(streamSid || 'default');
      this.callbacks.delete(streamSid || 'default');
      this.vadCallbacks.delete(streamSid || 'default');
      this.wsConnections.delete(streamSid || 'default');
    });

    connection.on(LiveTranscriptionEvents.Error, (err) => {
      console.error('[DeepgramSTTService] Deepgram error:', err);
    });

    return connection;
  }

  // Send audio data
  sendAudio(streamSid, audioBuffer) {
    const connection = this.connections.get(streamSid || 'default');
    if (connection) {
      connection.send(audioBuffer);
    }
  }

  // Stop transcription
  stopTranscription(streamSid) {
    const connection = this.connections.get(streamSid || 'default');
    if (connection) {
      connection.finish();
      this.connections.delete(streamSid || 'default');
      this.callbacks.delete(streamSid || 'default');
      this.vadCallbacks.delete(streamSid || 'default');
      this.wsConnections.delete(streamSid || 'default');
    }
  }
}

module.exports = { STTService: DeepgramSTTService };
