/**
 * Handles Speech-to-Text related requests and WebSocket integration.
 */
class STTController {
  constructor(wss, sttService, callService, llmController) {
    this.wss = wss; // WebSocket Server instance
    this.sttService = sttService; // STTService instance
    this.callService = callService; // CallService instance for state management
    this.llmController = llmController; // LLMController for transcript handling

    // 设置STTService依赖
    this.sttService.setDependencies({ callService });
  }

  // 启动转录并转发回调
  startTranscription(ws, streamSid, onTranscriptCallback, onSpeechStartCallback) {
    this.sttService.startTranscription(streamSid, onTranscriptCallback, onSpeechStartCallback, ws);
  }

  // 发送音频数据
  sendAudio(streamSid, audioBuffer) {
    this.sttService.sendAudio(streamSid, audioBuffer);
  }

  // 停止转录
  stopTranscription(streamSid) {
    this.sttService.stopTranscription(streamSid);
  }
}

module.exports = { STTController };
