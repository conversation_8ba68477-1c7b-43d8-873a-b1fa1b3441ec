// agents/controllers/AgentController.js
const { AgentService } = require('../services/AgentService');
const { SalesforceService } = require('../../salesforce/services/SalesforceService');
const { agentStatusService } = require('../services/AgentStatusService');
const { UserService } = require('../../auth/services/UserService');

class AgentController {
  constructor(callService) {
    this.callService = callService;
    this.agentService = new AgentService(callService);
    this.agentStatusService = agentStatusService;
    this.userService = new UserService();
  }

  async createAgent(req, res) {
    try {
      const { name, twilioNumber, promptId, promptVersion } = req.body;
      if (!req.user || !req.user.userId) {
        return res.status(401).send('User not authenticated');
      }
      const result = await this.agentService.createAgent({
        name,
        twilioNumber,
        promptId,
        promptVersion,
        userId: req.user.userId,
      });

      // Initialize agent status
      this.agentStatusService.initializeAgent(result.id, {
        name,
        twilioNumber,
        status: 'available',
      });

      res.json(result);
    } catch (error) {
      console.error('[AgentController] Error creating agent:', error);
      res.status(500).send('Failed to create agent');
    }
  }

  async updateAgent(req, res) {
    try {
      const agentId = req.params.agentId;
      const { name, twilioNumber, promptId, promptVersion } = req.body;
      if (!req.user || !req.user.userId) {
        return res.status(401).send('User not authenticated');
      }
      const result = await this.agentService.updateAgent(agentId, {
        name,
        twilioNumber,
        promptId,
        promptVersion,
        userId: req.user.userId,
      });
      res.json(result);
    } catch (error) {
      console.error('[AgentController] Error updating agent:', error);
      res.status(500).send('Failed to update agent');
    }
  }

  async getAllAgents(req, res) {
    try {
      if (!req.user || !req.user.userId) {
        return res.status(401).send('User not authenticated');
      }
      const agents = await this.agentService.getAllAgents();

      // Initialize all agents' status
      this.agentStatusService.initializeAgents(agents);

      res.json(agents);
    } catch (error) {
      console.error('[AgentController] Error getting agents:', error);
      res.status(500).send('Failed to fetch agents');
    }
  }

  async getTwilioNumbers(req, res) {
    try {
      const numbers = await this.agentService.getTwilioNumbers();
      res.json(numbers);
    } catch (error) {
      console.error('[AgentController] Error getting Twilio numbers:', error);
      res.status(500).send('Failed to fetch Twilio numbers');
    }
  }

  async getCompanyAgents(req, res) {
    try {
      const user = await this.userService.getUserById(req.user.userId);
      if (!user || !user.Company_ID) {
        return res.status(404).json({ message: 'User or company not found' });
      }
      const companyId = user.Company_ID;
      const agents = await this.agentService.getAgentsByCompanyId(companyId);
      res.json(agents);
    } catch (error) {
      console.error('[AgentController] Error getting agents by company:', error);
      res.status(500).json({ message: 'Failed to fetch agents by company', error: error.message });
    }
  }

  // Handle outbound call request
  async makeCall(req, res) {
    try {
      const {
        agentId,
        clientName,
        toNumber,
        campaignType,
        promptId,
        promptVersion,
        salesforceId,
        enableAMD = false, // 新增：是否启用 AMD
        amdOptions = {}, // 新增：AMD 配置选项
      } = req.body;
      const userTag = `[user:${toNumber || 'unknown'} ${clientName || 'unknown'}]`;
      if (!req.user || !req.user.userId) {
        return res.status(401).send('User not authenticated');
      }

      const user = await this.userService.getUserById(req.user.userId);
      const companyId = user ? user.Company_ID : null;
      console.log(`${userTag} [AgentController] Received make-call request:`, {
        agentId,
        clientName,
        toNumber,
        campaignType,
        promptId,
        promptVersion,
        enableAMD,
        amdOptions,
      });

      // 如果启用了 AMD，调用 AMD 版本的方法
      if (enableAMD) {
        return await this.makeCallWithAMD(req, res);
      }

      // 原有的普通拨打电话逻辑保持不变
      // Update agent status to 'in-call'
      this.agentStatusService.updateAgentStatus(agentId, 'in-call');

      // Get SF lead info
      let lead = null;
      let leadData = {
        name: clientName || '',
        email: '',
        phone: toNumber || '',
        campaignType: campaignType || '',
      };

      let newLeadId = null;

      if (salesforceId) {
        const salesforceService = new SalesforceService();
        await salesforceService.connect();
        lead = await salesforceService.getLead('Lead', salesforceId);
        leadData = {
          name: lead.Name || clientName || '',
          email: lead.Email || '',
          phone: lead.Phone || lead.Primary_Number__c || toNumber || 'unknown',
          campaignType: lead.Campaign_Type__c || campaignType || 'unknown',
          diagnosis: lead.Diagnosis__c || 'unknown',
          diagnosisDate: lead.Date_Diagnosed_Injury_Date__c || 'unknown',
          personallyPrepareHandleProduct: lead.Personally_prepare_handle_product__c || 'unknown',
          understandFirmWillVerifyDiagnosis:
            lead.Understand_firm_will_verify_claim_dx__c || 'unknown',
          evidenceTypeForYourInjury: lead.Evidence_type_for_your_injury__c || 'unknown',
          firstExposed: lead.When_1st_exposed_used__c || 'unknown',
          lastExposed: lead.When_last_exposed_used__c || 'unknown',
        };
        const leadUserTag = `[user:${leadData.phone || 'unknown'} ${leadData.name || 'unknown'}]`;
        console.log(`${leadUserTag} [AgentController] Loaded Salesforce lead for outbound call.`);
      } else {
        const leadData = {
          LastName: clientName || '',
          Company: 'SimZ AI',
          Phone: toNumber || '',
          Description: 'Outbound call from AI agent',
          Status: 'AI Called',
          LeadSource: 'Phone',
        };
        const salesforceService = new SalesforceService();
        await salesforceService.connect();
        const newLead = await salesforceService.createRecord('Lead', leadData);
        const newLeadUserTag = `[user:${leadData.Phone || 'unknown'} ${leadData.LastName || 'unknown'}]`;
        console.log(`${newLeadUserTag} [AgentController] Created new lead:`, newLead);
        newLeadId = newLead.id;
      }

      let callResult;

      // Set global call data
      if (salesforceId && lead) {
        callResult = await this.agentService.makeCall({
          agentId,
          clientName: lead ? lead.Name || clientName : clientName,
          toNumber: lead ? lead.Phone || lead.Primary_Number__c || toNumber : toNumber,
          campaignType: lead ? lead.Campaign_Type__c || campaignType : campaignType,
          promptId,
          promptVersion,
          salesforceId,
          leadData: salesforceId ? leadData : null,
          companyId,
        });

        this.callService.stageLeadData(callResult.twilioCallSid, {
          agentId: agentId || 'unknown',
          clientName: lead.Name || clientName || '',
          clientEmail: lead.Email || '',
          customerPhone: lead.Phone || lead.Primary_Number__c || toNumber || 'unknown',
          campaignType: lead.Campaign_Type__c || campaignType || 'unknown',
          clientDiagnosis: lead.Diagnosis__c || 'unknown',
          clientDiagnosisDate: lead.Date_Diagnosed_Injury_Date__c || 'unknown',
          clientPersonallyPrepareHandleProduct:
            lead.Personally_prepare_handle_product__c || 'unknown',
          clientUnderstandFirmWillVerifyDiagnosis:
            lead.Understand_firm_will_verify_claim_dx__c || 'unknown',
          clientEvidenceTypeForYourInjury: lead.Evidence_type_for_your_injury__c || 'unknown',
          client1stExposed: lead.When_1st_exposed_used__c || 'unknown',
          clientLastExposed: lead.When_last_exposed_used__c || 'unknown',
          promptId: promptId || 'unknown',
          promptVersion: promptVersion || 'unknown',
          twilioCallSid: callResult.twilioCallSid,
          internalCallID: callResult.internalCallID,
          salesforceId: salesforceId || 'unknown',
        });
      } else {
        callResult = await this.agentService.makeCall({
          agentId,
          clientName: clientName || 'unknown',
          toNumber: toNumber || 'unknown',
          campaignType: campaignType || 'unknown',
          promptId: promptId || 'unknown',
          promptVersion: promptVersion || 'unknown',
          leadData: leadData,
          companyId,
        });

        this.callService.stageLeadData(callResult.twilioCallSid, {
          agentId: agentId || 'unknown',
          clientName: clientName || 'unknown',
          customerPhone: toNumber || 'unknown',
          campaignType: campaignType || 'unknown',
          promptVersion: promptVersion || 'unknown',
          twilioCallSid: callResult.twilioCallSid,
          internalCallID: callResult.internalCallID,
          salesforceId: newLeadId || null,
        });
      }
      console.log(
        `${userTag} [AgentController] Get lead data in staged lead data:`,
        this.callService.getStagedLeadData(callResult.twilioCallSid)
      );

      res.json({
        message: 'Call initiated',
        internalCallID: callResult.internalCallID,
        twilioCallSid: callResult.twilioCallSid,
      });
    } catch (error) {
      const userTag = `[user:${req.body.toNumber || 'unknown'} ${req.body.clientName || 'unknown'}]`;
      console.error(`${userTag} [AgentController] Error making call:`, error);
      res.status(500).send('Failed to make call');
    }
  }

  // Handle outbound call request with AMD
  async makeCallWithAMD(req, res) {
    try {
      const {
        agentId,
        clientName,
        toNumber,
        campaignType,
        promptId,
        promptVersion,
        salesforceId,
        amdOptions = {},
      } = req.body;

      if (!req.user || !req.user.userId) {
        return res.status(401).send('User not authenticated');
      }

      const user = await this.userService.getUserById(req.user.userId);
      const companyId = user ? user.Company_ID : null;

      console.log('[AgentController] Received make-call-with-amd request:', {
        agentId,
        clientName,
        toNumber,
        campaignType,
        promptId,
        promptVersion,
        amdOptions,
      });

      // Update agent status to 'in-call'
      this.agentStatusService.updateAgentStatus(agentId, 'in-call');

      // Get SF lead info (same logic as makeCall)
      let lead = null;
      let leadData = {
        name: clientName || '',
        email: '',
        phone: toNumber || '',
        campaignType: campaignType || '',
      };

      let newLeadId = null;

      if (salesforceId) {
        const salesforceService = new SalesforceService();
        await salesforceService.connect();
        lead = await salesforceService.getLead('Lead', salesforceId);
        leadData = {
          name: lead.Name || clientName || '',
          email: lead.Email || '',
          phone: lead.Phone || lead.Primary_Number__c || toNumber || 'unknown',
          campaignType: lead.Campaign_Type__c || campaignType || 'unknown',
          diagnosis: lead.Diagnosis__c || 'unknown',
          diagnosisDate: lead.Date_Diagnosed_Injury_Date__c || 'unknown',
          personallyPrepareHandleProduct: lead.Personally_prepare_handle_product__c || 'unknown',
          understandFirmWillVerifyDiagnosis:
            lead.Understand_firm_will_verify_claim_dx__c || 'unknown',
          evidenceTypeForYourInjury: lead.Evidence_type_for_your_injury__c || 'unknown',
          firstExposed: lead.When_1st_exposed_used__c || 'unknown',
          lastExposed: lead.When_last_exposed_used__c || 'unknown',
        };
      } else {
        const leadData = {
          LastName: clientName || '',
          Company: 'SimZ AI',
          Phone: toNumber || '',
          Description: 'Outbound call from AI agent with AMD',
          Status: 'AI Called',
          LeadSource: 'Phone',
        };
        const salesforceService = new SalesforceService();
        await salesforceService.connect();
        const newLead = await salesforceService.createRecord('Lead', leadData);
        console.log('[AgentController] Created new lead:', newLead);
        newLeadId = newLead.id;
      }

      let callResult;

      // Set global call data and make call with AMD
      if (salesforceId && lead) {
        callResult = await this.agentService.makeCallWithAMD({
          agentId,
          clientName: lead ? lead.Name || clientName : clientName,
          toNumber: lead ? lead.Phone || lead.Primary_Number__c || toNumber : toNumber,
          campaignType: lead ? lead.Campaign_Type__c || campaignType : campaignType,
          promptId,
          promptVersion,
          salesforceId,
          leadData: salesforceId ? leadData : null,
          amdOptions,
          companyId,
        });

        this.callService.stageLeadData(callResult.twilioCallSid, {
          agentId: agentId || 'unknown',
          clientName: lead.Name || clientName || '',
          clientEmail: lead.Email || '',
          customerPhone: lead.Phone || lead.Primary_Number__c || toNumber || 'unknown',
          campaignType: lead.Campaign_Type__c || campaignType || 'unknown',
          clientDiagnosis: lead.Diagnosis__c || 'unknown',
          clientDiagnosisDate: lead.Date_Diagnosed_Injury_Date__c || 'unknown',
          clientPersonallyPrepareHandleProduct:
            lead.Personally_prepare_handle_product__c || 'unknown',
          clientUnderstandFirmWillVerifyDiagnosis:
            lead.Understand_firm_will_verify_claim_dx__c || 'unknown',
          clientEvidenceTypeForYourInjury: lead.Evidence_type_for_your_injury__c || 'unknown',
          client1stExposed: lead.When_1st_exposed_used__c || 'unknown',
          clientLastExposed: lead.When_last_exposed_used__c || 'unknown',
          promptId: promptId || 'unknown',
          promptVersion: promptVersion || 'unknown',
          twilioCallSid: callResult.twilioCallSid,
          internalCallID: callResult.internalCallID,
          salesforceId: salesforceId || 'unknown',
        });
      } else {
        callResult = await this.agentService.makeCallWithAMD({
          agentId,
          clientName: clientName || 'unknown',
          toNumber: toNumber || 'unknown',
          campaignType: campaignType || 'unknown',
          promptId: promptId || 'unknown',
          promptVersion: promptVersion || 'unknown',
          leadData: leadData,
          amdOptions,
          companyId,
        });
        this.callService.stageLeadData(callResult.twilioCallSid, {
          agentId: agentId || 'unknown',
          clientName: clientName || 'unknown',
          customerPhone: toNumber || 'unknown',
          campaignType: campaignType || 'unknown',
          promptVersion: promptVersion || 'unknown',
          twilioCallSid: callResult.twilioCallSid,
          internalCallID: callResult.internalCallID,
          salesforceId: newLeadId || null,
        });
      }
      console.log(
        '[AgentController] Get lead data in staged lead data:',
        this.callService.getStagedLeadData(callResult.callSid)
      );

      res.json({
        message: 'Call with AMD initiated',
        internalCallID: callResult.internalCallID,
        twilioCallSid: callResult.twilioCallSid,
        amdEnabled: true,
      });
    } catch (error) {
      console.error('[AgentController] Error making call with AMD:', error);
      res.status(500).json({
        error: 'Failed to make call with AMD',
        details: error.message,
      });
    }
  }

  // Handle AMD callback from Twilio
  async handleAMDCallback(req, res) {
    try {
      console.log('[AgentController] AMD callback received:', req.body);

      const { CallSid } = req.body;
      if (!CallSid) {
        return res.status(400).json({ error: 'CallSid is required' });
      }

      // 处理 AMD 结果
      const amdResult = await this.agentService.handleAMDResult(CallSid, req.body);

      console.log('[AgentController] AMD result processed:', amdResult);

      // 如果检测到答录机，更新 agent 状态为可用
      if (!amdResult.shouldContinueCall) {
        // 从全局数据中获取 agentId
        const amdCallData = global.amdCalls && global.amdCalls[CallSid];
        if (amdCallData && amdCallData.agentId) {
          this.agentStatusService.updateAgentStatus(amdCallData.agentId, 'available');
          console.log('[AgentController] Agent status updated to available due to AMD detection');
        }
      }

      res.status(200).json({
        message: 'AMD callback processed',
        result: amdResult,
      });
    } catch (error) {
      console.error('[AgentController] Error handling AMD callback:', error);
      res.status(500).json({
        error: 'Failed to process AMD callback',
        details: error.message,
      });
    }
  }

  // Get AMD call status
  async getAMDCallStatus(req, res) {
    try {
      const { callSid } = req.params;

      if (!callSid) {
        return res.status(400).json({ error: 'CallSid is required' });
      }

      const amdCallData = global.amdCalls && global.amdCalls[callSid];

      if (!amdCallData) {
        return res.status(404).json({ error: 'AMD call data not found' });
      }

      res.json({
        callSid,
        ...amdCallData,
      });
    } catch (error) {
      console.error('[AgentController] Error getting AMD call status:', error);
      res.status(500).json({
        error: 'Failed to get AMD call status',
        details: error.message,
      });
    }
  }
}

module.exports = { AgentController };
