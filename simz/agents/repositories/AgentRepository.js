// agents/repositories/AgentRepository.js
const mysql = require('mysql2/promise');
const config = require('../../config/env');
const { v4: uuidv4 } = require('uuid');

class AgentRepository {
  constructor() {
    this.pool = mysql.createPool({
      host: config.db.host,
      user: config.db.user,
      password: config.db.password,
      database: config.db.database,
    });
  }

  async createAgent({ agentId, name, promptId, promptVersion, twilioNumber, userId }) {
    const id = agentId || uuidv4();
    await this.pool.execute(
      `INSERT INTO Call_Agents (Agent_ID, Prompt_ID, Prompt_Version, Last_Modified_By_User, Agent_Name, Twilio_Number)
       VALUES (?, ?, ?, ?, ?, ?)`,
      [id, promptId, promptVersion, userId, name, twilioNumber]
    );
    return {
      Agent_ID: id,
      Prompt_ID: promptId,
      Prompt_Version: promptVersion,
      Agent_Name: name,
      Twilio_Number: twilioNumber,
    };
  }

  async updateAgent(agentId, { name, promptId, promptVersion, twilioNumber, userId }) {
    await this.pool.execute(
      `UPDATE Call_Agents
       SET Agent_Name = ?, Prompt_ID = ?, Prompt_Version = ?, Twilio_Number = ?, Last_Modified_By_User = ?, Updated_At = NOW()
       WHERE Agent_ID = ?`,
      [name, promptId, promptVersion, twilioNumber, userId, agentId]
    );
    return {
      Agent_ID: agentId,
      Agent_Name: name,
      Prompt_ID: promptId,
      Prompt_Version: promptVersion,
      Twilio_Number: twilioNumber,
    };
  }

  async getAgentById(agentId) {
    const [rows] = await this.pool.execute('SELECT * FROM Call_Agents WHERE Agent_ID = ?', [
      agentId,
    ]);
    return rows[0] || null;
  }

  async getAllAgents() {
    const [rows] = await this.pool.execute(
      `SELECT 
        c.Agent_ID, 
        c.Agent_Name, 
        c.Prompt_ID, 
        c.Prompt_Version,
        c.Twilio_Number, 
        c.Last_Modified_By_User, 
        c.Created_At, 
        c.Updated_At, 
        p.Prompt_Name,
        c.Prompt_Version
      FROM Call_Agents c
      LEFT JOIN Prompt_Store p 
        ON c.Prompt_ID = p.Prompt_ID
      LIMIT 0, 1000`
    );
    return rows;
  }

  async getAgentByPhoneNumber(phoneNumber) {
    const [rows] = await this.pool.execute('SELECT * FROM Call_Agents WHERE Twilio_Number = ?', [
      phoneNumber,
    ]);
    return rows[0] || null;
  }

  async getAgentsByCompanyId(companyId) {
    const [rows] = await this.pool.execute(
      `SELECT 
        c.Agent_ID, 
        c.Agent_Name, 
        c.Prompt_ID, 
        c.Prompt_Version,
        c.Twilio_Number, 
        c.Last_Modified_By_User, 
        c.Created_At, 
        c.Updated_At, 
        p.Prompt_Name,
        c.Prompt_Version
      FROM Call_Agents c
      LEFT JOIN Prompt_Store p 
        ON c.Prompt_ID = p.Prompt_ID
      WHERE p.Company_ID = ?
      LIMIT 0, 1000`,
      [companyId]
    );
    return rows;
  }
}

module.exports = { AgentRepository };
