// agents/services/AgentService.js
const { AgentRepository } = require('../repositories/AgentRepository');
const { PromptService } = require('../../llm-integration/services/PromptService');
const { TwilioService } = require('../../call-management/services/TwilioService');
const twilio = require('twilio');
const apiKeys = require('../../config/api-keys');
const config = require('../../config/env');

class AgentService {
  constructor(callService) {
    this.agentRepository = new AgentRepository();
    this.promptService = new PromptService();
    this.twilioClient = new twilio(apiKeys.twilio.accountSid, apiKeys.twilio.authToken);
    this.twilioService = new TwilioService(this.twilioClient);
    this.callService = callService;
  }

  async createAgent({ name, twilioNumber, promptId, promptVersion, userId }) {
    return await this.agentRepository.createAgent({
      name,
      twilioNumber,
      promptId,
      promptVersion,
      userId,
    });
  }

  async updateAgent(agentId, { name, twilioNumber, promptId, promptVersion, userId }) {
    return await this.agentRepository.updateAgent(agentId, {
      name,
      twilioNumber,
      promptId,
      promptVersion,
      userId,
    });
  }

  async getAllAgents() {
    return await this.agentRepository.getAllAgents();
  }

  async getTwilioNumbers() {
    const numbers = await this.twilioClient.incomingPhoneNumbers.list();
    return numbers.map((num) => ({
      phoneNumber: num.phoneNumber,
      friendlyName: num.friendlyName,
    }));
  }

  async getAgentsByCompanyId(companyId) {
    if (!companyId) throw new Error('companyId is required');
    return await this.agentRepository.getAgentsByCompanyId(companyId);
  }

  // Replace the variables in prompts
  replacePromptVariables(promptContent, leadData) {
    console.log('[AgentService] Replace Prompt Variables, leadData:', leadData);
    if (!leadData) return promptContent;

    let modifiedPrompt = promptContent;

    // Get Current Date
    const today = new Date();
    const todayDate = today.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });

    // Define First and Last Name
    let firstName = '';
    let lastName = '';
    if (leadData.name) {
      const nameParts = leadData.name.split(' ');
      if (nameParts.length >= 1) firstName = nameParts[0];
      if (nameParts.length >= 2) lastName = nameParts.slice(1).join(' ');
    }
    // Define the replacements
    const replacements = {
      '{{full_name}}': leadData.name || '',
      '{{today_date}}': todayDate,
      '{{first_name}}': firstName,
      '{{last_name}}': lastName,
      '{{email}}': leadData.email || '',
      '{{phone_number}}': leadData.phone || '',
    };
    console.log('[AgentService] Prompt replacements:', replacements);
    for (const [placeholder, value] of Object.entries(replacements)) {
      modifiedPrompt = modifiedPrompt.replace(new RegExp(placeholder, 'g'), value);
    }
    return modifiedPrompt;
  }

  // Initiate an outbound call using Twilio
  async makeCall({
    agentId,
    clientName,
    toNumber,
    campaignType,
    promptId,
    promptVersion,
    salesforceId,
    leadData,
    companyId,
  }) {
    try {
      // Get agent details to retrieve the fromNumber
      console.log(
        '[AgentService] Making call, agentId:',
        agentId,
        'with Salesforce ID:',
        salesforceId,
        'with promptId:',
        promptId,
        'with promptVersion:',
        promptVersion
      );
      const agent = await this.agentRepository.getAgentById(agentId);
      if (!agent) {
        throw new Error('Agent not found');
      }
      const fromNumber = agent.Twilio_Number;
      if (!fromNumber) {
        throw new Error('Agent does not have a Twilio number');
      }

      // Generate internalCallID (Date.now() + 5-digit random number)
      const randomNum = Math.floor(10000 + Math.random() * 90000);
      const internalCallID = `call_${Date.now()}_${randomNum}`;

      // Get Prompt content
      let promptContent = await this.promptService.getPromptContent(promptId, promptVersion);
      // Replace Prompt Varialbes
      promptContent = this.replacePromptVariables(promptContent, leadData);
      // console.log(
      //   '[AgentService] Processed Prompt content with variables replaced, promptContent:',
      //   promptContent
      // );

      console.log('[AgentService] Initiating call:', {
        internalCallID,
        fromNumber,
        toNumber,
        clientName,
        campaignType,
        promptId,
        promptVersion,
      });

      // Initiate the call with Twilio
      const call = await this.twilioClient.calls.create({
        from: fromNumber,
        to: toNumber,
        url: `${config.server.url}/out-bound`,
        record: true,
        recordingStatusCallback: `${config.server.url}/api/call-recordings/callback`,
        recordingStatusCallbackEvent: ['completed'],
        recordingStatusCallbackMethod: 'POST',
        statusCallback: `${config.server.url}/agent-call-status`,
        statusCallbackEvent: ['initiated', 'ringing', 'answered', 'no-answer', 'failed', 'busy', 'canceled', 'completed'],
        statusCallbackMethod: 'POST',
      });

      const twilioCallSid = call.sid;
      // Store initial call information in the Calls table
      console.log(
        '[AgentService] Call initiated, Twilio Call SID:',
        twilioCallSid,
        'Internal Call ID:',
        internalCallID
      );

      // Store initial call information in the Calls table
      await this.agentRepository.pool.execute(
        `INSERT INTO Calls (Call_ID, Call_DateTime, Call_Direction, Client_Name, Associated_Campaign, Caller_Number, Agent_ID, To_Number, Twilio_Call_SID, Company_ID)
         VALUES (?, NOW(), ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          internalCallID,
          'Outbound',
          clientName,
          campaignType,
          fromNumber,
          agentId,
          toNumber,
          twilioCallSid,
          companyId,
        ]
      );

      // Store Prompt content using the call service staging method
      const promptData = {
        internalCallID,
        promptContent,
        promptId,
        promptVersion,
        agentId,
        clientName,
        campaignType,
        fromNumber,
        toNumber,
        twilioCallSid, // Store twilioCallSid for reference
        callerNumber: toNumber,
      };
      this.callService.stagePromptData(twilioCallSid, promptData);
      console.log('[AgentService] Staged prompt data for Twilio Call SID:', twilioCallSid);

      return {
        internalCallID,
        twilioCallSid,
        message: 'Call initiated successfully',
      };
    } catch (error) {
      console.error('[AgentService] Error making call:', error);
      throw error;
    }
  }

  // Initiate an outbound call using Twilio with AMD
  async makeCallWithAMD({
    agentId,
    clientName,
    toNumber,
    campaignType,
    promptId,
    promptVersion,
    salesforceId,
    leadData,
    amdOptions = {},
    companyId,
  }) {
    try {
      // Get agent details to retrieve the fromNumber
      console.log(
        '[AgentService] Making call with AMD, agentId:',
        agentId,
        'with Salesforce ID:',
        salesforceId,
        'with promptId:',
        promptId,
        'with promptVersion:',
        promptVersion
      );
      const agent = await this.agentRepository.getAgentById(agentId);
      if (!agent) {
        throw new Error('Agent not found');
      }
      const fromNumber = agent.Twilio_Number;
      if (!fromNumber) {
        throw new Error('Agent does not have a Twilio number');
      }

      // Generate internalCallID (Date.now() + 5-digit random number)
      const randomNum = Math.floor(10000 + Math.random() * 90000);
      const internalCallID = `call_${Date.now()}_${randomNum}`;

      // Get Prompt content
      let promptContent = await this.promptService.getPromptContent(promptId, promptVersion);
      // Replace Prompt Variables
      promptContent = this.replacePromptVariables(promptContent, leadData);

      console.log('[AgentService] Initiating call with AMD:', {
        internalCallID,
        fromNumber,
        toNumber,
        clientName,
        campaignType,
        promptId,
        promptVersion,
        amdEnabled: true,
      });

      // 准备通话参数
      const callParams = {
        from: fromNumber,
        to: toNumber,
        url: `${config.server.url}/out-bound`,
        statusCallback: `${config.server.url}/agent-call-status`,
        amdCallback: `${config.server.url}/api/amd-callback`,
        record: true,
        recordingStatusCallback: `${config.server.url}/api/call-recordings/callback`,
        recordingStatusCallbackEvent: ['completed'],
        recordingStatusCallbackMethod: 'POST',
        statusCallbackEvent: ['initiated', 'ringing', 'answered', 'completed'],
        statusCallbackMethod: 'POST',
      };

      // 使用 TwilioService 创建带 AMD 的通话
      const call = await this.twilioService.makeCallWithAMD(callParams, amdOptions);
      const twilioCallSid = call.sid;

      console.log(
        '[AgentService] Call with AMD initiated, Twilio Call SID:',
        twilioCallSid,
        'Internal Call ID:',
        internalCallID
      );

      // Store initial call information in the Calls table
      await this.agentRepository.pool.execute(
        `INSERT INTO Calls (Call_ID, Call_DateTime, Call_Direction, Client_Name, Associated_Campaign, Caller_Number, Agent_ID, To_Number, Twilio_Call_SID, Company_ID)
         VALUES (?, NOW(), ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          internalCallID,
          'Outbound',
          clientName,
          campaignType,
          fromNumber,
          agentId,
          toNumber,
          twilioCallSid,
          companyId,
        ]
      );

      // Store Prompt content using the call service staging method
      const promptData = {
        internalCallID,
        promptContent,
        promptId,
        promptVersion,
        agentId,
        clientName,
        campaignType,
        fromNumber,
        toNumber,
        twilioCallSid,
        amdEnabled: true, // 标记这个通话启用了 AMD
        callerNumber: toNumber,
      };
      this.callService.stagePromptData(twilioCallSid, promptData);

      // 存储 AMD 相关信息到全局变量
      if (!global.amdCalls) {
        global.amdCalls = {};
      }
      global.amdCalls[twilioCallSid] = {
        internalCallID,
        agentId,
        clientName,
        toNumber,
        campaignType,
        timestamp: new Date().toISOString(),
        amdResult: null, // 将在 AMD 回调中更新
      };

      console.log('[AgentService] Staged prompt data for Twilio Call SID:', twilioCallSid);

      return {
        internalCallID,
        twilioCallSid,
        message: 'Call with AMD initiated successfully',
        amdEnabled: true,
      };
    } catch (error) {
      console.error('[AgentService] Error making call with AMD:', error);
      throw error;
    }
  }

  /**
   * 处理 AMD 回调结果
   * @param {string} callSid - Twilio Call SID
   * @param {Object} amdResult - AMD 结果
   * @returns {Object} 处理结果
   */
  async handleAMDResult(callSid, amdResult) {
    try {
      console.log('[AgentService] Processing AMD result for call:', callSid, amdResult);

      // 使用 TwilioService 处理 AMD 结果
      const processedResult = this.twilioService.processAMDResult(amdResult);

      // 更新全局 AMD 通话数据
      if (global.amdCalls && global.amdCalls[callSid]) {
        global.amdCalls[callSid].amdResult = processedResult;
      }

      // 如果检测到答录机或传真机，挂断通话
      if (!processedResult.shouldContinueCall) {
        console.log('[AgentService] AMD detected machine/fax, hanging up call:', callSid);
        await this.twilioService.hangupCall(callSid);

        // 更新数据库中的通话状态
        await this.agentRepository.pool.execute(
          `UPDATE Calls SET Call_Status = ?, AMD_Result = ? WHERE Twilio_Call_SID = ?`,
          ['AMD_Terminated', processedResult.answeredBy, callSid]
        );
      }

      return processedResult;
    } catch (error) {
      console.error('[AgentService] Error handling AMD result:', error);
      throw error;
    }
  }
}

module.exports = { AgentService };
