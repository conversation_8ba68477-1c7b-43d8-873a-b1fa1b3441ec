// llm-integration/services/GrokFunctionCallingService.js
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const https = require('https');
const twilio = require('twilio');
const nodemailer = require('nodemailer');
const jsforce = require('jsforce');
const VoiceResponse = require('twilio').twiml.VoiceResponse;
const env = require('../../config/env');
require('dotenv').config();
const sgMail = require('@sendgrid/mail');
const { CampaignService } = require('./CampaignService');

sgMail.setApiKey(env.sendgrid.apiKey);

class GrokFunctionCallingService {
  constructor() {
    // Salesforce connection
    this.conn = new jsforce.Connection({
      loginUrl: 'https://thelegalleads.my.salesforce.com',
    });

    // Twilio客户端初始化
    this.twilioClient = null;
    this.VoiceResponse = VoiceResponse;
    this.initTwilioClient();

    // Other API keys and configurations
    this.GOOGLE_PLACES_API_KEY = process.env.GOOGLE_PLACES_API_KEY;

    // Initialize Campaign Service
    this.campaignService = new CampaignService();

    // Product data loading
    this.loadProductData();

    // Define tool function mapping
    this.toolFunctions = {
      WarmTransferToInHouseAgent: this.warmTransferToInHouseAgent.bind(this),
      // SendConfirmation: this.sendConfirmation.bind(this),
      // sendPayment: this.sendPayment.bind(this),
      collectDiagnosisAndCreateCampaignQueue:
        this.collectDiagnosisAndCreateCampaignQueue.bind(this),
      switchToNextCampaign: this.switchToNextCampaign.bind(this),
      hangUp: this.hangUp.bind(this),
    };

    console.log(
      '[GrokFunctionCallingService] Initialized with tools:',
      Object.keys(this.toolFunctions)
    );
  }

  // Initialize Twilio client
  initTwilioClient() {
    try {
      const accountSid = process.env.TWILIO_ACCOUNT_SID;
      const authToken = process.env.TWILIO_AUTH_TOKEN;

      if (accountSid && authToken) {
        this.twilioClient = twilio(accountSid, authToken);
        console.log('[GrokFunctionCallingService] Twilio client initialized successfully');
      } else {
        console.warn(
          '[GrokFunctionCallingService] Twilio credentials not found, client not initialized'
        );
      }
    } catch (error) {
      console.error('[GrokFunctionCallingService] Error initializing Twilio client:', error);
    }
  }

  // Load product data
  loadProductData() {
    // Cancer-related product data
    this.cancerProducts = [
      {
        name: 'Talcum Baby Powder',
        cancers: [
          'Ovarian Cancer',
          'Primary Peritoneal Cancer',
          'Fallopian Tube Cancer',
          'Cancer in Ovaries',
          'Ovarian',
        ],
        startDate: '2020-01',
        endDate: '2025-07',
      },
      {
        name: "Hair Relaxers from L'Oreal",
        cancers: [
          'Uterine Cancer',
          'Endometrial Cancer',
          'Ovarian Cancer',
          'Cancer in Ovaries',
          'Ovarian',
          'Endometrial',
        ],
        startDate: '2010-01',
        endDate: '2025-07',
      },
      {
        name: 'Aqueous Film Forming Foam (A Triple F)',
        cancers: [
          'Kidney Cancer',
          'Thyroid Cancer',
          'Testicular Cancer',
          'Ulcerative Colitis',
          'Liver Cancer',
          'Liver',
          'Kidney',
        ],
        startDate: '1960-01',
        endDate: '2025-07',
      },
      {
        name: 'Roundup Weed Killer',
        cancers: [
          "Non-Hodgkin's Lymphoma",
          'Leukemia',
          'CLL',
          'Chronic Lymphocytic Leukemia',
          'Lymphoma',
          'Lymphoma Cancer',
        ],
        startDate: '2004-01',
        endDate: '2025-07',
      },
      {
        name: 'Contaminated Water at Camp Lejeune',
        cancers: [
          'Kidney Cancer',
          'Liver Cancer',
          "Non-Hodgkin's Lymphoma",
          'Leukemia',
          'Bladder Cancer',
          'Multiple Myeloma',
          "Parkinson's Disease",
          'Kidney/Renal Disease',
          'Kidney/Renal Failure',
          'Systemic Sclerosis/Systemic Scleroderma',
          'Lymphoma',
          'Kidney',
          'Lymphoma Cancer',
        ],
        startDate: '1953-08',
        endDate: '2025-07',
      },
    ];

    // Medical product data
    this.medicalProducts = [
      {
        name: 'Paraquat',
        productUsed: ['Paraquat'],
        complications: [
          "Parkinson's Disease",
          'Tremors',
          'Rigidity',
          'Balance Impairment',
          'Bradykinesia',
          'Difficulty speaking',
          'Reduced facial expression',
          'Drooling',
          'Paralysis',
          'Small Handwriting',
          'Decreased ROM',
          'Sleep Disorders',
          'Spasms',
          'Difficulty Swallowing',
        ],
        startDate: '1964-01',
        endDate: '2025-07',
      },
      {
        name: 'Semaglutide',
        productUsed: ['Ozempic', 'Wegovy'],
        complications: [
          'Persistent vomiting for 4+ weeks',
          'Gastric Injury',
          'Ileus',
          'Gastroparesis',
          'Pulmonary Aspiration',
          'Deep Vein Thrombosis',
        ],
        startDate: '2018-01',
        endDate: '2025-07',
      },
      {
        name: 'Catheter Port',
        productUsed: ['Catheter'],
        complications: [
          'Blood clots',
          'Cardiac arrhythmia',
          'Cardiac punctures',
          'Cardiac/pericardial tamponade',
          'Death',
          'Migration due to breakage',
          'Hemorrhage',
          'Leakage at port site',
          'Laceration to blood vessels',
          'Necrosis',
          'Pulmonary embolism',
          'Severe pain around port/catheter',
        ],
        startDate: '2010-01',
        endDate: '2025-07',
      },
      {
        name: 'Hernia Mesh',
        productUsed: ['Hernia Mesh'],
        complications: [
          'With a lot of pain',
          'lot of pain',
          'Abdominal pain',
          'Adhesion',
          'Pain and movement out of place',
          'Bowel perforation',
          'Edge of mesh curled',
          'Infection',
          'Hernia recurrence',
          'Intestinal blockage',
          'Mesh migration',
          'Mesh contraction',
          'Intestinal fistula',
          'Surgery to remove mesh',
          'Death',
          'Open wound',
          'Mesh ring broken',
          'Mesh balled up',
        ],
        startDate: '2010-01',
        endDate: '2025-09',
      },
    ];

    // State timezone data
    this.stateToTimeZone = {
      Alabama: 'America/Chicago',
      Alaska: 'America/Anchorage',
      Arizona: 'America/Phoenix',
      Arkansas: 'America/Chicago',
      California: 'America/Los_Angeles',
      Colorado: 'America/Denver',
      Connecticut: 'America/New_York',
      Delaware: 'America/New_York',
      Florida: 'America/New_York',
      Georgia: 'America/New_York',
      Hawaii: 'Pacific/Honolulu',
      Idaho: 'America/Boise',
      Illinois: 'America/Chicago',
      Indiana: 'America/Indiana/Indianapolis',
      Iowa: 'America/Chicago',
      Kansas: 'America/Chicago',
      Kentucky: 'America/New_York',
      Louisiana: 'America/Chicago',
      Maine: 'America/New_York',
      Maryland: 'America/New_York',
      Massachusetts: 'America/New_York',
      Michigan: 'America/Detroit',
      Minnesota: 'America/Chicago',
      Mississippi: 'America/Chicago',
      Missouri: 'America/Chicago',
      Montana: 'America/Denver',
      Nebraska: 'America/Chicago',
      Nevada: 'America/Los_Angeles',
      'New Hampshire': 'America/New_York',
      'New Jersey': 'America/New_York',
      'New Mexico': 'America/Denver',
      'New York': 'America/New_York',
      'North Carolina': 'America/New_York',
      'North Dakota': 'America/Chicago',
      Ohio: 'America/New_York',
      Oklahoma: 'America/Chicago',
      Oregon: 'America/Los_Angeles',
      Pennsylvania: 'America/New_York',
      'Rhode Island': 'America/New_York',
      'South Carolina': 'America/New_York',
      'South Dakota': 'America/Chicago',
      Tennessee: 'America/Chicago',
      Texas: 'America/Chicago',
      Utah: 'America/Denver',
      Vermont: 'America/New_York',
      Virginia: 'America/New_York',
      Washington: 'America/Los_Angeles',
      'West Virginia': 'America/New_York',
      Wisconsin: 'America/Chicago',
      Wyoming: 'America/Denver',
    };
  }

  // Get tool definitions, provide to Grok API
  getToolDefinitions() {
    return [
      {
        type: 'function',
        function: {
          name: 'hangUp',
          description: 'Hang up the current customer call',
          parameters: {
            type: 'object',
            properties: {
              reason: {
                type: 'string',
                description: 'The reason for hanging up the call',
              },
            },
            required: ['reason'],
          },
        },
      },
      {
        type: 'function',
        function: {
          name: 'collectDiagnosisAndCreateCampaignQueue',
          description:
            "Collect user's cancer diagnosis information and create a queue of matching campaigns sorted by value",
          parameters: {
            type: 'object',
            properties: {
              cancerTypes: {
                type: 'array',
                items: {
                  type: 'string',
                },
                description: 'List of cancer or disease types diagnosed',
              },
              diagnosisDate: {
                type: 'array',
                items: {
                  type: 'string',
                },
                description: 'The date of cancer diagnosis in YYYY-MM format',
              },
            },
            required: ['cancerTypes', 'diagnosisDate'],
          },
        },
      },
      {
        type: 'function',
        function: {
          name: 'switchToNextCampaign',
          description:
            "MANDATORY: Must be called immediately when user does not qualify for current campaign. This function MUST be triggered when saying phrases like Lets check here if we have any other campaigns or when user fails any qualification criteria.",
          parameters: {
            type: 'object',
            properties: {
              reason: {
                type: 'string',
                description: "The reason why the user doesn't qualify for the current campaign",
              },
            },
            required: ['reason'],
          },
        },
      },

      {
        type: 'function',
        function: {
          name: 'WarmTransferToInHouseAgent',
          description:
            "Transfer the current customer call to a human agent using Twilio's Conference API for warm handoff",
          parameters: {
            type: 'object',
            properties: {
              customerPhone: {
                type: 'string',
                description: "Customer's phone number in E.164 format (e.g., +1234567890)",
              },
              customerName: {
                type: 'string',
                description: "Customer's full name for greeting purposes",
              },
              customerEmail: {
                type: 'string',
                description: "Customer's email address if provided, otherwise, leave it blank",
              },
              qualificationInfo: {
                type: 'string',
                description: 'Detailed qualification information to share with agent',
              },
            },
            required: ['customerPhone', 'customerName', 'qualificationInfo'],
          },
        },
      },
      /*
      {
        type: 'function',
        function: {
          name: 'SendConfirmation',
          description: 'Send a confirmation message to the customer via SMS or email',
          parameters: {
            type: 'object',
            properties: {
              sendMethod: {
                type: 'string',
                description: "The method to send the confirmation. e.g. 'SMS', 'Email', 'Both'",
                enum: ['SMS', 'Email', 'Both'],
              },
              bookingType: {
                type: 'string',
                description: "The type of booking. e.g. 'Restaurant', 'Hotel'",
              },
              phoneNumber: {
                type: 'string',
                description: "Customer's phone number in E.164 format (e.g., +1234567890)",
              },
              emailAddress: {
                type: 'string',
                description: "Customer's email address",
              },
              confirmationContent: {
                type: 'string',
                description: 'The content of the confirmation message',
              },
              reservationDate: {
                type: 'string',
                description: 'The date of the reservation',
              },
              reservationTime: {
                type: 'string',
                description: 'The time of the reservation',
              },
              numberOfGuests: {
                type: 'number',
                description: 'The number of guests for the reservation for the restaurant or hotel',
              },
              restaurantName: {
                type: 'string',
                description: 'The name of the restaurant',
              },
              restaurantAddress: {
                type: 'string',
                description: 'The address of the restaurant',
              },
              restaurantPhone: {
                type: 'string',
                description: 'The phone number of the restaurant',
              },
              guestName: {
                type: 'string',
                description: 'The name of the guest',
              },
              roomType: {
                type: 'string',
                description:
                  "The type of room. eg. 'Deluxe Suite', 'King Room', 'Double Room', 'Ocean View Room', 'Standard Room'",
              },
              checkInDate: {
                type: 'string',
                description: 'The check-in date',
              },
              checkOutDate: {
                type: 'string',
                description: 'The check-out date',
              },
              numberOfNights: {
                type: 'number',
                description: 'The number of nights',
              },
            },
            required: ['sendMethod', 'confirmationContent'],
          },
        },
      },
      {
        type: 'function',
        function: {
          name: 'sendPayment',
          description: 'Create a new payment order and send a payment link via SMS',
          parameters: {
            type: 'object',
            properties: {
              customerName: {
                type: 'string',
                description: 'The full name of the customer',
              },
              service: {
                type: 'string',
                description:
                  "The name of the product or service being purchased, e.g. 'SimZ Steakhouse', 'SimZ Hotel For 2 Nights', 'SimZ Restaurant'",
              },
              price: {
                type: 'number',
                description: 'The price of the service before tax',
              },
              tax: {
                type: 'number',
                description: 'The tax amount for the service',
              },
              totalAmount: {
                type: 'number',
                description: 'The total price including tax',
              },
              phoneNumber: {
                type: 'string',
                description: 'The phone number of the customer in E.164 format (e.g., +1234567890)',
              },
            },
            required: ['customerName', 'service', 'price', 'tax', 'totalAmount', 'phoneNumber'],
          },
        },
      },
      */
    ];
  }

  /**
   * Hang up the current customer call
   * @param {Object} args - The arguments for the hangUp function
   * @param {string} args.reason - The reason for hanging up the call
   * @param {string} callSid - The Twilio Call SID
   * @param {Object} callState - The current Call State
   * @returns {Promise<Object>} - A promise that resolves to an object containing the success status and message
   */
  async hangUp(args, callSid, callState) {
    try {
      const { reason } = args;
      console.log('[GrokFunctionCallingService] Hang up: ', reason);
      console.log(
        '[GrokFunctionCallingService] Hang up the current customer call promptData:',
        callState.getState('promptData')
      );

      if (!callSid) {
        return {
          success: false,
          message: "Can not get the current call's Call SID",
        };
      }

      if (!this.twilioClient) {
        return {
          success: false,
          message: 'Twilio client is not initialized',
        };
      }

      // 创建Twiml响应
      const twiml = new this.VoiceResponse();
      twiml.say(
        { voice: 'Google.en-US-Chirp3-HD-Aoede', language: 'en-US' },
        `Thank you for calling.`
      );
      twiml.hangup();

      // 更新通话状态
      await this.twilioClient.calls(callSid).update({ twiml: twiml.toString() });

      return {
        success: true,
        message: 'Call has been successfully hung up',
        callSid: callSid,
      };
    } catch (error) {
      console.error('[GrokFunctionCallingService] Error hanging up the call:', error);
      return {
        success: false,
        message: `Failed to hang up the call: ${error.message}`,
      };
    }
  }

  // New function to collect diagnosis and create campaign queue
  async collectDiagnosisAndCreateCampaignQueue(args, callSid) {
    try {
      const { cancerTypes, diagnosisDate } = args;
      console.log(
        `[GrokFunctionCallingService] Collecting diagnosis info: ${cancerTypes.join(', ')}, ${diagnosisDate.join(',')}`
      );

      // 确保cancerTypes和diagnosisDate都是数组
      const cancerTypesArray = Array.isArray(cancerTypes) ? cancerTypes : [cancerTypes];
      const diagnosisDateArray = Array.isArray(diagnosisDate) ? diagnosisDate : [diagnosisDate];

      // 验证数组长度是否匹配
      if (cancerTypesArray.length !== diagnosisDateArray.length) {
        return {
          success: false,
          message: 'Cancer types and diagnosis dates arrays must have the same length',
          queue: [],
        };
      }

      // 验证每个日期格式
      const dateRegex = /^\d{4}-\d{2}$/;
      for (let i = 0; i < diagnosisDateArray.length; i++) {
        if (!dateRegex.test(diagnosisDateArray[i])) {
          return {
            success: false,
            message: `Invalid date format for ${cancerTypesArray[i]}. Expected YYYY-MM`,
            queue: [],
          };
        }
      }

      // Use Campaign Service to find matching campaigns
      const matchingCampaigns = await this.campaignService.createCampaignQueue(
        cancerTypesArray,
        diagnosisDateArray
      );

      // Create queue structure
      const campaignQueue = matchingCampaigns.map((campaign) => {
        // 找出用户确诊的疾病中与当前campaign相关的疾病，不区分大小写
        const clientDiseaseRelate = cancerTypesArray.filter((disease) =>
          campaign.cancerTypes.some(
            (campaignDisease) => campaignDisease.toLowerCase() === disease.toLowerCase()
          )
        );

        return {
          campaignId: campaign.Campaign_ID,
          promptId: campaign.Prompt_ID,
          value: campaign.Value,
          name: campaign.Name,
          matchedCancerTypes: campaign.cancerTypes || [],
          clientDiseaseRelate: clientDiseaseRelate,
          matchedDiagnosisDate: campaign.matchedDiagnosisDate, // 添加匹配的诊断日期
        };
      });

      // Add default campaigns to the queue
      // Hernia Mesh/Port Catheter
      const herniaMeshCampaign = {
        campaignId: 'camp_0009',
        promptId: 'd80e25f7-1bb4-4d48-a22f-f23f076e77f5',
        value: 1,
        name: 'Port Catheter',
        matchedCancerTypes: ['Default'],
        clientDiseaseRelate: [],
        matchedDiagnosisDate: null,
      };

      // MVA
      const mvaCampaign = {
        campaignId: 'camp_0006',
        promptId: '464b12f7-46b2-4b1a-86e9-a8a08d250640',
        value: 0,
        name: 'MVA',
        matchedCancerTypes: ['Default'],
        clientDiseaseRelate: [],
        matchedDiagnosisDate: null,
      };

      campaignQueue.push(herniaMeshCampaign);
      campaignQueue.push(mvaCampaign);

      // Create campaign queue in global state
      if (!global.campaignQueues) {
        global.campaignQueues = {};
      }

      // Get current call SID
      global.campaignQueues[callSid] = {
        queue: campaignQueue,
        currentIndex: 0,
        diagnosisInfo: {
          cancerTypes: cancerTypesArray,
          diagnosisDate: diagnosisDateArray,
        },
      };

      console.log(
        `[GrokFunctionCallingService] Created campaign queue for ${callSid}:`,
        campaignQueue.map((c) => `${c.name} (${c.campaignId})`).join(', ')
      );

      return {
        success: true,
        message: `Found ${campaignQueue.length} matching campaigns for ${cancerTypesArray.join(', ')}`,
        queue: campaignQueue,
      };
    } catch (error) {
      console.error('[GrokFunctionCallingService] Error creating campaign queue:', error);
      return {
        success: false,
        message: error.message,
        queue: [],
      };
    }
  }

  // New function to switch to next campaign
  async switchToNextCampaign(args, callSid, callState) {
    try {
      const { reason } = args;
      if (!callSid) {
        console.warn(`[GrokFunctionCallingService] Can not get the current call's Call SID`);
        return {
          success: false,
          message: "Can not get the current call's Call SID",
          nextCampaign: null,
          systemPromptChanged: false,
        };
      }

      if (!callState) {
        console.warn(`[GrokFunctionCallingService] No call state found for call ${callSid}`);
        return {
          success: false,
          message: 'No campaign queue found for this call',
          nextCampaign: null,
          systemPromptChanged: false,
        };
      }

      if (!global.campaignQueues || !global.campaignQueues[callSid]) {
        console.warn(`[GrokFunctionCallingService] No campaign queue found for call ${callSid}`);
        return {
          success: false,
          message: 'No campaign queue found for this call',
          nextCampaign: null,
          systemPromptChanged: false,
        };
      }

      const campaignState = global.campaignQueues[callSid];
      const { queue, currentIndex } = campaignState;

      // Check if there are more campaigns in the queue
      if (currentIndex >= queue.length - 1) {
        console.log(`[GrokFunctionCallingService] No more campaigns in queue for ${callSid}`);
        return {
          success: false,
          message: 'No more campaigns available in queue',
          nextCampaign: null,
          systemPromptChanged: false,
        };
      }

      // Move to next campaign
      campaignState.currentIndex += 1;
      const nextCampaign = queue[campaignState.currentIndex];

      // Get prompt content for the next campaign
      try {
        const campaignData = await this.campaignService.getCampaignPrompt(nextCampaign.campaignId);
        const promptContent = campaignData.promptContent;
        const promptData = callState.getState('promptData');
        promptData.promptContent = promptContent;

        // Update promptData
        callState.updateState('promptData', promptData);

        console.log(
          `[GrokFunctionCallingService] Switched to next campaign for ${callSid}: ${nextCampaign.name}`
        );
        console.log(`[GrokFunctionCallingService] Reason for switch: ${reason}`);

        return {
          success: true,
          message: `Switched to campaign: ${nextCampaign.name}`,
          nextCampaign,
          systemPromptChanged: true,
        };
      } catch (error) {
        console.error(
          `[GrokFunctionCallingService] Error getting prompt for campaign ${nextCampaign.campaignId}:`,
          error
        );

        // Still increment the index but return error
        return {
          success: false,
          message: `Error getting prompt: ${error.message}`,
          nextCampaign,
          systemPromptChanged: false,
        };
      }
    } catch (error) {
      console.error('[GrokFunctionCallingService] Error switching campaign:', error);
      return {
        success: false,
        message: error.message,
        nextCampaign: null,
        systemPromptChanged: false,
      };
    }
  }

  // Entry method for processing tool calls
  async processToolCall(toolCall, callState) {
    try {
      const functionName = toolCall.function.name;
      const args = JSON.parse(toolCall.function.arguments);
      const callSid = callState.getState('currentCallSid');

      console.log(`[GrokFunctionCallingService] Processing function call: ${functionName}`);
      console.log(`[GrokFunctionCallingService] Arguments:`, args);

      if (this.toolFunctions[functionName]) {
        const output = await this.toolFunctions[functionName](args, callSid, callState);
        return {
          tool_call_id: toolCall.id,
          output: JSON.stringify(output),
        };
      } else {
        console.error(`[GrokFunctionCallingService] Unknown function: ${functionName}`);
        return {
          tool_call_id: toolCall.id,
          output: JSON.stringify({
            error: `Unknown function: ${functionName}`,
            success: false,
          }),
        };
      }
    } catch (error) {
      console.error(`[GrokFunctionCallingService] Error processing tool call:`, error);
      return {
        tool_call_id: toolCall.id,
        output: JSON.stringify({
          error: error.message,
          success: false,
        }),
      };
    }
  }

  // Warm Transfer
  async warmTransferToInHouseAgent(args, callSid, callState) {
    if (!callSid) {
      console.error('[GrokFunctionCallingService > WarmTransfer] Missing callSid');
      return {
        success: false,
        message: "Can't get current call's CallSid",
        status: 'failed',
      };
    }
    console.log('[GrokFunctionCallingService > WarmTransfer] Get current call"s CallSid:', callSid);

    // 获取当前通话的 WebSocket 连接
    if (callState && callState.getState('ws')) {
      const ws = callState.getState('ws');
      if (ws.readyState === 1) {
        // 设置转接进行中标志
        callState.updateState({ transferInProgress: true });

        // 发送 stop 事件到 WebSocket 服务器
        const stopEvent = {
          event: 'stop',
          streamSid: callState.getState('streamSid') || 'default',
          start: {
            callSid: callSid,
          },
        };
        ws.send(JSON.stringify(stopEvent));
        console.log(
          '[GrokFunctionCallingService > WarmTransfer] Sent stop event to WebSocket server'
        );
      }
    }

    const { customerPhone, customerName, customerEmail, qualificationInfo } = args;

    const leadData = callState.getState('leadData');

    let phonePassToSalesforce = leadData.customerPhone ? leadData.customerPhone : customerPhone;

    if (!this.twilioClient) {
      return {
        success: false,
        message: 'Twilio client is not initialized',
        status: 'failed',
      };
    }

    let leadResult;
    try {
      // 1. Create conference
      const conference = await this.createConference(customerName);

      // 2. Move customer to conference
      await this.moveCustomerToConference(callSid, callState, conference.conferenceName);

      // 3. Find available agent
      const agent = await this.findAvailableAgent('general', 'medium');

      // 4. Call agent and provide customer information
      const agentCall = await this.callAgent({
        agentPhone: agent.agentPhone,
        customerInfo: {
          name: customerName,
          phone: phonePassToSalesforce,
          qualificationInfo: qualificationInfo,
          sfLeadId: null, // Will be updated asynchronously by stop event
          newCampaignType: leadData.campaignType || '',
        },
        conferenceName: conference.conferenceName,
        leadData,
      });

      return {
        success: true,
        message: 'Transfer initiated',
        status: 'initiated',
        conferenceData: {
          conferenceName: conference.conferenceName,
          agentCallSid: agentCall.callSid,
        },
      };
    } catch (error) {
      console.error('[GrokFunctionCallingService > WarmTransfer] Transfer error:', error);
      return {
        success: false,
        message: `Transfer failed: ${error.message}`,
        status: 'failed',
      };
    }
  }

  async createConference(customerName) {
    try {
      const conferenceName = `Transfer_${customerName}_${Date.now()}`;

      // Create conference options
      const conferenceOptions = {
        friendlyName: conferenceName,
        waitUrl: 'http://twimlets.com/holdmusic?Bucket=com.twilio.music.classical',
        waitMethod: 'GET',
        endConferenceOnExit: true,
        startConferenceOnEnter: true,
        beep: false,
        record: 'record-from-start',
        statusCallback: process.env.CONFERENCE_STATUS_CALLBACK_URL,
        statusCallbackEvent: ['start', 'end', 'join', 'leave'],
      };

      return {
        success: true,
        conferenceName,
        options: conferenceOptions,
      };
    } catch (error) {
      throw new Error(`Failed to create conference: ${error.message}`);
    }
  }

  async moveCustomerToConference(callSid, callState, conferenceName) {
    try {
      if (!callState) {
        throw new Error(`Could not find call state for ${callSid}`);
      }

      // wait for the current audio queue to be processed, ensuring the AI conversation is complete
      console.log(
        '[GrokFunctionCallingService] Waiting 2 seconds to ensure AI conversation is complete...'
      );
      await new Promise((resolve) => setTimeout(resolve, 4000)); // 等待2秒，确保AI说话完成
      console.log('[GrokFunctionCallingService] Wait completed, proceeding with transfer.');

      const twiml = new this.VoiceResponse();
      twiml.say(
        { voice: 'Google.en-US-Chirp3-HD-Aoede', language: 'en-US' },
        'Please wait, we are transferring you to a professional intake specialist.'
      );
      twiml.dial().conference(
        {
          endConferenceOnExit: false,
          startConferenceOnEnter: false,
          waitUrl: 'http://twimlets.com/holdmusic?Bucket=com.twilio.music.classical',
        },
        conferenceName
      );

      await this.twilioClient.calls(callSid).update({ twiml: twiml.toString() });

      return true;
    } catch (error) {
      throw new Error(`Failed to add customer to conference: ${error.message}`);
    }
  }

  convertPhoneNumberToWords(phoneNumber) {
    if (!phoneNumber || typeof phoneNumber !== 'string') {
      console.warn('[convertPhoneNumberToWords] Invalid phone number input:', phoneNumber);
      return 'Invalid phone number';
    }

    const digits = phoneNumber.replace(/\D/g, '');

    // 检查是否是有效电话号码（例如至少7位）
    if (digits.length < 7) {
      console.warn('[convertPhoneNumberToWords] Phone number too short:', phoneNumber);
      return 'Invalid phone number';
    }

    // 将每个数字转换为英文单词
    const numberWords = {
      0: 'zero',
      1: 'one',
      2: 'two',
      3: 'three',
      4: 'four',
      5: 'five',
      6: 'six',
      7: 'seven',
      8: 'eight',
      9: 'nine',
    };

    return digits
      .split('')
      .map((digit) => numberWords[digit])
      .join(' ');
  }

  async callAgent({ agentPhone, customerInfo, conferenceName, leadData }) {
    try {
      const twiml = new this.VoiceResponse();

      let phone = customerInfo.phone;
      const phoneInWords = this.convertPhoneNumberToWords(phone);

      let campaignType = customerInfo.newCampaignType || leadData.campaignType || 'Not provided';

      // Initial Sentence
      twiml.say(
        { voice: 'Google.en-US-Chirp3-HD-Aoede', language: 'en-US' },
        `You have a new transfer call.`
      );

      // Use Gather to determine the key
      const actionUrl = `${env.server.url}/agent-confirm?conferenceName=${encodeURIComponent(conferenceName)}&customerName=${encodeURIComponent(customerInfo.name)}&customerPhone=${encodeURIComponent(phoneInWords)}&qualificationInfo=${encodeURIComponent(customerInfo.qualificationInfo)}&campaignType=${encodeURIComponent(campaignType)}`;

      twiml
        .gather({
          numDigits: 1,
          action: actionUrl,
          method: 'POST',
          timeout: 300,
        })
        .say(
          { voice: 'Google.en-US-Chirp3-HD-Aoede', language: 'en-US' },
          `Customer information:
        Name: ${customerInfo.name},
        Again, the customer name is: ${customerInfo.name},
        Customer Phone: ${phoneInWords},
        Again, the customer phone is: ${phoneInWords},
        Campaign Type: ${campaignType},
        ${leadData.campaignType ? `Original Campaign Type: ${leadData.campaignType},` : ''}
        And Qualification Info: ${customerInfo.qualificationInfo},
        Press 1 to join the call.,
        Press 2 to listen to the customer information again.`
        );

      // 处理按键输入
      twiml.redirect(actionUrl);

      // Call agent
      const call = await this.twilioClient.calls.create({
        twiml: twiml.toString(),
        to: agentPhone,
        from: process.env.TWILIO_PHONE_NUMBER,
        statusCallback: `${env.server.url}/agent-call-status`,
        statusCallbackEvent: ['initiated', 'ringing', 'answered', 'no-answer', 'failed', 'busy', 'canceled', 'completed'],
        statusCallbackMethod: 'POST',
      });

      console.log('[GrokFunctionCallingService] Agent call initiated:', {
        callSid: call.sid,
        conferenceName,
        actionUrl,
      });

      return {
        success: true,
        callSid: call.sid,
      };
    } catch (error) {
      console.error('[GrokFunctionCallingService] Error calling agent:', error);
      throw new Error(`Failed to call agent: ${error.message}`);
    }
  }

  async findAvailableAgent(agentSkill, urgency) {
    // Find available agent
    // Simulated agent selection logic
    // The actual implementation should query the agent status database or service

    return {
      agentFound: true,
      agentPhone: process.env.TRANSFER_NUMBER, // Simulated agent phone
      agentName: 'John Smith',
      agentId: 'agent123',
    };
  }

  async sendConfirmation(args) {
    try {
      const {
        sendMethod,
        bookingType,
        phoneNumber,
        emailAddress,
        confirmationContent,
        reservationDate,
        reservationTime,
        numberOfGuests,
        restaurantName,
        restaurantAddress,
        restaurantPhone,
        guestName,
        roomType,
        checkInDate,
        checkOutDate,
        numberOfNights,
      } = args;

      if (sendMethod === 'SMS') {
        if (!phoneNumber) {
          throw new Error('Phone number is required for SMS confirmation');
        }

        if (!this.twilioClient) {
          throw new Error('Twilio client is not initialized');
        }

        // Send SMS using Twilio
        const message = await this.twilioClient.messages.create({
          body: confirmationContent,
          from: process.env.TWILIO_PHONE_NUMBER,
          to: phoneNumber,
        });

        return {
          success: true,
          message: 'SMS confirmation sent successfully',
          messageSid: message.sid,
        };
      } else if (sendMethod === 'Email') {
        if (!emailAddress) {
          throw new Error('Email address is required for email confirmation');
        }

        // Send email using sendgrid
        if (bookingType === 'Restaurant') {
          const msg = {
            to: emailAddress,
            from: '<EMAIL>',
            templateId: 'd-b322607dcec24a9b9f6322eeceeb0389',
            dynamicTemplateData: {
              name: guestName || 'Guest',
              reservationDate: reservationDate,
              reservationTime: reservationTime,
              numberOfGuests: numberOfGuests || 2,
              restaurantName: restaurantName || 'SimZ Steak House',
              restaurantAddress: restaurantAddress || '123 Main St, San Francisco, CA 94105',
              restaurantPhone: restaurantPhone || '+****************',
            },
          };

          const response = await sgMail.send(msg);
          return {
            success: true,
            message: 'Email confirmation sent successfully',
            messageId: response[0].headers['x-message-id'],
          };
        } else if (bookingType === 'Hotel') {
          // Send email using sendgrid
          const msg = {
            to: emailAddress,
            from: '<EMAIL>',
            templateId: 'd-95922878391240468d8f4371a71cb145',
            dynamicTemplateData: {
              name: guestName || 'Guest',
              reservationDate: reservationDate || '2024-01-01',
              reservationTime: reservationTime || '12:00 PM',
              numberOfGuests: numberOfGuests || 2,
              restaurantName: restaurantName || 'SimZ Luxury Suites',
              restaurantAddress: restaurantAddress || '123 Main St, San Francisco, CA 94105',
              restaurantPhone: restaurantPhone || '+****************',
              roomType: roomType || 'Standard Room',
              checkInDate: checkInDate || '2024-01-01',
              checkOutDate: checkOutDate || '2024-01-02',
              numberOfNights: numberOfNights || 1,
            },
          };

          const response = await sgMail.send(msg);
          return {
            success: true,
            message: 'Email confirmation sent successfully',
            messageId: response[0].headers['x-message-id'],
          };
        }
      } else if (sendMethod === 'Both') {
        // Send SMS using Twilio
        const message = await this.twilioClient.messages.create({
          body: confirmationContent,
          from: process.env.TWILIO_PHONE_NUMBER,
          to: phoneNumber,
        });

        // Send email using SendGrid
        const msg = {
          to: emailAddress,
          from: '<EMAIL>',
          templateId: 'd-b322607dcec24a9b9f6322eeceeb0389',
          dynamicTemplateData: {
            name: guestName || 'Guest',
            reservationDate: reservationDate,
            reservationTime: reservationTime,
            numberOfGuests: numberOfGuests || 2,
            restaurantName: restaurantName || 'SimZ Steak House',
            restaurantAddress: restaurantAddress || '123 Main St, San Francisco, CA 94105',
            restaurantPhone: restaurantPhone || '+****************',
          },
        };

        const response = await sgMail.send(msg);
        return {
          success: true,
          message: 'Email confirmation sent successfully',
          messageId: response[0].headers['x-message-id'],
        };
      } else {
        throw new Error(`Invalid send method: ${sendMethod}`);
      }
    } catch (error) {
      console.error('[GrokFunctionCallingService] Error sending confirmation:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  async sendPayment(args) {
    try {
      const updatedService = args.service.replace('Sim Zee', 'SimZ');
      const orderData = {
        customerName: args.customerName,
        service: updatedService,
        price: args.price,
        tax: args.tax,
        totalAmount: args.totalAmount,
        phoneNumber: args.phoneNumber,
      };

      const axiosInstance = axios.create({
        baseURL: 'http://localhost:5000/api',
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await axiosInstance.post('/orders/create', orderData);

      return {
        success: true,
        orderId: response.data.order.uniqueId,
        paymentUrl: response.data.order.paymentUrl,
        smsSent: response.data.smsResult.success,
      };
    } catch (error) {
      console.error(
        '[GrokFunctionCallingService] sendPayment error:',
        error?.response?.data || error.message
      );
      return {
        success: false,
        message: error.message,
      };
    }
  }
}

module.exports = { GrokFunctionCallingService };
