// llm-integration/services/GrokLLMService.js
const fs = require('fs');
const path = require('path');
const OpenAI = require('openai');

require('dotenv').config();
const config = require('../../config/env');
const { GrokFunctionCallingService } = require('./GrokFunctionCallingService');
const { convertLegacyOptions } = require('@deepgram/sdk');
const { PromptService } = require('./PromptService');

class GrokLLMService {
  constructor() {
    this.grokApiKey = process.env.GROK_API_KEY || config.grok.apiKey;
    // it is actually openai api key
    // this.grokApiKey = '***************************************************';
    this.grokBaseURL =
      process.env.GROK_API_BASE_URL || config.grok.baseURL || 'https://api.x.ai/v1';
    // this.grokBaseURL = 'https://api.openai.com/v1';
    this.assistantConfigs = {};
    this.externalDependencies = {
      EventHandler: require('../../utils/HandleFunctionCalling.js').EventHandler,
      queueAudio: null,
    };
    this.grokFunctionCallingService = new GrokFunctionCallingService();
    console.log('[GrokLLMService] Initialized with API Key:', this.grokApiKey);
    console.log('[GrokLLMService] Base URL:', this.grokBaseURL);
    this.loadPromptsSync();
    this.grok = this.getClient();
    this.promptService = new PromptService();
    const promptFilePath = path.join(
      __dirname,
      '../utils/prompts/intake_fiveQuestionPreQual_prompt.txt'
    );
    fs.watch(promptFilePath, (eventType, filename) => {
      if (eventType === 'change') {
        console.log('[GrokLLMService] Detected change in hotel prompt file, reloading prompts...');
        this.loadPromptsSync();
      }
    });

    const steakHousePromptFilePath = path.join(
      __dirname,
      '../utils/prompts/simz_steak_house_inbound.txt'
    );
    fs.watch(steakHousePromptFilePath, (eventType, filename) => {
      if (eventType === 'change') {
        console.log(
          '[GrokLLMService] Detected change in steak house prompt file, reloading prompts...'
        );
        this.loadPromptsSync();
      }
    });
  }

  loadPromptsSync() {
    try {
      const defaultGrokPrompt = fs.readFileSync(
        path.join(__dirname, '../utils/prompts/intake_fiveQuestionPreQual_prompt.txt'),
        'utf8'
      );
      const steakHousePrompt = fs.readFileSync(
        path.join(__dirname, '../utils/prompts/simz_steak_house_inbound.txt'),
        'utf8'
      );

      this.assistantConfigs = {
        defaultGrok: {
          // model: 'gpt-4.1',
          model: 'grok-3-fast',
          // model: 'grok-3-beta',
          // model: 'grok-2-latest',
          systemPrompt: defaultGrokPrompt,
        },
        intakeGrok: {
          // model: 'gpt-4.1',
          model: 'grok-3-fast',
          // model: 'grok-3-beta',
          // model: 'grok-2-latest',
          systemPrompt: defaultGrokPrompt,
        },
        steakHouseGrok: {
          // model: 'gpt-4.1',
          model: 'grok-3-fast',
          // model: 'grok-3-beta',
          // model: 'grok-2-latest',
          systemPrompt: steakHousePrompt,
        },
      };
      console.log('[GrokLLMService] Prompts loaded successfully from .txt files');
    } catch (error) {
      console.error('❗[GrokLLMService] Error loading prompts from .txt files:', error);
      this.assistantConfigs = {
        defaultGrok: {
          // model: 'gpt-4.1',
          model: 'grok-3-fast',
          // model: 'grok-3-beta',
          // model: 'grok-2-latest',
          systemPrompt:
            "You are Grok, a chatbot inspired by the Hitchhiker's Guide to the Galaxy.\nProvide witty and insightful responses.",
        },
        intakeGrok: {
          // model: 'gpt-4.1',
          model: 'grok-3-fast',
          // model: 'grok-3-beta',
          // model: 'grok-2-latest',
          systemPrompt:
            'You are Grok, assisting with client intake processes.\nCollect client information and pre-qualify them for legal settlements.',
        },
        steakHouseGrok: {
          // model: 'gpt-4.1',
          model: 'grok-3-fast',
          // model: 'grok-3-beta',
          // model: 'grok-2-latest',
          systemPrompt:
            'You are an AI assistant for Simz Steak House, a high-end steakhouse specializing in premium cuts.',
        },
      };
    }
  }

  getClient() {
    return new OpenAI({
      apiKey: this.grokApiKey,
      baseURL: this.grokBaseURL,
    });
  }

  setDependencies(deps) {
    this.externalDependencies.EventHandler =
      deps.EventHandler || this.externalDependencies.EventHandler;
    this.externalDependencies.queueAudio = deps.queueAudio || this.externalDependencies.queueAudio;
  }

  replacePromptVariables(promptContent, leadData) {
    console.log('[GrokLLMService] Replace Prompt Variables, leadData:', leadData);
    if (!leadData) return promptContent;

    let modifiedPrompt = promptContent;

    // Get Current Date
    const today = new Date();
    const todayDate = today.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });

    // Define First and Last Name
    let firstName = '';
    let lastName = '';
    if (leadData.name) {
      const nameParts = leadData.name.split(' ');
      if (nameParts.length >= 1) firstName = nameParts[0];
      if (nameParts.length >= 2) lastName = nameParts.slice(1).join(' ');
    }
    // Define the replacements
    const replacements = {
      '{{full_name}}': leadData.name || '',
      '{{today_date}}': todayDate,
      '{{first_name}}': firstName,
      '{{last_name}}': lastName,
      '{{email}}': leadData.email || '',
      '{{phone_number}}': leadData.phone || '',
      '{{campaign_type}}': leadData.campaignType || 'mass tort',
    };
    for (const [placeholder, value] of Object.entries(replacements)) {
      modifiedPrompt = modifiedPrompt.replace(new RegExp(placeholder, 'g'), value);
    }

    return modifiedPrompt;
  }

  async generateAudioFromText(text, ws, streamSid, state, callSid, broadcastCallMessages) {
    if (!text) return;

    const sentence = text.trim();
    
    // Skip TTS for non-meaningful content
    if (this.shouldSkipTTS(sentence)) {
      console.log('[GrokLLMService] Skipping TTS for non-meaningful text:', sentence);
      return;
    }
    
    if (sentence) {
      console.log('[GrokLLMService] Generating audio for text:🟢', sentence);
      const audioFileName = `responseAudio_${Date.now()}_${Math.random()}.wav`;

      this.externalDependencies.queueAudio(
        sentence,
        audioFileName,
        ws,
        streamSid,
        state.cannotBeCutBuffer,
        callSid,
        broadcastCallMessages
      );

      if (/you may pre-qualify for a settlement since you have used one or more/i.test(sentence)) {
        state.userQualify = true;
      }
      state.assistantConfig = await this.switchAssistantBasedOnTextResponse(sentence, state);

      const openaiEndTime = Date.now();
      const openaiResponseTime = openaiEndTime - state.openaiStartTime;
      if (
        !state.textToSpeechConversionTimeMap ||
        !(state.textToSpeechConversionTimeMap instanceof Map)
      ) {
        console.error(
          '❗[GrokLLMService] textToSpeechConversionTimeMap is undefined or invalid, initializing'
        );
        state.textToSpeechConversionTimeMap = new Map();
      }
      state.textToSpeechConversionTimeMap.set(audioFileName, openaiResponseTime);
      state.openaiStartTime = Date.now();
    }
  }

  async handleGrokResponse(
    transcript,
    ws,
    streamSid,
    state,
    previousMessages = [],
    callService,
    callSid,
    broadcastCallMessages
  ) {
    try {
      let triggerToolCall = false;
      // Check if there is requests being processing. If yes, skip the current request.
      if (state.isProcessingLLM) {
        console.log('[GrokLLMService] Skip the request because it is already being processed');
        return;
      }

      let messages = [...previousMessages];
      previousMessages.push({
        role: 'user',
        content: transcript,
        timestamp: Date.now(),
        interrupted: false,
      });
      if (transcript) {
        messages.push({ role: 'user', content: transcript });
      }

      let accumulatedText = '';
      let entireResponse = '';
      const eventHandler = new this.externalDependencies.EventHandler(this.grok, ws, streamSid);

      // Enhanced tool call event handling for campaign prompts
      eventHandler.on('toolCallProcessed', async ({ toolCallName, output }) => {
        console.log('[GrokLLMService] Tool call processed:', toolCallName);

        // Parse the output if it's a string
        let parsedOutput = output;
        if (typeof output === 'string') {
          try {
            parsedOutput = JSON.parse(output);
          } catch (e) {
            console.warn('[GrokLLMService] Could not parse tool call output as JSON');
          }
        }

        // Handle campaign-related function results
        if (toolCallName === 'collectDiagnosisAndCreateCampaignQueue') {
          console.log(
            '[GrokLLMService] Campaign queue created:',
            parsedOutput.success
              ? `Found ${parsedOutput.queue?.length || 0} campaigns for cancer types: ${parsedOutput.diagnosisInfo?.cancerTypes?.join(', ') || 'unknown'}`
              : `Failed: ${parsedOutput.message}`
          );

          if (parsedOutput.success && parsedOutput.queue?.length > 0) {
            // Campaign queue created successfully
            state.hasCampaignQueue = true;
          }
        } else if (toolCallName === 'switchToNextCampaign') {
          console.log('[GrokLLMService] Switch to next campaign result:', parsedOutput);

          if (parsedOutput.systemPromptChanged) {
            console.log(
              '[GrokLLMService] Prompt was switched to next campaign:',
              parsedOutput.nextCampaign?.name
            );
          }
        }

        // Continue with existing processing
        state.assistantConfig = await this.switchAssistantBasedOnFunctionCall(
          toolCallName,
          parsedOutput,
          state
        );
      });

      eventHandler.on('toolCallProcessedTextResult', async ({ textResult }) => {
        console.log('[GrokLLMService] Tool call text result:', textResult);
        if (
          /you may pre-qualify for a settlement since you have used one or more/i.test(textResult)
        ) {
          state.userQualify = true;
        }
        state.assistantConfig = await this.switchAssistantBasedOnTextResponse(textResult, state);
      });

      // 根据callSid查找对应的promptType
      let promptData = state.getState('promptData');
      let leadData = state.getState('leadData');
      let systemPrompt = this.assistantConfigs.defaultGrok.systemPrompt;

      // 根据promptType选择对应的系统提示
      if (promptData) {
        if (promptData.promptType === 'simz-steakhouse') {
          console.log('[GrokLLMService] Using Simz Steak House system prompt.');

          // Check if campaign queue is generated
          if (
            promptData.campaignQueue &&
            Array.isArray(promptData.campaignQueue) &&
            promptData.campaignQueue.length > 0
          ) {
            console.log('[GrokLLMService] Campaign queue exists, using dynamic prompt content.');

            // Use dynamic prompt
            if (promptData.promptContent) {
              systemPrompt = this.replacePromptVariables(promptData.promptContent, leadData);
              console.log('[GrokLLMService] Using dynamic prompt from campaign queue.');
            } else {
              console.log(
                '[GrokLLMService] Campaign queue exists but no prompt content found, using default.'
              );
            }
          } else {
            // First time loading prompt
            console.log(
              '[GrokLLMService] No campaign queue, fetching initial prompt from MongoDB.'
            );
            // TODO: Inbound does not work for this case
            const promptObj = await this.promptService.getPromptById(
              'a213c239-2313-47ef-a1fc-ead82891e2d6'
            );
            systemPrompt = this.replacePromptVariables(promptObj.content, leadData);
            console.log('[GrokLLMService] Using initial legal intake prompt.');
          }
        } else if (promptData.promptContent) {
          console.log('[GrokLLMService] Using custom prompt content.');
          systemPrompt = this.replacePromptVariables(promptData.promptContent, leadData);
        } else {
          console.log('[GrokLLMService] Using default system prompt.');
        }
      } else {
        console.log('[GrokLLMService] No prompt data found, using default system prompt.');
      }

      messages.unshift({ role: 'system', content: systemPrompt });

      console.log('[GrokLLMService] Using API Key:', this.grokApiKey);
      console.log('[GrokLLMService] Using Base URL:', this.grokBaseURL);
      console.log('[GrokLLMService] Model:', this.assistantConfigs.defaultGrok.model);

      state.isProcessingLLM = true;
      callService.stopPlayingAudioAndClearAudioQueue(ws, state.getState('streamSid'));
      state.isGeneratingResponse = true;

      // get the tool definitions
      const tools = this.grokFunctionCallingService.getToolDefinitions();
      console.log(
        '[GrokLLMService] Using tools:',
        tools.map((t) => t.function.name)
      );

      const stream = await this.grok.chat.completions.create({
        model: this.assistantConfigs.defaultGrok.model,
        messages,
        stream: true,
        tools: tools, // add tools parameter
        tool_choice: 'auto', // allow model to decide whether to use tools
      });

      for await (const chunk of stream) {
        // Check if there are tool calls being made
        if (chunk.choices[0]?.delta?.tool_calls) {
          triggerToolCall = true;
          const toolCallDelta = chunk.choices[0].delta.tool_calls[0];
          console.log('[GrokLLMService] Tool call detected:', toolCallDelta);

          // Process the full tool call (actual implementation would be more complex, handling streaming tool call concatenation)
          if (
            toolCallDelta.function &&
            toolCallDelta.function.name &&
            toolCallDelta.function.arguments
          ) {
            const toolCallResult = await this.grokFunctionCallingService.processToolCall(
              toolCallDelta,
              state
            );
            console.log('[GrokLLMService] Tool call result:', toolCallResult);

            // Check if this was a prompt-switching tool call
            if (
              toolCallDelta.function.name === 'switchToNextCampaign' ||
              toolCallDelta.function.name === 'collectDiagnosisAndCreateCampaignQueue'
            ) {
              try {
                const result = JSON.parse(toolCallResult.output);
                console.log('[GrokLLMService] Prompt switching tool call result:', result);

                if (
                  toolCallDelta.function.name === 'collectDiagnosisAndCreateCampaignQueue' &&
                  result.success &&
                  result.queue?.length > 0
                ) {
                  // Ininialize campaign queue
                  console.log(
                    '[GrokLLMService] Campaign queue created with:',
                    result.queue.length,
                    'campaigns'
                  );

                  // Create Disease and campaign relation
                  const campaignRelations = result.queue
                    .filter((campaign) => campaign.clientDiseaseRelate.length > 0)
                    .map((campaign) => {
                      const diseases = campaign.clientDiseaseRelate.join(' and ');
                      return `${campaign.name} Campaign is only related to Client's ${diseases}`;
                    })
                    .join(', ');

                  const relationMessage = campaignRelations
                    ? `Client diagnosis and campaign link: ${campaignRelations}. As a legal assistant, you have to only follow the above cancer and campaign relationship. And never assume any other relationship.`
                    : 'No specific disease-campaign relationships found';
                  console.log('[GrokLLMService] Campaign relation message:', relationMessage);

                  // Add system message to message history
                  previousMessages.push({
                    role: 'system',
                    content: relationMessage,
                  });

                  const promptData = state.getState('promptData') || {};
                  console.log('[GrokLLMService] Checking prompt:', promptData);

                  promptData.campaignQueue = result.queue;
                  promptData.currentCampaignIndex = 0;

                  // Update PromptData
                  state.updateState('promptData', promptData);

                  const currentCampaign = result.queue[0];
                  const promptId = currentCampaign.promptId;

                  try {
                    const PromptService = require('./PromptService').PromptService;
                    const promptService = new PromptService();

                    const latestPromptData = await promptService.getLatestVersion(promptId);

                    promptData.promptId = promptId;
                    promptData.promptVersion = latestPromptData.version;
                    promptData.promptContent = latestPromptData.content;

                    // Update PromptData
                    state.updateState('promptData', promptData);

                    console.log(
                      `[GrokLLMService] Updated prompt to campaign ${currentCampaign.name}, promptId: ${promptId}, version: ${latestPromptData.version}`
                    );

                    // update grok prompt
                    messages[0] = { role: 'system', content: latestPromptData.content };
                  } catch (error) {
                    console.error('[GrokLLMService] Error fetching prompt:', error);
                  }
                } else if (
                  toolCallDelta.function.name === 'switchToNextCampaign' &&
                  result.systemPromptChanged
                ) {
                  // 切换到下一个campaign
                  console.log('[GrokLLMService] Switching to next campaign');

                  const promptData = state.getState('promptData') || {};
                  console.log('[GrokLLMService] Checking prompt:', promptData.campaignQueue);
                  if (
                    !promptData.campaignQueue ||
                    !Array.isArray(promptData.campaignQueue) ||
                    promptData.campaignQueue.length === 0
                  ) {
                    console.log('[GrokLLMService] No campaign queue available to switch');
                  } else {
                    // 更新当前指针，移至下一个campaign
                    if (promptData.currentCampaignIndex === undefined) {
                      promptData.currentCampaignIndex = 0;
                    } else {
                      promptData.currentCampaignIndex++;
                    }
                    console.log(
                      '[GrokLLMService] Current campaign index:',
                      promptData.currentCampaignIndex
                    );

                    // 检查是否超出队列范围
                    if (promptData.currentCampaignIndex >= promptData.campaignQueue.length) {
                      console.log('[GrokLLMService] Reached end of campaign queue');
                    } else {
                      // 获取当前campaign
                      const currentCampaign =
                        promptData.campaignQueue[promptData.currentCampaignIndex];
                      const promptId = currentCampaign.promptId;

                      try {
                        // 加载PromptService来获取最新版本的prompt
                        const PromptService = require('./PromptService').PromptService;
                        const promptService = new PromptService();

                        // 获取最新版本的prompt内容
                        const latestPromptData = await promptService.getLatestVersion(promptId);

                        // 更新promptData中的信息
                        promptData.promptId = promptId;
                        promptData.promptVersion = latestPromptData.version;
                        promptData.promptContent = latestPromptData.content;

                        // Update PromptData
                        state.updateState('promptData', promptData);

                        console.log(
                          `[GrokLLMService] Switched to campaign ${currentCampaign.name}, promptId: ${promptId}, version: ${latestPromptData.version}`
                        );
                        // 更新系统提示
                        messages[0] = { role: 'system', content: latestPromptData.content };
                        console.log(
                          '[GrokLLMService] Updated system prompt with new prompt:',
                          latestPromptData.content
                        );
                      } catch (error) {
                        console.error('[GrokLLMService] Error fetching prompt:', error);
                      }
                    }
                  }
                }
              } catch (error) {
                console.error(
                  `[GrokLLMService] Error parsing ${toolCallDelta.function.name} result:`,
                  error
                );
              }
            }

            // Add the tool call result to the messages
            messages.push({
              role: 'tool',
              tool_call_id: toolCallDelta.id,
              content: toolCallResult.output,
            });

            // previousMessages.push({
            //   role: 'tool',
            //   tool_call_id: toolCallDelta.id,
            //   content: toolCallResult.output
            // });

            // // Use the updated messages to continue the conversation
            // const response = await this.grok.chat.completions.create({
            //   model: this.assistantConfigs.defaultGrok.model,
            //   messages,
            //   tools: tools,
            //   tool_choice: "auto"
            // });

            // console.log('[GrokLLMService] Grok response after tool call:', response.choices[0].message.content);

            // // 处理tool call后的response
            // const toolCallResponse = response.choices[0].message.content;
            // if (toolCallResponse) {
            //   // 按句子分割文本
            //   const sentences = toolCallResponse.split(/(?<=[.!?])\s+/);
            //   for (const sentence of sentences) {
            //     if (sentence.trim()) {
            //       entireResponse += sentence + ' ';
            //       await this.generateAudioFromText(sentence, ws, streamSid, state, agentId, broadcastCallMessages);
            //     }
            //   }
            // }
          }
          continue;
        }

        // Process regular text responses
        const delta = chunk.choices[0]?.delta?.content;
        if (delta) {
          accumulatedText += delta;
          if (/[.!?]/.test(delta)) {
            const sentence = accumulatedText.trim();
            entireResponse += sentence + ' ';
            accumulatedText = '';
            await this.generateAudioFromText(
              sentence,
              ws,
              streamSid,
              state,
              callSid,
              broadcastCallMessages
            );
          }
        }
      }

      if (accumulatedText.trim()) {
        const sentence = accumulatedText.trim();
        entireResponse += sentence + ' ';
        await this.generateAudioFromText(
          sentence,
          ws,
          streamSid,
          state,
          callSid,
          broadcastCallMessages
        );
        console.log('[GrokLLMService] Processed remaining accumulated text:', sentence);
      }

      if (entireResponse) {
        messages.push({
          role: 'assistant',
          content: entireResponse,
          timestamp: Date.now(),
          interrupted: false,
        });
        previousMessages.push({
          role: 'assistant',
          content: entireResponse,
          timestamp: Date.now(),
          interrupted: false,
        });
      }

      state.currentRunId = `grok_run_${Date.now()}`;
      console.log('[GrokLLMService] Stream ended, run ID updated:', state.currentRunId);

      if (triggerToolCall) {
        // If triggerToolCall is true，need to generate another following response

        console.log('[GrokLLMService] Trigger tool call, processing tool call result');
        let entireResponseAfterToolCall = '';
        // Use the updated messages to continue the conversation
        const response = await this.grok.chat.completions.create({
          model: this.assistantConfigs.defaultGrok.model,
          messages,
          tools: tools,
          tool_choice: 'auto',
        });

        console.log(
          '[GrokLLMService] Grok response after tool call:',
          response.choices[0].message.content
        );

        // Generate response after call tool
        const toolCallResponse = response.choices[0].message.content;
        if (toolCallResponse) {
          const sentences = toolCallResponse.split(/(?<=[.!?])\s+/);
          for (const sentence of sentences) {
            if (sentence.trim()) {
              entireResponseAfterToolCall += sentence + ' ';
              await this.generateAudioFromText(sentence, ws, streamSid, state, callSid, broadcastCallMessages);
            }
          }
        }
        previousMessages.push({
          role: 'assistant',
          content: entireResponseAfterToolCall,
          timestamp: Date.now(),
          interrupted: false,
        });
        triggerToolCall = false;
      } else {
        // If triggerToolCall is false, make sure to check if we need to force trigger one of the function
        console.log('[GrokLLMService] No tool call triggered, checking if we need to force trigger one of the function');
        const shouldForceToolCall = this.shouldForceToolCall(entireResponse, state, transcript);
        
        console.log('[GrokLLMService] Tool choice strategy:', shouldForceToolCall, 'based on response:', entireResponse);
        
        if (shouldForceToolCall) {
          console.log(`[GrokLLMService] Force triggering function: ${shouldForceToolCall.functionName}`);
          console.log(`[GrokLLMService] Trigger type: ${shouldForceToolCall.triggerType}`);
          
          try {
            // Create a mock tool call object similar to what Grok would send
            const mockToolCall = {
              id: `forced_call_${Date.now()}`,
              function: {
                name: shouldForceToolCall.functionName,
                arguments: JSON.stringify(shouldForceToolCall.args)
              }
            };
            
            // Use GrokFunctionCallingService to process the forced tool call
            const toolCallResult = await this.grokFunctionCallingService.processToolCall(
              mockToolCall,
              state
            );
            
            console.log('[GrokLLMService] Forced tool call result:', toolCallResult);
            
            // Handle different types of forced tool calls
            switch (shouldForceToolCall.triggerType) {
              case 'campaign_switch':
                await this.handleForcedCampaignSwitch(toolCallResult, state);
                break;
                
              case 'transfer_to_agent':
                console.log('[GrokLLMService] Forced transfer to agent initiated');
                // The transfer is handled by the function itself
                break;
                
              case 'call_termination':
                console.log('[GrokLLMService] Forced call termination initiated');
                // The hangup is handled by the function itself
                break;
                
              case 'diagnosis_collection':
                console.log('[GrokLLMService] Forced diagnosis collection - this may need user input');
                // This function might need actual user input to work properly
                break;
                
              case 'disqualification':
                await this.handleForcedCampaignSwitch(toolCallResult, state);
                break;
                
              default:
                console.log(`[GrokLLMService] Unknown trigger type: ${shouldForceToolCall.triggerType}`);
            }
            // Generate response after tool call
            console.log('[GrokLLMService] Trigger tool call, processing tool call result');
            let entireResponseAfterToolCall = '';
            // Use the updated messages to continue the conversation
            const response = await this.grok.chat.completions.create({
              model: this.assistantConfigs.defaultGrok.model,
              messages,
              tools: tools,
              tool_choice: 'auto',
            });

            console.log(
              '[GrokLLMService] Grok response after tool call:',
              response.choices[0].message.content
            );

            // Generate response after call tool
            const toolCallResponse = response.choices[0].message.content;
            if (toolCallResponse) {
              const sentences = toolCallResponse.split(/(?<=[.!?])\s+/);
              for (const sentence of sentences) {
                if (sentence.trim()) {
                  entireResponseAfterToolCall += sentence + ' ';
                  await this.generateAudioFromText(sentence, ws, streamSid, state, callSid, broadcastCallMessages);
                }
              }
            }
            previousMessages.push({
              role: 'assistant',
              content: entireResponseAfterToolCall,
              timestamp: Date.now(),
              interrupted: false,
            });
            
          } catch (error) {
            console.error('[GrokLLMService] Error executing forced tool call:', error);
          }
        }
      }

      state.isProcessingLLM = false;
      return { id: state.currentRunId, messages: previousMessages };
    } catch (error) {
      console.error('❗[GrokLLMService] Error handling Grok API:', error);
      state.isProcessingLLM = false;
      throw error;
    }
  }

  async createAssistantMessage(content, state) {
    state.messages = state.messages || [];
    state.messages.push({ role: 'assistant', content: content });
    return state.messages;
  }

  async createUserMessage(content, state) {
    state.messages = state.messages || [];
    state.messages.push({ role: 'user', content: content });
    return state.messages;
  }

  async createRun(threadId, assistantConfig) {
    console.log(`Simulating run creation for Grok with config:`, assistantConfig);
    return { id: `grok_run_${Date.now()}` };
  }

  async cancelRunWithTimeout(threadId, runId, timeoutMs, retryIntervalMs, ws, streamSid, state) {
    console.log(
      `Simulating cancellation of run ${runId}. Grok streaming cannot be cancelled mid-stream.`
    );
    const newRunId = `grok_run_${Date.now()}`;
    state.currentRunId = newRunId;
    await this.handleGrokResponse('', ws, streamSid, state, state.messages || []);
    return { status: 'cancelled', id: newRunId };
  }

  async waitForRunCompletion(threadId, runId) {
    console.log(`Simulating wait for run ${runId} completion (Grok doesn't require this).`);
  }

  async switchAssistantBasedOnFunctionCall(toolCallName, output, state) {
    console.log('[GrokLLMService] Switching assistant based on function call:', toolCallName);
    let success = false;
    if (typeof output === 'object') {
      success = output.success;
    } else if (output === true) {
      success = true;
    }
    if (success) {
      console.log('[GrokLLMService] Tool call was successful');
      state.userQualify = true;
    }
    // Handle campaign-related tools specifically
    if (toolCallName === 'collectDiagnosisAndCreateCampaignQueue') {
      return this.assistantConfigs.intakeGrok;
    } else if (toolCallName === 'switchToNextCampaign') {
      return null; // No need to change assistantConfig as the prompt content will be updated directly
    }
    // Handle other tools
    switch (toolCallName) {
      case 'check_cancer_qualification':
        return success ? this.assistantConfigs.intakeGrok : this.assistantConfigs.defaultGrok;
      case 'check_medical_qualification':
        return success ? this.assistantConfigs.intakeGrok : this.assistantConfigs.defaultGrok;
      case 'check_accident_qualification':
        return this.assistantConfigs.intakeGrok;
      default:
        console.log('Unknown tool call');
        return null;
    }
  }

  async switchAssistantBasedOnTextResponse(testResponse, state) {
    // const lowerCaseResponse = testResponse.toLowerCase();
    // let newAssistantConfig = state.assistantConfig || this.assistantConfigs.defaultGrok;
    // if (/port catheter|hernia mesh/.test(lowerCaseResponse)) {
    //   console.log('Switching to Intake Grok for Medical');
    //   newAssistantConfig = this.assistantConfigs.intakeGrok;
    // } else if (/car accident/.test(lowerCaseResponse)) {
    //   console.log('Switching to Intake Grok for Accident');
    //   newAssistantConfig = this.assistantConfigs.intakeGrok;
    // } else if (/you do indeed pre-qualify|you are pre-qualified/.test(lowerCaseResponse)) {
    //   console.log('User pre-qualified, keeping Intake Grok');
    //   newAssistantConfig = this.assistantConfigs.intakeGrok;
    // } else if (/i need to ask you some additional questions|unfortunately.*not qualify|I'm sorry, but based on/i.test(lowerCaseResponse)) {
    //   console.log('User might not qualify, checking if we need to switch campaigns');
    //   if (state.hasCampaignQueue) {
    //     // If there's a negative response and we have a campaign queue,
    //     // this might be a good time to use switchToNextCampaign
    //     console.log('[GrokLLMService] May need to switch campaigns based on text response');
    //   }
    // }
    // return newAssistantConfig;
  }

  /**
   * Check if we should force trigger a tool call based on the AI response
   * @param {string} entireResponse - The complete AI response
   * @param {Object} state - The current call state
   * @param {string} lastUserInput - The most recent user input
   * @returns {Object|null} - Returns tool call info if force trigger needed, null otherwise
   */
  shouldForceToolCall(entireResponse, state, lastUserInput = '') {
    const lowerResponse = entireResponse.toLowerCase();
    const lowerUserInput = lastUserInput.toLowerCase();
    
    // 1. Check for collectDiagnosisAndCreateCampaignQueue triggers
    const diagnosisTriggers = [
      "it seems like you may qualify for a settlement",
      "glad to hear you've never been diagnosed with anything else",
    ];
    
    const foundDiagnosisTrigger = diagnosisTriggers.find(trigger => 
      lowerResponse.includes(trigger)
    );
    
    if (foundDiagnosisTrigger) {
      console.log(`[GrokLLMService] Found diagnosis collection trigger: "${foundDiagnosisTrigger}"`);
      
      // Extract cancer types and diagnosis dates from conversation history or user input
      const extractedData = this.extractCancerDiagnosisFromContext(state, lastUserInput);
      
      return {
        functionName: 'collectDiagnosisAndCreateCampaignQueue',
        args: {
          cancerTypes: extractedData.cancerTypes,
          diagnosisDate: extractedData.diagnosisDate
        },
        foundPhrase: foundDiagnosisTrigger,
        triggerType: 'diagnosis_collection'
      };
    }
    
    // 2. Check for WarmTransferToInHouseAgent triggers
    const transferTriggers = [
      "i will now transfer you over to the intake specialist",
      "transfer you to a human agent",
      "connect you with our specialist",
      "let me transfer you to someone who can help",
      "i'll transfer you over to",
      "transfer you over to the intake specialist",
      "great news as you meet the criteria to be matched with one of our firms"
    ];
    
    const foundTransferTrigger = transferTriggers.find(trigger => 
      lowerResponse.includes(trigger)
    );
    
    if (foundTransferTrigger) {
      console.log(`[GrokLLMService] Found transfer trigger: "${foundTransferTrigger}"`);
      
      const leadData = state.getState('leadData') || {};
      const promptData = state.getState('promptData') || {};
      
      return {
        functionName: 'WarmTransferToInHouseAgent',
        args: {
          customerPhone: leadData.phone || leadData.customerPhone || '',
          customerName: leadData.name || leadData.customerName || 'Customer',
          customerEmail: leadData.email || leadData.customerEmail || '',
          qualificationInfo: `Customer qualified for ${promptData.campaignType || 'legal consultation'} campaign`
        },
        foundPhrase: foundTransferTrigger,
        triggerType: 'transfer_to_agent'
      };
    }
    
    // 3. Check for hangUp triggers
    const hangUpTriggers = [
      "hank you so much for taking my call",
      "have a great rest of your day",
    ];
    
    const foundHangUpTrigger = hangUpTriggers.find(trigger => 
      lowerResponse.includes(trigger)
    );
    
    if (foundHangUpTrigger) {
      console.log(`[GrokLLMService] Found hang up trigger: "${foundHangUpTrigger}"`);
      return {
        functionName: 'hangUp',
        args: {
          reason: 'call_completion'
        },
        foundPhrase: foundHangUpTrigger,
        triggerType: 'call_termination'
      };
    }
    
    // 4. Check for switchToNextCampaign triggers
    const campaignSwitchTriggers = [
      "let you know as soon as a firm is willing to take your",
      "it seems like you may qualify for another settlement",
      "it seems like you may still qualify for another settlement"
    ];
    
    const foundCampaignSwitchTrigger = campaignSwitchTriggers.find(trigger => 
      lowerResponse.includes(trigger)
    );
    
    if (foundCampaignSwitchTrigger) {
      console.log(`[GrokLLMService] Found campaign switch trigger: "${foundCampaignSwitchTrigger}"`);
      
      // Determine the reason based on the context of the response
      let reason = "auto_detected_disqualification";
    
      return {
        functionName: 'switchToNextCampaign',
        args: { reason },
        foundPhrase: foundCampaignSwitchTrigger,
        triggerType: 'campaign_switch'
      };
    }

    // 5. Check for other disqualification scenarios
    const disqualificationKeywords = [
      "unfortunately, you do not qualify",
      "does not fit the date criteria",
      "doesn't fit the date criteria", 
      "you do not meet the criteria",
      "not qualify for this campaign",
      "unfortunately.*not qualify",
      "i'm sorry, but based on"
    ];

    const foundDisqualification = disqualificationKeywords.find(keyword => 
      lowerResponse.includes(keyword) || new RegExp(keyword, 'i').test(entireResponse)
    );

    if (foundDisqualification) {
      console.log(`[GrokLLMService] Found disqualification keyword: "${foundDisqualification}"`);
      return {
        functionName: 'switchToNextCampaign',
        args: { reason: "disqualification_detected" },
        foundPhrase: foundDisqualification,
        triggerType: 'disqualification'
      };
    }

    return null;
  }

  /**
   * Handle forced campaign switch result
   * @param {Object} toolCallResult - The result from the forced tool call
   * @param {Object} state - The current call state
   */
  async handleForcedCampaignSwitch(toolCallResult, state) {
    try {
      const result = JSON.parse(toolCallResult.output);
      console.log('[GrokLLMService] Forced campaign switch result:', result);
      
      if (result.systemPromptChanged) {
        console.log('[GrokLLMService] System prompt was changed by forced tool call');
        
        // Update the prompt data as needed
        const promptData = state.getState('promptData') || {};
        if (result.nextCampaign && result.nextCampaign.promptId) {
          // Fetch and update the new prompt content
          try {
            const PromptService = require('./PromptService').PromptService;
            const promptService = new PromptService();
            const latestPromptData = await promptService.getLatestVersion(result.nextCampaign.promptId);
            
            promptData.promptId = result.nextCampaign.promptId;
            promptData.promptVersion = latestPromptData.version;
            promptData.promptContent = latestPromptData.content;
            
            state.updateState('promptData', promptData);
            console.log(`[GrokLLMService] Updated to campaign ${result.nextCampaign.name} via forced tool call`);
          } catch (error) {
            console.error('[GrokLLMService] Error updating prompt after forced tool call:', error);
          }
        }
      } else if (result.success === false) {
        console.log('[GrokLLMService] Campaign switch failed:', result.message);
      }
    } catch (error) {
      console.error('[GrokLLMService] Error parsing forced campaign switch result:', error);
    }
  }

  /**
   * Extract cancer diagnosis information from conversation context
   * @param {Object} state - The current call state
   * @param {string} lastUserInput - The most recent user input
   * @returns {Object} - Object containing cancerTypes and diagnosisDate arrays
   */
  extractCancerDiagnosisFromContext(state, lastUserInput) {
    const cancerTypes = [];
    const diagnosisDate = [];
    
    // Common cancer types to look for
    const cancerKeywords = [
      'ovarian cancer', 'ovarian', 'ovaries',
      'breast cancer', 'breast',
      'lung cancer', 'lung',
      'liver cancer', 'liver',
      'kidney cancer', 'kidney',
      'bladder cancer', 'bladder',
      'thyroid cancer', 'thyroid',
      'testicular cancer', 'testicular',
      'uterine cancer', 'uterine',
      'endometrial cancer', 'endometrial',
      'lymphoma', 'leukemia',
      'non-hodgkin\'s lymphoma', 'non-hodgkins lymphoma',
      'multiple myeloma',
      'mesothelioma',
      'prostate cancer', 'prostate'
    ];
    
    // Check the most recent user input
    const lowerUserInput = lastUserInput.toLowerCase();
    
    // Look for cancer types in user input
    cancerKeywords.forEach(keyword => {
      if (lowerUserInput.includes(keyword)) {
        // Normalize the cancer type
        let normalizedCancer = keyword;
        if (keyword === 'ovaries') normalizedCancer = 'Ovarian Cancer';
        else if (keyword === 'breast') normalizedCancer = 'Breast Cancer';
        else if (keyword === 'lung') normalizedCancer = 'Lung Cancer';
        else if (keyword === 'liver') normalizedCancer = 'Liver Cancer';
        else if (keyword === 'kidney') normalizedCancer = 'Kidney Cancer';
        else if (keyword === 'bladder') normalizedCancer = 'Bladder Cancer';
        else if (keyword === 'thyroid') normalizedCancer = 'Thyroid Cancer';
        else if (keyword === 'testicular') normalizedCancer = 'Testicular Cancer';
        else if (keyword === 'uterine') normalizedCancer = 'Uterine Cancer';
        else if (keyword === 'endometrial') normalizedCancer = 'Endometrial Cancer';
        else if (keyword === 'prostate') normalizedCancer = 'Prostate Cancer';
        else if (keyword.includes('lymphoma')) normalizedCancer = 'Non-Hodgkin\'s Lymphoma';
        else normalizedCancer = keyword.charAt(0).toUpperCase() + keyword.slice(1);
        
        if (!cancerTypes.includes(normalizedCancer)) {
          cancerTypes.push(normalizedCancer);
        }
      }
    });
    
    // Look for diagnosis dates in user input
    const datePatterns = [
      /(\d{4})/g, // Year only (e.g., "2020")
      /(\d{1,2})\/(\d{4})/g, // MM/YYYY format
      /(\d{1,2})-(\d{4})/g, // MM-YYYY format
      /(\w+)\s+(\d{4})/g, // "January 2020" format
      /(\d+)\s+years?\s+ago/gi, // "5 years ago" format
    ];
    
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1;
    
    // Extract years
    const yearMatches = lowerUserInput.match(/\b(19|20)\d{2}\b/g);
    if (yearMatches) {
      yearMatches.forEach(year => {
        const yearNum = parseInt(year);
        if (yearNum >= 1950 && yearNum <= currentYear) {
          // Default to January if no month specified
          const dateStr = `${yearNum}-01`;
          if (!diagnosisDate.includes(dateStr)) {
            diagnosisDate.push(dateStr);
          }
        }
      });
    }
    
    // Extract "X years ago" patterns
    const yearsAgoMatch = lowerUserInput.match(/(\d+)\s+years?\s+ago/i);
    if (yearsAgoMatch) {
      const yearsAgo = parseInt(yearsAgoMatch[1]);
      const diagnosisYear = currentYear - yearsAgo;
      const dateStr = `${diagnosisYear}-01`;
      if (!diagnosisDate.includes(dateStr)) {
        diagnosisDate.push(dateStr);
      }
    }
    
    // If we found cancer types but no dates, add a default current date
    if (cancerTypes.length > 0 && diagnosisDate.length === 0) {
      diagnosisDate.push(`${currentYear}-${currentMonth.toString().padStart(2, '0')}`);
    }
    
    // If we found dates but no cancer types, add a default
    if (diagnosisDate.length > 0 && cancerTypes.length === 0) {
      cancerTypes.push('Cancer'); // Generic cancer type
    }
    
    console.log(`[GrokLLMService] Extracted cancer data: ${cancerTypes.join(', ')} diagnosed ${diagnosisDate.join(', ')}`);
    
    return {
      cancerTypes,
      diagnosisDate
    };
  }

  /**
   * Check if text should be skipped for TTS generation
   * @param {string} text - The text to check
   * @returns {boolean} - True if should skip, false otherwise
   */
  shouldSkipTTS(text) {
    if (!text || text.length === 0) return true;
    
    // Skip if contains separator token
    if (text.includes('<|separator|>')) {
      return true;
    }
    
    // Remove whitespace and check if only dots/ellipsis
    const cleanText = text.replace(/\s/g, '');
    
    // Skip if only dots (., .., ..., etc.)
    if (/^\.+$/.test(cleanText)) {
      return true;
    }
    
    // Skip if only ellipsis characters
    if (/^…+$/.test(cleanText)) {
      return true;
    }
    
    // Skip if only punctuation and no letters/numbers
    if (!/[a-zA-Z0-9]/.test(text)) {
      return true;
    }
    
    // Skip very short meaningless responses
    const meaninglessPatterns = [
      /^\.{1,}$/,           // Just dots
      /^…{1,}$/,            // Just ellipsis
      /^\s*\.\s*$/,         // Single dot with spaces
      /^\s*\.\.\.\s*$/,     // Three dots with spaces
      /^\s*[.,;:!?]*\s*$/   // Only punctuation
    ];
    
    return meaninglessPatterns.some(pattern => pattern.test(text));
  }
}

module.exports = { GrokService: GrokLLMService };
