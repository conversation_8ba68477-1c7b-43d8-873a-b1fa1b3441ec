// llm-integration/services/CampaignService.js
const { CampaignRepository } = require('../repositories/CampaignRepository');
const { PromptService } = require('./PromptService');

class CampaignService {
  constructor() {
    this.campaignRepository = new CampaignRepository();
    this.promptService = new PromptService();
  }

  async findMatchingCampaigns(cancerTypes, diagnosisDate) {
    try {
      // Validate date format
      const dateRegex = /^\d{4}-\d{2}$/;
      if (!dateRegex.test(diagnosisDate)) {
        throw new Error('Invalid diagnosis date format. Expected: YYYY-MM');
      }

      // 确保cancerTypes是数组
      const cancerTypesArray = Array.isArray(cancerTypes) ? cancerTypes : [cancerTypes];
      if (cancerTypesArray.length === 0) {
        console.warn('[CampaignService] No cancer types provided');
        return [];
      }

      console.log(
        `[CampaignService] Finding campaigns for cancer types: ${cancerTypesArray.join(', ')}`
      );

      // 合并所有癌症类型匹配的campaigns
      let allMatchingCampaigns = [];
      const processedCampaignIds = new Set();

      // 对每种癌症类型进行查询
      for (const cancerType of cancerTypesArray) {
        // 查找符合当前癌症类型的campaigns
        const matchingCampaigns = await this.campaignRepository.findCampaignsByCancerTypeAndDate(
          cancerType,
          diagnosisDate
        );
        // 添加尚未添加的campaigns
        for (const campaign of matchingCampaigns) {
          if (!processedCampaignIds.has(campaign.Campaign_ID)) {
            allMatchingCampaigns.push(campaign);
            processedCampaignIds.add(campaign.Campaign_ID);
          }
        }
      }

      // 根据价值进行排序（高到低）
      allMatchingCampaigns.sort((a, b) => b.Value - a.Value);

      console.log(
        `[CampaignService] Found ${allMatchingCampaigns.length} unique matching campaigns for cancer types: ${cancerTypesArray.join(', ')}, date: ${diagnosisDate}`
      );

      return allMatchingCampaigns;
    } catch (error) {
      console.error('[CampaignService] Error finding matching campaigns:', error);
      throw error;
    }
  }

  async getCampaignPrompt(campaignId) {
    try {
      // Get campaign details
      const campaign = await this.campaignRepository.getCampaignById(campaignId);
      if (!campaign) {
        throw new Error(`Campaign not found: ${campaignId}`);
      }

      // Get prompt content using PromptService
      const promptId = campaign.Prompt_ID;
      const promptContent = await this.promptService.getPromptContent(promptId, '1'); // Using version 1 as default

      return {
        campaign,
        promptContent,
      };
    } catch (error) {
      console.error('[CampaignService] Error getting campaign prompt:', error);
      throw error;
    }
  }

  async createCampaignQueue(cancerTypes, diagnosisDate) {
    try {
      const cancerTypesArray = Array.isArray(cancerTypes) ? cancerTypes : [cancerTypes];
      const diagnosisDateArray = Array.isArray(diagnosisDate) ? diagnosisDate : [diagnosisDate];

      // 验证数组长度是否匹配
      if (cancerTypesArray.length !== diagnosisDateArray.length) {
        throw new Error('Cancer types and diagnosis dates arrays must have the same length');
      }

      if (cancerTypesArray.length === 0) {
        console.warn('[CampaignService] No cancer types provided');
        return [];
      }

      console.log(
        `[CampaignService] Finding campaigns for cancer types: ${cancerTypesArray.join(', ')}, dates: ${diagnosisDateArray.join(', ')}`
      );

      let allMatchingCampaigns = [];
      const processedCampaignIds = new Set();

      for (let i = 0; i < cancerTypesArray.length; i++) {
        const cancerType = cancerTypesArray[i];
        const date = diagnosisDateArray[i];

        const dateRegex = /^\d{4}-\d{2}$/;
        if (!dateRegex.test(date)) {
          throw new Error(`Invalid diagnosis date format for ${cancerType}. Expected: YYYY-MM`);
        }

        const matchingCampaigns = await this.campaignRepository.findCampaignsByCancerTypeAndDate(
          cancerType,
          date
        );

        for (const campaign of matchingCampaigns) {
          if (!processedCampaignIds.has(campaign.Campaign_ID)) {
            allMatchingCampaigns.push({
              ...campaign,
              matchedDiagnosisDate: date, // 添加匹配的诊断日期
            });
            processedCampaignIds.add(campaign.Campaign_ID);
          }
        }
      }

      // Order by the value
      allMatchingCampaigns.sort((a, b) => b.Value - a.Value);

      console.log(
        `[CampaignService] Found ${allMatchingCampaigns.length} unique matching campaigns for cancer types: ${cancerTypesArray.join(', ')}, dates: ${diagnosisDateArray.join(', ')}`
      );

      return allMatchingCampaigns;
    } catch (error) {
      console.error('[CampaignService] Error finding matching campaigns:', error);
      throw error;
    }
  }

  // Initialize Tables (to be called during server startup)
  async initializeTables() {
    try {
      await this.campaignRepository.createTablesIfNotExist();
      return { success: true, message: 'Campaign tables initialized successfully' };
    } catch (error) {
      console.error('[CampaignService] Error initializing tables:', error);
      return { success: false, message: error.message };
    }
  }
}

module.exports = { CampaignService };
