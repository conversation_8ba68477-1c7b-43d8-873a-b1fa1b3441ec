const fs = require('fs');
const path = require('path');
const OpenAI = require('openai');

require('dotenv').config();
const config = require('../../config/env');
const { GrokFunctionCallingService } = require('./GrokFunctionCallingService');
const { PromptService } = require('./PromptService');

class AzureGrokLLMService {
  constructor() {
    this.azureGrokApiKey = process.env.AZURE_GROK_API_KEY || config.azureGrok.apiKey;
    this.azureGrokBaseURL = process.env.AZURE_GROK_ENDPOINT || config.azureGrok.baseURL;
    this.assistantConfigs = {};
    this.externalDependencies = {
      EventHandler: require('../../utils/HandleFunctionCalling.js').EventHandler,
      queueAudio: null,
    };
    this.grokFunctionCallingService = new GrokFunctionCallingService();
    console.log('[AzureGrokLLMService] Initialized with API Key:', this.azureGrokApiKey);
    console.log('[AzureGrokLLMService] Base URL:', this.azureGrokBaseURL);
    this.loadPromptsSync();
    this.grok = this.getClient();
    this.promptService = new PromptService();

    // 监听默认提示文件的变化
    const promptFilePath = path.join(
      __dirname,
      '../utils/prompts/intake_fiveQuestionPreQual_prompt.txt'
    );
    fs.watch(promptFilePath, (eventType, filename) => {
      if (eventType === 'change') {
        console.log(
          '[AzureGrokLLMService] Detected change in hotel prompt file, reloading prompts...'
        );
        this.loadPromptsSync();
      }
    });

    // 监听 Simz Steak House 提示文件的变化
    const steakHousePromptFilePath = path.join(
      __dirname,
      '../utils/prompts/simz_steak_house_inbound.txt'
    );
    fs.watch(steakHousePromptFilePath, (eventType, filename) => {
      if (eventType === 'change') {
        console.log(
          '[AzureGrokLLMService] Detected change in steak house prompt file, reloading prompts...'
        );
        this.loadPromptsSync();
      }
    });
  }

  loadPromptsSync() {
    try {
      const defaultGrokPrompt = fs.readFileSync(
        path.join(__dirname, '../utils/prompts/intake_fiveQuestionPreQual_prompt.txt'),
        'utf8'
      );
      const steakHousePrompt = fs.readFileSync(
        path.join(__dirname, '../utils/prompts/simz_steak_house_inbound.txt'),
        'utf8'
      );

      this.assistantConfigs = {
        defaultGrok: {
          model: 'grok-3',
          systemPrompt: defaultGrokPrompt,
        },
        intakeGrok: {
          model: 'grok-3',
          systemPrompt: defaultGrokPrompt,
        },
        steakHouseGrok: {
          model: 'grok-3',
          systemPrompt: steakHousePrompt,
        },
      };
      console.log('[AzureGrokLLMService] Prompts loaded successfully from .txt files');
    } catch (error) {
      console.error('❗[AzureGrokLLMService] Error loading prompts from .txt files:', error);
      this.assistantConfigs = {
        defaultGrok: {
          model: 'grok-3',
          systemPrompt:
            "You are Grok, a chatbot inspired by the Hitchhiker's Guide to the Galaxy.\nProvide witty and insightful responses.",
        },
        intakeGrok: {
          model: 'grok-3',
          systemPrompt:
            'You are Grok, assisting with client intake processes.\nCollect client information and pre-qualify them for legal settlements.',
        },
        steakHouseGrok: {
          model: 'grok-3',
          systemPrompt:
            'You are an AI assistant for Simz Steak House, a high-end steakhouse specializing in premium cuts.',
        },
      };
    }
  }

  getClient() {
    return new OpenAI({
      apiKey: this.azureGrokApiKey,
      baseURL: this.azureGrokBaseURL,
      defaultHeaders: {
        'api-key': this.azureGrokApiKey,
      },
      defaultQuery: {
        'api-version': '2024-05-01-preview',
      },
    });
  }

  setDependencies(deps) {
    this.externalDependencies.EventHandler =
      deps.EventHandler || this.externalDependencies.EventHandler;
    this.externalDependencies.queueAudio = deps.queueAudio || this.externalDependencies.queueAudio;
  }

  replacePromptVariables(promptContent, leadData) {
    console.log('[AzureGrokLLMService] Replace Prompt Variables, leadData:', leadData);
    if (!leadData) return promptContent;

    let modifiedPrompt = promptContent;

    // Get Current Date
    const today = new Date();
    const todayDate = today.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });

    // Define First and Last Name
    let firstName = '';
    let lastName = '';
    if (leadData.name) {
      const nameParts = leadData.name.split(' ');
      if (nameParts.length >= 1) firstName = nameParts[0];
      if (nameParts.length >= 2) lastName = nameParts.slice(1).join(' ');
    }

    // Define the replacements
    const replacements = {
      '{{full_name}}': leadData.name || '',
      '{{today_date}}': todayDate,
      '{{first_name}}': firstName,
      '{{last_name}}': lastName,
      '{{email}}': leadData.email || '',
      '{{phone_number}}': leadData.phone || '',
      '{{campaign_type}}': leadData.campaignType || 'mass tort',
    };
    for (const [placeholder, value] of Object.entries(replacements)) {
      modifiedPrompt = modifiedPrompt.replace(new RegExp(placeholder, 'g'), value);
    }

    return modifiedPrompt;
  }

  async generateAudioFromText(text, ws, streamSid, state, callSid, broadcastCallMessages) {
    if (!text) return;

    const sentence = text.trim();
    if (sentence) {
      console.log('[AzureGrokLLMService] Generating audio for text:🟢', sentence);
      const audioFileName = `responseAudio_${Date.now()}_${Math.random()}.wav`;

      this.externalDependencies.queueAudio(
        sentence,
        audioFileName,
        ws,
        streamSid,
        state.cannotBeCutBuffer,
        callSid,
        broadcastCallMessages
      );

      if (/you may pre-qualify for a settlement since you have used one or more/i.test(sentence)) {
        state.userQualify = true;
      }
      state.assistantConfig = await this.switchAssistantBasedOnTextResponse(sentence, state);

      const openaiEndTime = Date.now();
      const openaiResponseTime = openaiEndTime - state.openaiStartTime;
      if (
        !state.textToSpeechConversionTimeMap ||
        !(state.textToSpeechConversionTimeMap instanceof Map)
      ) {
        console.error(
          '❗[AzureGrokLLMService] textToSpeechConversionTimeMap is undefined or invalid, initializing'
        );
        state.textToSpeechConversionTimeMap = new Map();
      }
      state.textToSpeechConversionTimeMap.set(audioFileName, openaiResponseTime);
      state.openaiStartTime = Date.now();
    }
  }

  async handleOpenAIResponse(
    transcript,
    ws,
    streamSid,
    state,
    previousMessages = [],
    callService,
    callSid,
    broadcastCallMessages
  ) {
    try {
      let triggerToolCall = false;
      // Check if there is requests being processing. If yes, skip the current request.
      if (state.isProcessingLLM) {
        console.log('[AzureGrokLLMService] Skip the request because it is already being processed');
        return;
      }

      let messages = [...previousMessages];
      previousMessages.push({
        role: 'user',
        content: transcript,
        timestamp: Date.now(),
        interrupted: false,
      });
      if (transcript) {
        messages.push({ role: 'user', content: transcript });
      }

      let accumulatedText = '';
      let entireResponse = '';
      const eventHandler = new this.externalDependencies.EventHandler(this.grok, ws, streamSid);

      // Enhanced tool call event handling for campaign prompts
      eventHandler.on('toolCallProcessed', async ({ toolCallName, output }) => {
        console.log('[AzureGrokLLMService] Tool call processed:', toolCallName);

        // Parse the output if it's a string
        let parsedOutput = output;
        if (typeof output === 'string') {
          try {
            parsedOutput = JSON.parse(output);
          } catch (e) {
            console.warn('[AzureGrokLLMService] Could not parse tool call output as JSON');
          }
        }

        // Handle campaign-related function results
        if (toolCallName === 'collectDiagnosisAndCreateCampaignQueue') {
          console.log(
            '[AzureGrokLLMService] Campaign queue created:',
            parsedOutput.success
              ? `Found ${parsedOutput.queue?.length || 0} campaigns for cancer types: ${parsedOutput.diagnosisInfo?.cancerTypes?.join(', ') || 'unknown'}`
              : `Failed: ${parsedOutput.message}`
          );

          if (parsedOutput.success && parsedOutput.queue?.length > 0) {
            // Campaign queue created successfully
            state.hasCampaignQueue = true;
          }
        } else if (toolCallName === 'switchToNextCampaign') {
          console.log('[AzureGrokLLMService] Switch to next campaign result:', parsedOutput);

          if (parsedOutput.systemPromptChanged) {
            console.log(
              '[AzureGrokLLMService] Prompt was switched to next campaign:',
              parsedOutput.nextCampaign?.name
            );
          }
        }

        // Continue with existing processing
        state.assistantConfig = await this.switchAssistantBasedOnFunctionCall(
          toolCallName,
          parsedOutput,
          state
        );
      });

      eventHandler.on('toolCallProcessedTextResult', async ({ textResult }) => {
        console.log('[AzureGrokLLMService] Tool call text result:', textResult);
        if (
          /you may pre-qualify for a settlement since you have used one or more/i.test(textResult)
        ) {
          state.userQualify = true;
        }
        state.assistantConfig = await this.switchAssistantBasedOnTextResponse(textResult, state);
      });

      // 根据callSid查找对应的promptType
      let promptData = state.getState('promptData');
      let leadData = state.getState('leadData');
      let systemPrompt = this.assistantConfigs.defaultGrok.systemPrompt;

      // 根据promptType选择对应的系统提示
      if (promptData) {
        if (promptData.promptType === 'simz-steakhouse') {
          console.log('[AzureGrokLLMService] Using Simz Steak House system prompt.');

          // Check if campaign queue is generated
          if (
            promptData.campaignQueue &&
            Array.isArray(promptData.campaignQueue) &&
            promptData.campaignQueue.length > 0
          ) {
            console.log(
              '[AzureGrokLLMService] Campaign queue exists, using dynamic prompt content.'
            );

            // Use dynamic prompt
            if (promptData.promptContent) {
              systemPrompt = this.replacePromptVariables(promptData.promptContent, leadData);
              console.log('[AzureGrokLLMService] Using dynamic prompt from campaign queue.');
            } else {
              console.log(
                '[AzureGrokLLMService] Campaign queue exists but no prompt content found, using default.'
              );
            }
          } else {
            // First time loading prompt
            console.log(
              '[AzureGrokLLMService] No campaign queue, fetching initial prompt from MongoDB.'
            );
            // TODO: Inbound does not work for this case
            const promptObj = await this.promptService.getPromptById(
              'a213c239-2313-47ef-a1fc-ead82891e2d6'
            );
            systemPrompt = this.replacePromptVariables(promptObj.content, leadData);
            console.log('[AzureGrokLLMService] Using initial legal intake prompt.');
          }
        } else if (promptData.promptContent) {
          console.log('[AzureGrokLLMService] Using custom prompt content.');
          systemPrompt = this.replacePromptVariables(promptData.promptContent, leadData);
        } else {
          console.log('[AzureGrokLLMService] Using default system prompt.');
        }
      } else {
        console.log('[AzureGrokLLMService] No prompt data found, using default system prompt.');
      }

      messages.unshift({ role: 'system', content: systemPrompt });

      console.log('[AzureGrokLLMService] Using API Key:', this.azureGrokApiKey);
      console.log('[AzureGrokLLMService] Using Base URL:', this.azureGrokBaseURL);
      console.log('[AzureGrokLLMService] Model:', this.assistantConfigs.defaultGrok.model);

      state.isProcessingLLM = true;
      callService.stopPlayingAudioAndClearAudioQueue(ws, state.getState('streamSid'));
      state.isGeneratingResponse = true;

      // get the tool definitions
      const tools = this.grokFunctionCallingService.getToolDefinitions();
      console.log(
        '[AzureGrokLLMService] Using tools:',
        tools.map((t) => t.function.name)
      );

      const stream = await this.grok.chat.completions.create({
        model: this.assistantConfigs.defaultGrok.model,
        messages,
        stream: true,
        tools: tools,
        tool_choice: 'auto',
        max_tokens: 2048,
        temperature: 1,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0,
      });

      for await (const chunk of stream) {
        // Check if there are tool calls being made
        // console.log('[AzureGrokLLMService] Chunk:', JSON.stringify(chunk));
        if (chunk.choices[0]?.delta?.tool_calls) {
          triggerToolCall = true;
          const toolCallDelta = chunk.choices[0].delta.tool_calls[0];
          console.log('[AzureGrokLLMService] Tool call detected:', toolCallDelta);

          // Process the full tool call
          if (
            toolCallDelta.function &&
            toolCallDelta.function.name &&
            toolCallDelta.function.arguments
          ) {
            const toolCallResult = await this.grokFunctionCallingService.processToolCall(
              toolCallDelta,
              state
            );
            console.log('[AzureGrokLLMService] Tool call result:', toolCallResult);

            // Check if this was a prompt-switching tool call
            if (
              toolCallDelta.function.name === 'switchToNextCampaign' ||
              toolCallDelta.function.name === 'collectDiagnosisAndCreateCampaignQueue'
            ) {
              try {
                const result = JSON.parse(toolCallResult.output);
                console.log('[AzureGrokLLMService] Prompt switching tool call result:', result);

                if (
                  toolCallDelta.function.name === 'collectDiagnosisAndCreateCampaignQueue' &&
                  result.success &&
                  result.queue?.length > 0
                ) {
                  // 初始化campaign队列
                  console.log(
                    '[AzureGrokLLMService] Campaign queue created with:',
                    result.queue.length,
                    'campaigns'
                  );

                  // Create Disease and campaign relation
                  const campaignRelations = result.queue
                    .filter((campaign) => campaign.clientDiseaseRelate.length > 0)
                    .map((campaign) => {
                      const diseases = campaign.clientDiseaseRelate.join(' and ');
                      return `${campaign.name} Campaign is only related to Client's ${diseases}`;
                    })
                    .join(', ');

                  const relationMessage = campaignRelations
                    ? `Client diagnosis and campaign link: ${campaignRelations}. As a legal assistant, you have to only follow the above cancer and campaign relationship. And never assume any other relationship.`
                    : 'No specific disease-campaign relationships found';
                  console.log('[GrokLLMService] Campaign relation message:', relationMessage);

                  // Add system message to message history
                  previousMessages.push({
                    role: 'system',
                    content: relationMessage,
                  });

                  const promptData = state.getState('promptData') || {};
                  console.log('[AzureGrokLLMService] Checking prompt:', promptData);

                  promptData.campaignQueue = result.queue;
                  promptData.currentCampaignIndex = 0;

                  // Update PromptData
                  state.updateState('promptData', promptData);

                  const currentCampaign = result.queue[0];
                  const promptId = currentCampaign.promptId;

                  try {
                    const PromptService = require('./PromptService').PromptService;
                    const promptService = new PromptService();

                    const latestPromptData = await promptService.getLatestVersion(promptId);

                    promptData.promptId = promptId;
                    promptData.promptVersion = latestPromptData.version;
                    promptData.promptContent = latestPromptData.content;
                    state.updateState('promptData', promptData);

                    console.log(
                      `[AzureGrokLLMService] Updated prompt to campaign ${currentCampaign.name}, promptId: ${promptId}, version: ${latestPromptData.version}`
                    );

                    // update grok prompt
                    messages[0] = { role: 'system', content: latestPromptData.content };
                  } catch (error) {
                    console.error('[AzureGrokLLMService] Error fetching prompt:', error);
                  }
                } else if (
                  toolCallDelta.function.name === 'switchToNextCampaign' &&
                  result.systemPromptChanged
                ) {
                  // 切换到下一个campaign
                  console.log('[AzureGrokLLMService] Switching to next campaign');

                  const promptData = state.getState('promptData') || {};
                  console.log('[GrokLLMService] Checking prompt:', promptData);
                  // 检查是否有campaignQueue
                  if (
                    !promptData.campaignQueue ||
                    !Array.isArray(promptData.campaignQueue) ||
                    promptData.campaignQueue.length === 0
                  ) {
                    console.log('[AzureGrokLLMService] No campaign queue available to switch');
                  } else {
                    // 更新当前指针，移至下一个campaign
                    if (promptData.currentCampaignIndex === undefined) {
                      promptData.currentCampaignIndex = 0;
                    } else {
                      promptData.currentCampaignIndex++;
                    }
                    console.log(
                      '[AzureGrokLLMService] Current campaign index:',
                      promptData.currentCampaignIndex
                    );

                    // 检查是否超出队列范围
                    if (promptData.currentCampaignIndex >= promptData.campaignQueue.length) {
                      console.log('[AzureGrokLLMService] Reached end of campaign queue');
                    } else {
                      // 获取当前campaign
                      const currentCampaign =
                        promptData.campaignQueue[promptData.currentCampaignIndex];
                      const promptId = currentCampaign.promptId;

                      try {
                        // 加载PromptService来获取最新版本的prompt
                        const PromptService = require('./PromptService').PromptService;
                        const promptService = new PromptService();

                        // 获取最新版本的prompt内容
                        const latestPromptData = await promptService.getLatestVersion(promptId);

                        // 更新promptData中的信息
                        promptData.promptId = promptId;
                        promptData.promptVersion = latestPromptData.version;
                        promptData.promptContent = latestPromptData.content;

                        // Update PromptData
                        state.updateState('promptData', promptData);

                        console.log(
                          `[AzureGrokLLMService] Switched to campaign ${currentCampaign.name}, promptId: ${promptId}, version: ${latestPromptData.version}`
                        );

                        // 更新系统提示
                        messages[0] = { role: 'system', content: latestPromptData.content };
                        console.log(
                          '[AzureGrokLLMService] Updated system prompt with new prompt:',
                          latestPromptData.content
                        );
                      } catch (error) {
                        console.error('[AzureGrokLLMService] Error fetching prompt:', error);
                      }
                    }
                  }
                }
              } catch (error) {
                console.error(
                  `[AzureGrokLLMService] Error parsing ${toolCallDelta.function.name} result:`,
                  error
                );
              }
            }

            // Add the tool call result to the messages
            messages.push({
              role: 'tool',
              tool_call_id: toolCallDelta.id,
              content: toolCallResult.output,
            });

            previousMessages.push({
              role: 'tool',
              tool_call_id: toolCallDelta.id,
              content: toolCallResult.output,
            });
          }
          continue;
        }

        // Process regular text responses
        const delta = chunk.choices[0]?.delta?.content;
        if (delta) {
          accumulatedText += delta;
          if (/[.!?]/.test(delta)) {
            const sentence = accumulatedText.trim();
            entireResponse += sentence + ' ';
            accumulatedText = '';
            await this.generateAudioFromText(
              sentence,
              ws,
              streamSid,
              state,
              callSid,
              broadcastCallMessages
            );
          }
        }
      }

      if (entireResponse) {
        messages.push({
          role: 'assistant',
          content: entireResponse,
          timestamp: Date.now(),
          interrupted: false,
        });
        previousMessages.push({
          role: 'assistant',
          content: entireResponse,
          timestamp: Date.now(),
          interrupted: false,
        });
      }

      state.currentRunId = `azure_grok_run_${Date.now()}`;
      console.log('[AzureGrokLLMService] Stream ended, run ID updated:', state.currentRunId);

      if (triggerToolCall) {
        // 如果triggerToolCall为true，则需要处理tool call

        console.log('[AzureGrokLLMService] Trigger tool call, processing tool call result');
        let entireResponseAfterToolCall = '';
        // Use the updated messages to continue the conversation
        const response = await this.grok.chat.completions.create({
          model: this.assistantConfigs.defaultGrok.model,
          messages,
          tools: tools,
          tool_choice: 'auto',
        });

        console.log(
          '[AzureGrokLLMService] Grok response after tool call:',
          response.choices[0].message.content
        );

        // 处理tool call后的response
        const toolCallResponse = response.choices[0].message.content;
        if (toolCallResponse) {
          // 按句子分割文本
          const sentences = toolCallResponse.split(/(?<=[.!?])\s+/);
          for (const sentence of sentences) {
            if (sentence.trim()) {
              entireResponseAfterToolCall += sentence + ' ';
              await this.generateAudioFromText(
                sentence,
                ws,
                streamSid,
                state,
                callSid,
                broadcastCallMessages
              );
            }
          }
        }
        previousMessages.push({
          role: 'assistant',
          content: entireResponseAfterToolCall,
          timestamp: Date.now(),
          interrupted: false,
        });
        triggerToolCall = false;
      }

      state.isProcessingLLM = false;
      return { id: state.currentRunId, messages: previousMessages };
    } catch (error) {
      console.error('❗[AzureGrokLLMService] Error handling Azure Grok API:', error);
      state.isProcessingLLM = false;
      throw error;
    }
  }

  async createAssistantMessage(content, state) {
    state.messages = state.messages || [];
    state.messages.push({ role: 'assistant', content: content });
    return state.messages;
  }

  async createUserMessage(content, state) {
    state.messages = state.messages || [];
    state.messages.push({ role: 'user', content: content });
    return state.messages;
  }

  async createRun(threadId, assistantConfig) {
    console.log(`Simulating run creation for Azure Grok with config:`, assistantConfig);
    return { id: `azure_grok_run_${Date.now()}` };
  }

  async cancelRunWithTimeout(threadId, runId, timeoutMs, retryIntervalMs, ws, streamSid, state) {
    console.log(
      `Simulating cancellation of run ${runId}. Azure Grok streaming cannot be cancelled mid-stream.`
    );
    const newRunId = `azure_grok_run_${Date.now()}`;
    state.currentRunId = newRunId;
    await this.handleOpenAIResponse('', ws, streamSid, state, state.messages || []);
    return { status: 'cancelled', id: newRunId };
  }

  async waitForRunCompletion(threadId, runId) {
    console.log(`Simulating wait for run ${runId} completion (Azure Grok doesn't require this).`);
  }

  async switchAssistantBasedOnFunctionCall(toolCallName, output, state) {
    console.log('[AzureGrokLLMService] Switching assistant based on function call:', toolCallName);

    let success = false;
    if (typeof output === 'object') {
      success = output.success;
    } else if (output === true) {
      success = true;
    }

    if (success) {
      console.log('[AzureGrokLLMService] Tool call was successful');
      state.userQualify = true;
    }

    // Handle campaign-related tools specifically
    if (toolCallName === 'collectDiagnosisAndCreateCampaignQueue') {
      return this.assistantConfigs.intakeGrok;
    } else if (toolCallName === 'switchToNextCampaign') {
      return null; // No need to change assistantConfig as the prompt content will be updated directly
    }

    // Handle other tools
    switch (toolCallName) {
      case 'check_cancer_qualification':
        return success ? this.assistantConfigs.intakeGrok : this.assistantConfigs.defaultGrok;
      case 'check_medical_qualification':
        return success ? this.assistantConfigs.intakeGrok : this.assistantConfigs.defaultGrok;
      case 'check_accident_qualification':
        return this.assistantConfigs.intakeGrok;
      default:
        console.log('Unknown tool call');
        return null;
    }
  }

  async switchAssistantBasedOnTextResponse(testResponse, state) {
    // const lowerCaseResponse = testResponse.toLowerCase();
    // let newAssistantConfig = state.assistantConfig || this.assistantConfigs.defaultGrok;
    // if (/port catheter|hernia mesh/.test(lowerCaseResponse)) {
    //   console.log('Switching to Intake Grok for Medical');
    //   newAssistantConfig = this.assistantConfigs.intakeGrok;
    // } else if (/car accident/.test(lowerCaseResponse)) {
    //   console.log('Switching to Intake Grok for Accident');
    //   newAssistantConfig = this.assistantConfigs.intakeGrok;
    // } else if (/you do indeed pre-qualify|you are pre-qualified/.test(lowerCaseResponse)) {
    //   console.log('User pre-qualified, keeping Intake Grok');
    //   newAssistantConfig = this.assistantConfigs.intakeGrok;
    // } else if (/i need to ask you some additional questions|unfortunately.*not qualify|I'm sorry, but based on/i.test(lowerCaseResponse)) {
    //   console.log('User might not qualify, checking if we need to switch campaigns');
    //   if (state.hasCampaignQueue) {
    //     // If there's a negative response and we have a campaign queue,
    //     // this might be a good time to use switchToNextCampaign
    //     console.log('[AzureGrokLLMService] May need to switch campaigns based on text response');
    //   }
    // }
    // return newAssistantConfig;
  }
}

module.exports = { AzureGrokService: AzureGrokLLMService };
