const OpenAI = require('openai');
const { SalesforceService } = require('../../salesforce/services/SalesforceService');
require('dotenv').config();

class SalesforceProcessorService {
  constructor(callService) {
    this.openaiClient = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    this.salesforceService = new SalesforceService();
    this.callService = callService;

    // Initialize global message history storage if it doesn't exist
    if (!global.messageHistory) {
      global.messageHistory = [];
    }

    console.log('[SalesforceProcessorService] Initialized');
  }

  /**
   * Store conversation messages to global history for processing
   * @param {Array} messages - Array of conversation messages
   * @param {string} callSid - Twilio call SID for reference
   */
  storeConversationHistory(messages, callSid) {
    const historyEntry = {
      callSid,
      messages,
      timestamp: Date.now(),
      processed: false,
    };

    global.messageHistory.push(historyEntry);
    console.log(
      `[SalesforceProcessorService] Stored conversation history for callSid: ${callSid}, total messages: ${messages.length}`
    );
  }

  /**
   * Process conversation with second <PERSON> to extract Salesforce data
   * @param {Array} messages - Conversation messages
   * @param {Object} callState - Call state object
   * @returns {Promise<Object>} - Extracted Salesforce data including basic customer info
   */
  async processConversationForSalesforce(messages, callState) {
    try {
      const systemPrompt = `You are an AI assistant specialized in analyzing legal intake conversations to extract structured data for Salesforce Lead records.

Your task is to analyze the conversation and extract the following information in JSON format:

BASIC CUSTOMER INFO:
- customerName: Full customer name extracted from conversation
- customerPhone: Phone number if mentioned (format: +**********)
- customerEmail: Email address if mentioned
- qualificationInfo: Summary of qualification details discussed (max 500 chars)

REQUIRED SALESFORCE FIELDS:
- clientQualifiedCampaign: Campaign type, only choose from: Roundup, PFAS, Depo Birth Control, Paraquat, Hernia Mesh, Talcum, NEC, MVA, Catheter. If user maybe qualified for multiple campaigns, choose the most likely one.
- clientDiagnosis: Specific diagnosis from the provided enum list, only select one: 
          enum: [
                  'Adult T-cell lymphoma/leukemia',
                  'Aggressive NK-cell leukemia',
                  'Anaplastic Large Cell Lymphoma',
                  'Anaplastic large T/null-cell lymphoma',
                  'B-Cell Lymphoma',
                  'B-lymphoblastic leukemia',
                  'B-lymphoblastic lymphoma',
                  'B-prolymphocytic leukemia',
                  'Benign',
                  'Blastic mantle cell lymphoma',
                  'Blastic plasmacytoid dendritic cell neoplasm',
                  'Burkitt lymphoma',
                  'Central Nervous System Lymphoma',
                  'Cerebral Meningioma',
                  'Chronic lymphocytic leukemia',
                  'Composite lymphoma',
                  'Cutaneous CD4-positive small/medium T-cell lymphoma',
                  'Cutaneous CD8-positive aggressive epidermotropic cytotoxic T-cell lymphoma',
                  'Cutaneous Marginal Zone Lymphoma',
                  'Dendritic/histiocytic neoplasm',
                  'Diffuse Mixed Cell Lymphoma',
                  'Diffuse follicular lymphoma',
                  'Diffuse large B-cell lymphoma',
                  'Diffuse mantle cell lymphoma',
                  'EBV-driven lymphoproliferative disorder',
                  'Enteropathy-type T-cell lymphoma',
                  'Extranodal marginal zone B-cell Lymphoma',
                  'Follicular lymphoma',
                  'GI',
                  'GI Injury',
                  'Gastrointestinal injury',
                  'Hairy Cell Lymphoma or Leukemia',
                  'Hepatosplenic T-cell lymphoma',
                  'Histiocytic Lymphoma',
                  'Kidney Cancer',
                  'Langherns cell histiocytosis',
                  'Liver Cancer',
                  'Lymphoblastic Lymphoma',
                  'Lymphoma',
                  'Lymphomatoid granulomatosis',
                  'Lymphoplasmacytic lymphoma',
                  'Malignant',
                  'Malignant lymphoma unclassifiable',
                  'Mantle cell lymphoma',
                  'Mast cell disease',
                  'Meningioma',
                  'Mucosa-Associated Lymphoid Tissue Lymphoma',
                  'Mycosis fungoides',
                  'NEC',
                  'NK-cell granular lymphocytic proliferation',
                  'Nasal NK-T-cell lymphoma',
                  'Necrotizing Enterocolitis',
                  'Nodal marginal zone B-cell lymphoma',
                  'Nodular mantle cell lymphoma',
                  "Non-Hodgkin's Lymphoma",
                  'Ovarian',
                  'Ovarian Cancer',
                  "Parkinson's Disease",
                  'Peripheral T-cell lymphoma',
                  'Peripheral gamma-delta T-cell lymphoma',
                  'Plasma cell dyscrasia',
                  'Plasmacytoma',
                  'Post-transplantation lymphoproliferative disorder',
                  'Primary Central Nervous System Lymphoma',
                  'Primary cutaneous CD30-positive lymphoproliferative disorder',
                  'Renal Cell Carcinoma',
                  'SBS',
                  'Sezary syndrome',
                  'Short Bowel Syndrome',
                  'Small lymphocytic lymphoma',
                  'Splenic diffuse red pulp small B-cell lymphoma',
                  'Splenic marginal zone B-cell lymphoma',
                  'Subcutaneous panniculitis-like T-cell lymphoma',
                  'T-Cell Lymphoma',
                  'T-cell granular lymphocytic proliferation',
                  'T-cell lymphoproliferative disorder of childhood, Epstein-Barr virus positive',
                  'T-lymphoblastic leukemia',
                  'T-lymphoblastic lymphoma',
                  'T-prolyrnphocytic leukemia',
                  'Testicular Cancer',
                  'Ulcerative Colitis',
                  'Unclassifieable B-cell lymphoma',
                  'Waldenstrom Macroglobulinemia',
                ],
- clientDiagnosisDate: Date in MM/DD/YYYY format
- clientPersonallyPrepareHandleProduct: "Yes" or "No"
- clientUnderstandFirmWillVerifyDiagnosis: "Yes" or "No"  
- clientEvidenceTypeForYourInjury: Type of evidence available, choose from the list of evidence type options:  
                  'Can get from doctor',
                  'Clinical Evaluation',
                  'Death Certificate',
                  'Imaging Scan',
                  'Medical Bills',
                  'Medical Certificate',
                  'Medical Devices or Assistive Equipment',
                  'Medical Records',
                  'Not Applicable',
                  'Pathology Report',
                  "Physician's Note or Letter",
                  'Prescriptions',
                  'Treatment Documentation',
- client1stExposed: First exposure date in MM/DD/YYYY format. If date is not provided, put not provided. But if provided, you want the format to be MM/DD/YYYY.
- clientLastExposed: Last exposure date in MM/DD/YYYY format. If date is not provided, put not provided. But if provided, you want the format to be MM/DD/YYYY.
- otherDiagnosis: Other diagnosis, choose from the list of clientDiagnosis options

DIAGNOSIS OPTIONS:
Adult T-cell lymphoma/leukemia, Aggressive NK-cell leukemia, Anaplastic Large Cell Lymphoma, Anaplastic large T/null-cell lymphoma, B-Cell Lymphoma, B-lymphoblastic leukemia, B-lymphoblastic lymphoma, B-prolymphocytic leukemia, Benign, Blastic mantle cell lymphoma, Blastic plasmacytoid dendritic cell neoplasm, Burkitt lymphoma, Central Nervous System Lymphoma, Cerebral Meningioma, Chronic lymphocytic leukemia, Composite lymphoma, Cutaneous CD4-positive small/medium T-cell lymphoma, Cutaneous CD8-positive aggressive epidermotropic cytotoxic T-cell lymphoma, Cutaneous Marginal Zone Lymphoma, Dendritic/histiocytic neoplasm, Diffuse Mixed Cell Lymphoma, Diffuse follicular lymphoma, Diffuse large B-cell lymphoma, Diffuse mantle cell lymphoma, EBV-driven lymphoproliferative disorder, Enteropathy-type T-cell lymphoma, Extranodal marginal zone B-cell Lymphoma, Follicular lymphoma, GI, GI Injury, Gastrointestinal injury, Hairy Cell Lymphoma or Leukemia, Hepatosplenic T-cell lymphoma, Histiocytic Lymphoma, Kidney Cancer, Langherns cell histiocytosis, Liver Cancer, Lymphoblastic Lymphoma, Lymphoma, Lymphomatoid granulomatosis, Lymphoplasmacytic lymphoma, Malignant, Malignant lymphoma unclassifiable, Mantle cell lymphoma, Mast cell disease, Meningioma, Mucosa-Associated Lymphoid Tissue Lymphoma, Mycosis fungoides, NEC, NK-cell granular lymphocytic proliferation, Nasal NK-T-cell lymphoma, Necrotizing Enterocolitis, Nodal marginal zone B-cell lymphoma, Nodular mantle cell lymphoma, Non-Hodgkin's Lymphoma, Ovarian, Ovarian Cancer, Parkinson's Disease, Peripheral T-cell lymphoma, Peripheral gamma-delta T-cell lymphoma, Plasma cell dyscrasia, Plasmacytoma, Post-transplantation lymphoproliferative disorder, Primary Central Nervous System Lymphoma, Primary cutaneous CD30-positive lymphoproliferative disorder, Renal Cell Carcinoma, SBS, Sezary syndrome, Short Bowel Syndrome, Small lymphocytic lymphoma, Splenic diffuse red pulp small B-cell lymphoma, Splenic marginal zone B-cell lymphoma, Subcutaneous panniculitis-like T-cell lymphoma, T-Cell Lymphoma, T-cell granular lymphocytic proliferation, T-cell lymphoproliferative disorder of childhood, Epstein-Barr virus positive, T-lymphoblastic leukemia, T-lymphoblastic lymphoma, T-prolyrnphocytic leukemia, Testicular Cancer, Ulcerative Colitis, Unclassifieable B-cell lymphoma, Waldenstrom Macroglobulinemia

EVIDENCE TYPE OPTIONS:
Can get from doctor, Clinical Evaluation, Death Certificate, Imaging Scan, Medical Bills, Medical Certificate, Medical Devices or Assistive Equipment, Medical Records, Not Applicable, Pathology Report, Physician's Note or Letter, Prescriptions, Treatment Documentation

Return ONLY a JSON object with the extracted data. If information is not available, use "Not provided" for string fields.

Example response:
{
  "customerName": "John Smith",
  "customerPhone": "+**********", 
  "customerEmail": "<EMAIL>",
  "qualificationInfo": "Customer used Roundup for 10 years, diagnosed with Non-Hodgkin's Lymphoma in 2020",
  "clientQualifiedCampaign": "Roundup",
  "clientDiagnosis": "Non-Hodgkin's Lymphoma", 
  "clientDiagnosisDate": "03/15/2020",
  "clientPersonallyPrepareHandleProduct": "Yes",
  "clientUnderstandFirmWillVerifyDiagnosis": "Yes",
  "clientEvidenceTypeForYourInjury": "Medical Records",
  "client1stExposed": "01/01/2010",
  "clientLastExposed": "12/31/2019"
}`;

      const conversationText = messages.map((msg) => `${msg.role}: ${msg.content}`).join('\n');

      const requestMessages = [
        { role: 'system', content: systemPrompt },
        {
          role: 'user',
          content: `Analyze this conversation and extract all customer and Salesforce data:\n\n${conversationText}`,
        },
      ];

      console.log('[SalesforceProcessorService] Sending conversation to AI for analysis...');

      const response = await this.openaiClient.chat.completions.create({
        model: 'gpt-4',
        messages: requestMessages,
        temperature: 0.1,
        max_tokens: 2000,
      });

      const extractedData = JSON.parse(response.choices[0].message.content);
      console.log('[SalesforceProcessorService] Extracted data from AI:', extractedData);

      return extractedData;
    } catch (error) {
      console.error('[SalesforceProcessorService] Error processing conversation:', error);
      // Return default values if processing fails
      let fallbackPhone = 'Not provided';
      if (callState) {
        const leadData = callState.getState('leadData') || {};
        fallbackPhone = leadData.customerPhone || 'Not provided';
      }
      return {
        customerName: 'Unknown Customer',
        customerPhone: fallbackPhone,
        customerEmail: 'Not provided',
        qualificationInfo: 'Processing failed - data extraction error',
        clientQualifiedCampaign: 'Not provided',
        clientDiagnosis: 'Not provided',
        clientDiagnosisDate: 'Not provided',
        clientPersonallyPrepareHandleProduct: 'No',
        clientUnderstandFirmWillVerifyDiagnosis: 'No',
        clientEvidenceTypeForYourInjury: 'Not Applicable',
        client1stExposed: 'Not provided',
        clientLastExposed: 'Not provided',
      };
    }
  }

  /**
   * Process warm transfer and update Salesforce asynchronously
   * @param {string} callSid - Twilio call SID
   */
  async processWarmTransferAsync(callSid) {
    try {
      console.log(
        '[SalesforceProcessorService] Starting async Salesforce processing for callSid:',
        callSid
      );

      // Find conversation history for this call
      const historyEntry = global.messageHistory?.find((entry) => entry.callSid === callSid);
      if (!historyEntry) {
        console.warn(
          '[SalesforceProcessorService] No conversation history found for callSid:',
          callSid
        );
        return;
      }

      // Get callState by callSid using the injected callService
      const callState = this.callService.getCallStateByCallSid(callSid);

      console.log(
        '[SalesforceProcessorService] Found history entry with',
        historyEntry.messages?.length || 0,
        'messages'
      );

      // Process conversation with AI to extract all data including customer info
      const extractedData = await this.processConversationForSalesforce(
        historyEntry.messages,
        callState
      );

      // Prepare complete lead data for Salesforce
      let leadData = callState ? callState.getState('leadData') || {} : {};
      leadData = {
        ...leadData,
        First_Name__c: extractedData.customerName.split(' ')[0] || 'Unknown',
        Last_Name__c:
          extractedData.customerName.split(' ')[1] ||
          extractedData.customerName.split(' ')[0] ||
          'Unknown',
        LastName:
          extractedData.customerName.split(' ')[1] ||
          extractedData.customerName.split(' ')[0] ||
          'Unknown',
        FirstName: extractedData.customerName.split(' ')[0] || 'Unknown',
        // Email: extractedData.customerEmail || '',
        Phone: extractedData.customerPhone || leadData.customerPhone,
        Campaign_Type__c: extractedData.clientQualifiedCampaign,
        Diagnosis__c: extractedData.clientDiagnosis,
        Other_Cancer_Type__c: extractedData.otherDiagnosis,
        Date_Diagnosed_Injury_Date__c: extractedData.clientDiagnosisDate,
        Personally_prepare_handle_product__c: extractedData.clientPersonallyPrepareHandleProduct,
        Understand_firm_will_verify_claim_dx__c:
          extractedData.clientUnderstandFirmWillVerifyDiagnosis,
        Evidence_type_for_your_injury__c: extractedData.clientEvidenceTypeForYourInjury,
        When_1st_exposed_used__c: extractedData.client1stExposed,
        When_last_exposed_used__c: extractedData.clientLastExposed,
        SimZ_Notes__c: extractedData.qualificationInfo || 'Transfer call from AI agent',
        Status: 'Open - Not Contacted',
        LeadSource: 'SimZ AI',
      };
      if (callState) callState.updateState('leadData', leadData);

      console.log('[SalesforceProcessorService] Prepared lead data:', leadData);

      // Create or update Salesforce lead
      const salesforceService =
        new (require('../../salesforce/services/SalesforceService').SalesforceService)();
      await salesforceService.connect();

      // Check if lead exists by phone number
      const existingLead = await salesforceService.findLeadByPhoneNumber(
        extractedData.customerPhone,
        callState
      );

      if (existingLead && existingLead.Id) {
        // Update existing lead
        const leadId = existingLead.Id;
        console.log('[SalesforceProcessorService] Updating existing Salesforce lead:', leadId);
        await salesforceService.updateRecord('Lead', leadData, leadId);
        console.log('[SalesforceProcessorService] Updated existing Salesforce lead:', leadId);
      } else {
        // Create new lead
        const result = await salesforceService.createRecord('Lead', leadData);
        console.log('[SalesforceProcessorService] Created new Salesforce lead:', result.id);
      }

      await salesforceService.disconnect();
      console.log(
        '[SalesforceProcessorService] Salesforce processing completed successfully for callSid:',
        callSid
      );

      // Mark this conversation as processed
      historyEntry.processed = true;
    } catch (error) {
      console.error('[SalesforceProcessorService] Error in async Salesforce processing:', error);
      throw error;
    }
  }

  /**
   * Clean up old processed conversation histories
   * @param {number} maxAgeMs - Maximum age in milliseconds (default: 1 hour)
   */
  cleanupMessageHistory(maxAgeMs = 3600000) {
    const now = Date.now();
    const initialLength = global.messageHistory.length;

    global.messageHistory = global.messageHistory.filter(
      (entry) => now - entry.timestamp < maxAgeMs
    );

    const removedCount = initialLength - global.messageHistory.length;
    if (removedCount > 0) {
      console.log(
        `[SalesforceProcessorService] Cleaned up ${removedCount} old conversation histories`
      );
    }
  }
}

module.exports = { SalesforceProcessorService };
