const OpenAI = require('openai');
const fs = require('fs');
const path = require('path');
require('dotenv').config();
const config = require('../../config/env');

/**
 * Grok API Service for frontend chat interaction
 */
class GrokAPIService {
  constructor() {
    this.grokApiKey = process.env.GROK_API_KEY || config.grok.apiKey;
    this.grokBaseURL =
      process.env.GROK_API_BASE_URL || config.grok.baseURL || 'https://api.x.ai/v1';
    this.promptFilePath = path.join(
      __dirname,
      '../utils/prompts/intake_fiveQuestionPreQual_prompt.txt'
    );
    this.loadInitialPrompt();
    if (!this.grokApiKey) {
      throw new Error('GROK_API_KEY is not configured');
    }
    this.client = this.getClient();
    this.chatHistory = [];
  }

  getClient() {
    return new OpenAI({
      apiKey: this.grokApiKey,
      baseURL: this.grokBaseURL,
    });
  }

  loadInitialPrompt() {
    try {
      this.systemPrompt = fs.readFileSync(this.promptFilePath, 'utf8');
    } catch (error) {
      console.error('❗[GrokAPIService] Failed to load initial Prompt:', error);
      this.systemPrompt = 'You are a helpful assistant, assisting users with their questions.';
    }
  }

  async savePrompt(prompt) {
    try {
      fs.writeFileSync(this.promptFilePath, prompt, 'utf8');
      this.systemPrompt = prompt;
      return { message: 'Prompt saved successfully' };
    } catch (error) {
      console.error('❗[GrokAPIService] Failed to save Prompt:', error);
      throw new Error('Failed to save Prompt');
    }
  }

  async getPrompt() {
    return this.systemPrompt;
  }

  async chatWithGrokSystemPrompt(userMessage, systemPrompt) {
    try {
      this.chatHistory.push({ role: 'user', content: userMessage });
      const messages = [
        { role: 'system', content: systemPrompt || this.systemPrompt },
        ...this.chatHistory,
      ];

      const response = await this.client.chat.completions.create({
        model: 'grok-3-beta',
        // model: 'grok-2-latest',
        messages,
        stream: false,
      });

      const grokResponse = response.choices[0].message.content;
      this.chatHistory.push({ role: 'assistant', content: grokResponse });
      return { response: grokResponse };
    } catch (error) {
      console.error('❗[GrokAPIService] Failed to interact with Grok:', error);
      throw new Error('Failed to interact with Grok');
    }
  }

  async chatWithGrok(messageWithPrompt) {
    try {
      this.chatHistory.push({ role: 'user', content: messageWithPrompt });
      const messages = [{ role: 'system', content: this.systemPrompt }, ...this.chatHistory];

      const response = await this.client.chat.completions.create({
        model: 'grok-3-beta',
        // model: 'grok-2-latest',
        messages,
        stream: false,
      });

      const grokResponse = response.choices[0].message.content;
      this.chatHistory.push({ role: 'assistant', content: grokResponse });
      return { response: grokResponse };
    } catch (error) {
      console.error('❗[GrokAPIService] Failed to interact with Grok:', error.message);
      throw new Error('Failed to interact with Grok');
    }
  }

  async clearChatHistory() {
    try {
      this.chatHistory = [];
      return { success: true, message: 'Chat history cleared successfully' };
    } catch (error) {
      console.error('❗[GrokAPIService] Failed to clear chat history:', error);
      throw new Error('Failed to clear chat history');
    }
  }
}

module.exports = { GrokAPIService };
