// llm-integration/services/PromptService.js
const { PromptRepository } = require('../repositories/PromptRepository');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');

class PromptService {
  constructor() {
    this.promptRepository = new PromptRepository();
  }

  async getInboundPrompt() {
    const promptFilePath = path.join(
      __dirname,
      '../utils/prompts/intake_fiveQuestionPreQual_prompt.txt'
    );
    try {
      const prompt = fs.readFileSync(promptFilePath, 'utf8');
      return prompt;
    } catch (error) {
      console.error('[PromptService] Error reading inbound prompt file:', error);
      throw error;
    }
  }

  async saveInboundPrompt(prompt) {
    const promptFilePath = path.join(
      __dirname,
      '../utils/prompts/intake_fiveQuestionPreQual_prompt.txt'
    );
    try {
      fs.writeFileSync(promptFilePath, prompt, 'utf8');
      return { message: 'Inbound prompt saved successfully' };
    } catch (error) {
      console.error('[PromptService] Error saving inbound prompt file:', error);
      throw error;
    }
  }

  // 获取Simz Steak House的prompt
  async getSimzSteakHousePrompt() {
    const promptFilePath = path.join(__dirname, '../utils/prompts/simz_steak_house_inbound.txt');
    try {
      const prompt = fs.readFileSync(promptFilePath, 'utf8');
      return prompt;
    } catch (error) {
      console.error('[PromptService] Error reading Simz Steak House prompt file:', error);
      throw error;
    }
  }

  // 保存Simz Steak House的prompt
  async saveSimzSteakHousePrompt(prompt) {
    const promptFilePath = path.join(__dirname, '../utils/prompts/simz_steak_house_inbound.txt');
    try {
      fs.writeFileSync(promptFilePath, prompt, 'utf8');
      return { message: 'Simz Steak House prompt saved successfully' };
    } catch (error) {
      console.error('[PromptService] Error saving Simz Steak House prompt file:', error);
      throw error;
    }
  }

  async getPromptById(promptId) {
    if (!promptId) throw new Error('promptId is required');
    const prompt = await this.promptRepository.getPromptById(promptId);
    if (!prompt) throw new Error('Prompt not found');

    const [versions] = await this.promptRepository.pool.execute(
      'SELECT mongo_prompt_id FROM Prompt_Versions WHERE Prompt_ID = ? ORDER BY Created_At DESC LIMIT 1',
      [promptId]
    );
    if (versions.length > 0) {
      const mongoPrompt = await this.promptRepository.getPromptFromMongo(
        versions[0].mongo_prompt_id
      );
      return { ...prompt, ...mongoPrompt._doc };
    }
    return prompt;
  }

  async savePrompt({ promptId, name, content, provider, version, userId }) {
    if (!name || !content || !userId) throw new Error('name, content, and userId are required');
    const id = promptId || uuidv4();
    const versionId = uuidv4();
    const safeVersion = version || '1';

    const existingPrompt = await this.promptRepository.getPromptById(id);
    let mongoPromptId;

    let versions = [];
    if (existingPrompt) {
      try {
        versions = JSON.parse(existingPrompt.Versions);
        if (!Array.isArray(versions)) {
          console.warn(
            `[PromptService] Invalid Versions format for Prompt_ID ${id}, resetting to array`
          );
          versions = [];
        }
      } catch (error) {
        console.warn(
          `[PromptService] Failed to parse Versions for Prompt_ID ${id}: ${error.message}, resetting to array`
        );
        versions = [];
      }

      const [existingVersion] = await this.promptRepository.pool.execute(
        'SELECT mongo_prompt_id FROM Prompt_Versions WHERE Prompt_ID = ? AND Version = ?',
        [id, safeVersion]
      );

      if (existingVersion.length > 0) {
        // Update existing version
        mongoPromptId = existingVersion[0].mongo_prompt_id;
        await this.promptRepository.updatePromptInMongo(mongoPromptId, {
          content,
          provider,
          version: safeVersion,
        });
      } else {
        // Create new version
        mongoPromptId = await this.promptRepository.savePromptToMongo({
          content,
          provider,
          version: safeVersion,
        });
        versions.push(versionId);
        await this.promptRepository.updatePromptVersions(id, versions);
        await this.promptRepository.createPromptVersion({
          versionId,
          promptId: id,
          version: safeVersion,
          mongoPromptId,
        });
      }
    } else {
      // Create new Prompt
      mongoPromptId = await this.promptRepository.savePromptToMongo({
        content,
        provider,
        version: safeVersion,
      });
      await this.promptRepository.createPrompt(id, name, userId);
      versions = [versionId];
      await this.promptRepository.updatePromptVersions(id, versions);
      await this.promptRepository.createPromptVersion({
        versionId,
        promptId: id,
        version: safeVersion,
        mongoPromptId,
      });
    }

    return { promptId: id, versionId, version: safeVersion, mongoPromptId, name };
  }

  async getAllPrompts() {
    const [rows] = await this.promptRepository.pool.execute(
      'SELECT Prompt_ID, Prompt_Name, Versions FROM Prompt_Store'
    );
    return rows;
  }

  async getPromptsByCompanyId(companyId) {
    const [rows] = await this.promptRepository.pool.execute(
      'SELECT Prompt_ID, Prompt_Name, Versions FROM Prompt_Store WHERE Company_ID = ?', [companyId]
    );
    return rows;
  }

  async getPromptVersions(promptId) {
    const [versions] = await this.promptRepository.pool.execute(
      'SELECT Version_ID, Version, mongo_prompt_id FROM Prompt_Versions WHERE Prompt_ID = ? ORDER BY Created_At DESC',
      [promptId]
    );
    return versions;
  }

  async getPromptFromMongo(mongoPromptId) {
    return await this.promptRepository.getPromptFromMongo(mongoPromptId);
  }

  // Get Prompt content by promptId and promptVersion for outbound calls
  async getPromptContent(promptId, promptVersion) {
    try {
      if (!promptId || !promptVersion) {
        throw new Error('promptId and promptVersion are required');
      }
      const [versions] = await this.promptRepository.pool.execute(
        'SELECT mongo_prompt_id FROM Prompt_Versions WHERE Prompt_ID = ? AND Version = ?',
        [promptId, promptVersion]
      );
      if (versions.length === 0) {
        throw new Error(`No version ${promptVersion} found for Prompt_ID ${promptId}`);
      }
      const mongoPrompt = await this.promptRepository.getPromptFromMongo(
        versions[0].mongo_prompt_id
      );
      if (!mongoPrompt) {
        throw new Error('Prompt content not found in MongoDB');
      }
      return mongoPrompt.content;
    } catch (error) {
      console.error('[PromptService] Error getting prompt content:', error);
      throw error;
    }
  }

  // Get the latest version of a prompt by promptId
  async getLatestVersion(promptId) {
    try {
      if (!promptId) {
        throw new Error('promptId is required');
      }
      const [versions] = await this.promptRepository.pool.execute(
        'SELECT Version, mongo_prompt_id FROM Prompt_Versions WHERE Prompt_ID = ? ORDER BY Created_At DESC LIMIT 1',
        [promptId]
      );
      if (versions.length === 0) {
        throw new Error(`No versions found for Prompt_ID ${promptId}`);
      }
      const mongoPrompt = await this.promptRepository.getPromptFromMongo(
        versions[0].mongo_prompt_id
      );
      if (!mongoPrompt) {
        throw new Error('Prompt content not found in MongoDB');
      }
      return {
        version: versions[0].Version,
        content: mongoPrompt.content,
        mongoPromptId: versions[0].mongo_prompt_id,
      };
    } catch (error) {
      console.error('[PromptService] Error getting latest prompt version:', error);
      throw error;
    }
  }
}

module.exports = { PromptService };
