const fs = require('fs');
const path = require('path');
const { AzureOpenAI } = require('openai');

require('dotenv').config();
const config = require('../../config/env');
const { OpenAIFunctionCallingService } = require('./OpenAIFunctionCallingService');
const { convertLegacyOptions } = require('@deepgram/sdk');
const { PromptService } = require('./PromptService');

class AzureOpenAILLMService {
  constructor() {
    this.apiKey = process.env.AZURE_OPENAI_API_KEY || config.azure.openai.apiKey;
    this.endpoint = process.env.AZURE_OPENAI_ENDPOINT || config.azure.openai.endpoint;
    this.apiVersion = '2024-04-01-preview';
    this.deployment = 'gpt-4.1';
    this.modelName = 'gpt-4.1';

    this.assistantConfigs = {};
    this.externalDependencies = {
      EventHandler: require('../../utils/HandleFunctionCalling.js').EventHandler,
      queueAudio: null,
    };
    this.openAIFunctionCallingService = new OpenAIFunctionCallingService();
    console.log('[AzureOpenAILLMService] Initialized with API Key:', this.apiKey);
    console.log('[AzureOpenAILLMService] Endpoint:', this.endpoint);
    this.loadPromptsSync();
    this.azureClient = this.getClient();
    this.promptService = new PromptService();

    // 监听默认提示文件的变化
    const promptFilePath = path.join(
      __dirname,
      '../utils/prompts/intake_fiveQuestionPreQual_prompt.txt'
    );
    fs.watch(promptFilePath, (eventType, filename) => {
      if (eventType === 'change') {
        console.log(
          '[AzureOpenAILLMService] Detected change in hotel prompt file, reloading prompts...'
        );
        this.loadPromptsSync();
      }
    });

    // 监听 Simz Steak House 提示文件的变化
    const steakHousePromptFilePath = path.join(
      __dirname,
      '../utils/prompts/simz_steak_house_inbound.txt'
    );
    fs.watch(steakHousePromptFilePath, (eventType, filename) => {
      if (eventType === 'change') {
        console.log(
          '[AzureOpenAILLMService] Detected change in steak house prompt file, reloading prompts...'
        );
        this.loadPromptsSync();
      }
    });
  }

  loadPromptsSync() {
    try {
      const defaultGrokPrompt = fs.readFileSync(
        path.join(__dirname, '../utils/prompts/intake_fiveQuestionPreQual_prompt.txt'),
        'utf8'
      );
      const steakHousePrompt = fs.readFileSync(
        path.join(__dirname, '../utils/prompts/simz_steak_house_inbound.txt'),
        'utf8'
      );

      this.assistantConfigs = {
        defaultGrok: {
          model: this.modelName,
          systemPrompt: defaultGrokPrompt,
        },
        intakeGrok: {
          model: this.modelName,
          systemPrompt: defaultGrokPrompt,
        },
        steakHouseGrok: {
          model: this.modelName,
          systemPrompt: steakHousePrompt,
        },
      };
      console.log('[AzureOpenAILLMService] Prompts loaded successfully from .txt files');
    } catch (error) {
      console.error('❗[AzureOpenAILLMService] Error loading prompts from .txt files:', error);
      this.assistantConfigs = {
        defaultGrok: {
          model: this.modelName,
          systemPrompt:
            "You are Grok, a chatbot inspired by the Hitchhiker's Guide to the Galaxy.\nProvide witty and insightful responses.",
        },
        intakeGrok: {
          model: this.modelName,
          systemPrompt:
            'You are Grok, assisting with client intake processes.\nCollect client information and pre-qualify them for legal settlements.',
        },
        steakHouseGrok: {
          model: this.modelName,
          systemPrompt:
            'You are an AI assistant for Simz Steak House, a high-end steakhouse specializing in premium cuts.',
        },
      };
    }
  }

  getClient() {
    return new AzureOpenAI({
      apiKey: this.apiKey,
      endpoint: this.endpoint,
      apiVersion: this.apiVersion,
      deployment: this.deployment,
    });
  }

  setDependencies(deps) {
    this.externalDependencies.EventHandler =
      deps.EventHandler || this.externalDependencies.EventHandler;
    this.externalDependencies.queueAudio = deps.queueAudio || this.externalDependencies.queueAudio;
  }

  replacePromptVariables(promptContent, leadData) {
    console.log('[AzureOpenAILLMService] Replace Prompt Variables, leadData:', leadData);
    if (!leadData) return promptContent;

    let modifiedPrompt = promptContent;

    // Get Current Date
    const today = new Date();
    const todayDate = today.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });

    // Define First and Last Name
    let firstName = '';
    let lastName = '';
    if (leadData.name) {
      const nameParts = leadData.name.split(' ');
      if (nameParts.length >= 1) firstName = nameParts[0];
      if (nameParts.length >= 2) lastName = nameParts.slice(1).join(' ');
    }

    // Define the replacements
    const replacements = {
      '{{full_name}}': leadData.name || '',
      '{{today_date}}': todayDate,
      '{{first_name}}': firstName,
      '{{last_name}}': lastName,
      '{{email}}': leadData.email || '',
      '{{phone_number}}': leadData.phone || '',
    };
    for (const [placeholder, value] of Object.entries(replacements)) {
      modifiedPrompt = modifiedPrompt.replace(new RegExp(placeholder, 'g'), value);
    }

    return modifiedPrompt;
  }

  async generateAudioFromText(text, ws, streamSid, state, callSid, broadcastCallMessages) {
    if (!text) return;

    const sentence = text.trim();
    if (sentence) {
      console.log('[AzureOpenAILLMService] Generating audio for text:🟢', sentence);
      const audioFileName = `responseAudio_${Date.now()}_${Math.random()}.wav`;

      this.externalDependencies.queueAudio(
        sentence,
        audioFileName,
        ws,
        streamSid,
        state.cannotBeCutBuffer,
        callSid,
        broadcastCallMessages
      );

      if (/you may pre-qualify for a settlement since you have used one or more/i.test(sentence)) {
        state.userQualify = true;
      }
      state.assistantConfig = await this.switchAssistantBasedOnTextResponse(sentence, state);

      const openaiEndTime = Date.now();
      const openaiResponseTime = openaiEndTime - state.openaiStartTime;
      if (
        !state.textToSpeechConversionTimeMap ||
        !(state.textToSpeechConversionTimeMap instanceof Map)
      ) {
        console.error(
          '❗[AzureOpenAILLMService] textToSpeechConversionTimeMap is undefined or invalid, initializing'
        );
        state.textToSpeechConversionTimeMap = new Map();
      }
      state.textToSpeechConversionTimeMap.set(audioFileName, openaiResponseTime);
      state.openaiStartTime = Date.now();
    }
  }

  async handleOpenAIResponse(
    transcript,
    ws,
    streamSid,
    state,
    previousMessages = [],
    callService,
    callSid,
    broadcastCallMessages
  ) {
    try {
      let triggerToolCall = false;
      // Check if there is requests being processing. If yes, skip the current request.
      if (state.isProcessingLLM) {
        console.log(
          '[AzureOpenAILLMService] Skip the request because it is already being processed'
        );
        return;
      }

      let messages = [...previousMessages];
      previousMessages.push({
        role: 'user',
        content: transcript,
        timestamp: Date.now(),
        interrupted: false,
      });
      if (transcript) {
        messages.push({ role: 'user', content: transcript });
      }

      let accumulatedText = '';
      let entireResponse = '';
      const eventHandler = new this.externalDependencies.EventHandler(
        this.azureClient,
        ws,
        streamSid
      );

      // Enhanced tool call event handling for campaign prompts
      eventHandler.on('toolCallProcessed', async ({ toolCallName, output }) => {
        console.log('[AzureOpenAILLMService] Tool call processed:', toolCallName);

        // Parse the output if it's a string
        let parsedOutput = output;
        if (typeof output === 'string') {
          try {
            parsedOutput = JSON.parse(output);
          } catch (e) {
            console.warn('[AzureOpenAILLMService] Could not parse tool call output as JSON');
          }
        }

        // Handle campaign-related function results
        if (toolCallName === 'collectDiagnosisAndCreateCampaignQueue') {
          console.log(
            '[AzureOpenAILLMService] Campaign queue created:',
            parsedOutput.success
              ? `Found ${parsedOutput.queue?.length || 0} campaigns for cancer types: ${parsedOutput.diagnosisInfo?.cancerTypes?.join(', ') || 'unknown'}`
              : `Failed: ${parsedOutput.message}`
          );

          if (parsedOutput.success && parsedOutput.queue?.length > 0) {
            // Campaign queue created successfully
            state.hasCampaignQueue = true;
          }
        } else if (toolCallName === 'switchToNextCampaign') {
          console.log('[AzureOpenAILLMService] Switch to next campaign result:', parsedOutput);

          if (parsedOutput.systemPromptChanged) {
            console.log(
              '[AzureOpenAILLMService] Prompt was switched to next campaign:',
              parsedOutput.nextCampaign?.name
            );
          }
        }

        // Continue with existing processing
        state.assistantConfig = await this.switchAssistantBasedOnFunctionCall(
          toolCallName,
          parsedOutput,
          state
        );
      });

      // 根据callSid查找对应的promptType
      let promptData = state.getState('promptData');
      let leadData = state.getState('leadData');
      let systemPrompt = this.assistantConfigs.defaultGrok.systemPrompt;

      // 根据promptType选择对应的系统提示
      if (promptData) {
        if (promptData.promptType === 'simz-steakhouse') {
          console.log('[AzureOpenAILLMService] Using Simz Steak House system prompt.');
          console.log('[AzureOpenAILLMService] Prompt data:', JSON.stringify(promptData, null, 2));

          // Check if campaign queue is generated
          if (
            promptData.campaignQueue &&
            Array.isArray(promptData.campaignQueue) &&
            promptData.campaignQueue.length > 0
          ) {
            console.log(
              '[AzureOpenAILLMService] Campaign queue exists, using dynamic prompt content.'
            );

            // Use dynamic prompt
            if (promptData.promptContent) {
              systemPrompt = this.replacePromptVariables(promptData.promptContent, leadData);
              console.log('[AzureOpenAILLMService] Using dynamic prompt from campaign queue.');
            } else {
              console.log(
                '[AzureOpenAILLMService] Campaign queue exists but no prompt content found, using default.'
              );
            }
          } else {
            // First time loading prompt
            console.log(
              '[AzureOpenAILLMService] No campaign queue, fetching initial prompt from MongoDB.'
            );
            // Fetch the latest in-bounnd legal prompt
            const promptObj = await this.promptService.getPromptById(
              'a213c239-2313-47ef-a1fc-ead82891e2d6'
            );
            systemPrompt = this.replacePromptVariables(promptObj.content, leadData);
            console.log('[AzureOpenAILLMService] Using initial legal intake prompt.');
          }
        } else if (promptData.promptContent) {
          systemPrompt = this.replacePromptVariables(promptData.promptContent, leadData);
        } else {
          console.log('[AzureOpenAILLMService] Using default system prompt.');
        }
      } else {
        console.log('[AzureOpenAILLMService] No prompt data found, using default system prompt.');
      }

      messages.unshift({ role: 'system', content: systemPrompt });

      console.log('[AzureOpenAILLMService] Using API Key:', this.apiKey);
      console.log('[AzureOpenAILLMService] Using Endpoint:', this.endpoint);
      console.log('[AzureOpenAILLMService] Model:', this.assistantConfigs.defaultGrok.model);

      state.isProcessingLLM = true;
      callService.stopPlayingAudioAndClearAudioQueue(ws, state.getState('streamSid'));
      state.isGeneratingResponse = true;

      // get the tool definitions
      const tools = this.openAIFunctionCallingService.getToolDefinitions();
      console.log(
        '[AzureOpenAILLMService] Using tools:',
        tools.map((t) => t.function.name)
      );

      const stream = await this.azureClient.chat.completions.create({
        model: this.assistantConfigs.defaultGrok.model,
        messages,
        stream: true,
        tools: tools,
        tool_choice: 'auto',
      });

      for await (const chunk of stream) {
        // Check if there are tool calls being made
        if (chunk.choices[0]?.delta?.tool_calls) {
          triggerToolCall = true;
          const toolCallDelta = chunk.choices[0].delta.tool_calls[0];
          console.log('[AzureOpenAILLMService] Tool call detected:', toolCallDelta);

          // 如果是第一个chunk，初始化累积器
          if (toolCallDelta.function?.name && !state.currentToolCall) {
            state.currentToolCall = {
              id: toolCallDelta.id,
              name: toolCallDelta.function.name,
              arguments: '',
            };
          }

          // 累积arguments
          if (toolCallDelta.function?.arguments) {
            state.currentToolCall.arguments += toolCallDelta.function.arguments;
          }

          // 当收到完整的tool call时处理它
          if (
            state.currentToolCall &&
            state.currentToolCall.name &&
            state.currentToolCall.arguments
          ) {
            try {
              // 尝试解析完整的参数
              JSON.parse(state.currentToolCall.arguments);

              // 如果解析成功，说明收到了完整的参数，可以处理tool call
              const toolCallResult = await this.openAIFunctionCallingService.processToolCall({
                id: state.currentToolCall.id,
                function: {
                  name: state.currentToolCall.name,
                  arguments: state.currentToolCall.arguments,
                },
              });

              console.log('[AzureOpenAILLMService] Tool call result:', toolCallResult);

              // Check if this was a prompt-switching tool call
              if (
                state.currentToolCall.name === 'switchToNextCampaign' ||
                state.currentToolCall.name === 'collectDiagnosisAndCreateCampaignQueue'
              ) {
                try {
                  const result = JSON.parse(toolCallResult.output);
                  console.log('[AzureOpenAILLMService] Prompt switching tool call result:', result);

                  if (
                    state.currentToolCall.name === 'collectDiagnosisAndCreateCampaignQueue' &&
                    result.success &&
                    result.queue?.length > 0
                  ) {
                    // 初始化campaign队列
                    console.log(
                      '[AzureOpenAILLMService] Campaign queue created with:',
                      result.queue.length,
                      'campaigns'
                    );

                    // 创建疾病与campaign关联的系统消息
                    const campaignRelations = result.queue
                      .filter((campaign) => campaign.clientDiseaseRelate.length > 0)
                      .map((campaign) => {
                        const diseases = campaign.clientDiseaseRelate.join(' and ');
                        return `${campaign.name} Campaign is only related to Client's ${diseases}`;
                      })
                      .join(', ');

                    const relationMessage = campaignRelations
                      ? `Client diagnosis and campaign link: ${campaignRelations}. As a legal assistant, you have to only follow the above cancer and campaign relationship. And never assume any other relationship.`
                      : 'No specific disease-campaign relationships found';
                    console.log(
                      '[AzureOpenAILLMService] Campaign relation message:',
                      relationMessage
                    );

                    // 添加系统消息到消息历史
                    previousMessages.push({
                      role: 'system',
                      content: relationMessage,
                    });

                    const promptData = state.getState('promptData') || {};
                    console.log('[AzureOpenAILLMService] Checking prompt:', promptData);

                    promptData.campaignQueue = result.queue;
                    promptData.currentCampaignIndex = 0;

                    // Update PromptData
                    state.updateState('promptData', promptData);

                    const currentCampaign = result.queue[0];
                    const promptId = currentCampaign.promptId;

                    try {
                      const PromptService = require('./PromptService').PromptService;
                      const promptService = new PromptService();

                      const latestPromptData = await promptService.getLatestVersion(promptId);

                      promptData.promptId = promptId;
                      promptData.promptVersion = latestPromptData.version;
                      promptData.promptContent = latestPromptData.content;

                      // Update PromptData
                      state.updateState('promptData', promptData);

                      console.log(
                        `[AzureOpenAILLMService] Updated prompt to campaign ${currentCampaign.name}, promptId: ${promptId}, version: ${latestPromptData.version}`
                      );

                      // update grok prompt
                      messages[0] = { role: 'system', content: latestPromptData.content };
                    } catch (error) {
                      console.error('[AzureOpenAILLMService] Error fetching prompt:', error);
                    }
                  } else if (
                    state.currentToolCall.name === 'switchToNextCampaign' &&
                    result.systemPromptChanged
                  ) {
                    // 切换到下一个campaign
                    console.log('[AzureOpenAILLMService] Switching to next campaign');

                    const promptData = state.getState('promptData') || {};
                    console.log('[GrokLLMService] Checking prompt:', promptData);

                    // 检查是否有campaignQueue
                    if (
                      !promptData.campaignQueue ||
                      !Array.isArray(promptData.campaignQueue) ||
                      promptData.campaignQueue.length === 0
                    ) {
                      console.log('[AzureOpenAILLMService] No campaign queue available to switch');
                    } else {
                      // 更新当前指针，移至下一个campaign
                      if (promptData.currentCampaignIndex === undefined) {
                        promptData.currentCampaignIndex = 0;
                      } else {
                        promptData.currentCampaignIndex++;
                      }
                      console.log(
                        '[AzureOpenAILLMService] Current campaign index:',
                        promptData.currentCampaignIndex
                      );

                      // 检查是否超出队列范围
                      if (promptData.currentCampaignIndex >= promptData.campaignQueue.length) {
                        console.log('[AzureOpenAILLMService] Reached end of campaign queue');
                      } else {
                        // 获取当前campaign
                        const currentCampaign =
                          promptData.campaignQueue[promptData.currentCampaignIndex];
                        const promptId = currentCampaign.promptId;

                        try {
                          // 加载PromptService来获取最新版本的prompt
                          const PromptService = require('./PromptService').PromptService;
                          const promptService = new PromptService();

                          // 获取最新版本的prompt内容
                          const latestPromptData = await promptService.getLatestVersion(promptId);

                          // 更新promptData中的信息
                          promptData.promptId = promptId;
                          promptData.promptVersion = latestPromptData.version;
                          promptData.promptContent = latestPromptData.content;

                          // Update PromptData
                          state.updateState('promptData', promptData);

                          console.log(
                            `[AzureOpenAILLMService] Switched to campaign ${currentCampaign.name}, promptId: ${promptId}, version: ${latestPromptData.version}`
                          );

                          // update
                          messages[0] = { role: 'system', content: latestPromptData.content };
                          console.log(
                            '[AzureOpenAILLMService] Updated system prompt with new prompt:',
                            latestPromptData.content
                          );
                        } catch (error) {
                          console.error('[AzureOpenAILLMService] Error fetching prompt:', error);
                        }
                      }
                    }
                  }
                } catch (error) {
                  console.error(
                    `[AzureOpenAILLMService] Error parsing ${state.currentToolCall.name} result:`,
                    error
                  );
                }
              }

              // Add the tool call result to the messages
              messages.push({
                role: 'assistant',
                content: null,
                tool_calls: [
                  {
                    id: state.currentToolCall.id,
                    type: 'function',
                    function: {
                      name: state.currentToolCall.name,
                      arguments: state.currentToolCall.arguments,
                    },
                  },
                ],
              });

              messages.push({
                role: 'tool',
                tool_call_id: state.currentToolCall.id,
                content: toolCallResult.output,
              });

              // Add response after call tool
              try {
                // 使用完整的消息历史获取 AI 响应
                const response = await this.azureClient.chat.completions.create({
                  model: this.assistantConfigs.defaultGrok.model,
                  messages,
                  stream: true,
                });

                // 处理 AI 的响应
                for await (const chunk of response) {
                  const delta = chunk.choices[0]?.delta?.content;
                  if (delta) {
                    accumulatedText += delta;
                    if (/[.!?]/.test(delta)) {
                      const sentence = accumulatedText.trim();
                      entireResponse += sentence + ' ';
                      accumulatedText = '';
                      await this.generateAudioFromText(
                        sentence,
                        ws,
                        streamSid,
                        state,
                        callSid,
                        broadcastCallMessages
                      );
                    }
                  }
                }
              } catch (error) {
                console.error(
                  '[AzureOpenAILLMService] Error getting AI response after tool call:',
                  error
                );
              }

              // reset tool call
              state.currentToolCall = null;
            } catch (error) {
              // if parse failed
              console.log('[AzureOpenAILLMService] Waiting for complete tool call arguments...');
            }
          }
          continue;
        }

        // Process regular text responses
        const delta = chunk.choices[0]?.delta?.content;
        if (delta) {
          accumulatedText += delta;
          if (/[.!?]/.test(delta)) {
            const sentence = accumulatedText.trim();
            entireResponse += sentence + ' ';
            accumulatedText = '';
            await this.generateAudioFromText(
              sentence,
              ws,
              streamSid,
              state,
              callSid,
              broadcastCallMessages
            );
          }
        }
      }

      if (entireResponse) {
        messages.push({
          role: 'assistant',
          content: entireResponse,
          timestamp: Date.now(),
          interrupted: false,
        });
        previousMessages.push({
          role: 'assistant',
          content: entireResponse,
          timestamp: Date.now(),
          interrupted: false,
        });
      }

      state.currentRunId = `azure_openai_run_${Date.now()}`;
      console.log('[AzureOpenAILLMService] Stream ended, run ID updated:', state.currentRunId);

      if (triggerToolCall) {
        // 如果triggerToolCall为true，则需要处理tool call

        console.log('[AzureGrokLLMService] Trigger tool call, processing tool call result');
        let entireResponseAfterToolCall = '';
        // Use the updated messages to continue the conversation
        const response = await this.openAIFunctionCallingService.processToolCall({
          id: state.currentToolCall.id || 'default',
          function: {
            name: state.currentToolCall.name,
            arguments: state.currentToolCall.arguments,
          },
        });

        console.log(
          '[AzureGrokLLMService] Grok response after tool call:',
          response.choices[0].message.content
        );

        // 处理tool call后的response
        const toolCallResponse = response.choices[0].message.content;
        if (toolCallResponse) {
          // 按句子分割文本
          const sentences = toolCallResponse.split(/(?<=[.!?])\s+/);
          for (const sentence of sentences) {
            if (sentence.trim()) {
              entireResponseAfterToolCall += sentence + ' ';
              await this.generateAudioFromText(
                sentence,
                ws,
                streamSid,
                state,
                callSid,
                broadcastCallMessages
              );
            }
          }
        }
        previousMessages.push({
          role: 'assistant',
          content: entireResponseAfterToolCall,
          timestamp: Date.now(),
          interrupted: false,
        });
        triggerToolCall = false;
      }
      state.isProcessingLLM = false;
      return { id: state.currentRunId, messages: previousMessages };
    } catch (error) {
      console.error('❗[AzureOpenAILLMService] Error handling Azure OpenAI API:', error);
      state.isProcessingLLM = false;
      throw error;
    }
  }

  async createAssistantMessage(content, state) {
    state.messages = state.messages || [];
    state.messages.push({ role: 'assistant', content: content });
    return state.messages;
  }

  async createUserMessage(content, state) {
    state.messages = state.messages || [];
    state.messages.push({ role: 'user', content: content });
    return state.messages;
  }

  async createRun(threadId, assistantConfig) {
    console.log(`Simulating run creation for Azure OpenAI with config:`, assistantConfig);
    return { id: `azure_openai_run_${Date.now()}` };
  }

  async cancelRunWithTimeout(threadId, runId, timeoutMs, retryIntervalMs, ws, streamSid, state) {
    console.log(
      `Simulating cancellation of run ${runId}. Azure OpenAI streaming cannot be cancelled mid-stream.`
    );
    const newRunId = `azure_openai_run_${Date.now()}`;
    state.currentRunId = newRunId;
    await this.handleOpenAIResponse('', ws, streamSid, state, state.messages || []);
    return { status: 'cancelled', id: newRunId };
  }

  async waitForRunCompletion(threadId, runId) {
    console.log(`Simulating wait for run ${runId} completion (Azure OpenAI doesn't require this).`);
  }

  async switchAssistantBasedOnFunctionCall(toolCallName, output, state) {
    console.log(
      '[AzureOpenAILLMService] Switching assistant based on function call:',
      toolCallName
    );

    let success = false;
    if (typeof output === 'object') {
      success = output.success;
    } else if (output === true) {
      success = true;
    }

    if (success) {
      console.log('[AzureOpenAILLMService] Tool call was successful');
      state.userQualify = true;
    }

    // Handle campaign-related tools specifically
    if (toolCallName === 'collectDiagnosisAndCreateCampaignQueue') {
      return this.assistantConfigs.intakeGrok;
    } else if (toolCallName === 'switchToNextCampaign') {
      return null; // No need to change assistantConfig as the prompt content will be updated directly
    }

    // Handle other tools
    switch (toolCallName) {
      case 'check_cancer_qualification':
        return success ? this.assistantConfigs.intakeGrok : this.assistantConfigs.defaultGrok;
      case 'check_medical_qualification':
        return success ? this.assistantConfigs.intakeGrok : this.assistantConfigs.defaultGrok;
      case 'check_accident_qualification':
        return this.assistantConfigs.intakeGrok;
      default:
        console.log('Unknown tool call');
        return null;
    }
  }

  async switchAssistantBasedOnTextResponse(testResponse, state) {
    // const lowerCaseResponse = testResponse.toLowerCase();
    // let newAssistantConfig = state.assistantConfig || this.assistantConfigs.defaultGrok;
    // if (/port catheter|hernia mesh/.test(lowerCaseResponse)) {
    //   console.log('Switching to Intake Grok for Medical');
    //   newAssistantConfig = this.assistantConfigs.intakeGrok;
    // } else if (/car accident/.test(lowerCaseResponse)) {
    //   console.log('Switching to Intake Grok for Accident');
    //   newAssistantConfig = this.assistantConfigs.intakeGrok;
    // } else if (/you do indeed pre-qualify|you are pre-qualified/.test(lowerCaseResponse)) {
    //   console.log('User pre-qualified, keeping Intake Grok');
    //   newAssistantConfig = this.assistantConfigs.intakeGrok;
    // } else if (/i need to ask you some additional questions|unfortunately.*not qualify|I'm sorry, but based on/i.test(lowerCaseResponse)) {
    //   console.log('User might not qualify, checking if we need to switch campaigns');
    //   if (state.hasCampaignQueue) {
    //     // If there's a negative response and we have a campaign queue,
    //     // this might be a good time to use switchToNextCampaign
    //     console.log('[AzureOpenAILLMService] May need to switch campaigns based on text response');
    //   }
    // }
    // return newAssistantConfig;
  }
}

module.exports = { AzureOpenAIService: AzureOpenAILLMService };
