const { PromptService } = require('../services/PromptService');
const { GrokAPIService } = require('../services/GrokAPIService');
const { UserService } = require('../../auth/services/UserService');
const config = require('../../config/env');

class WebLLMController {
  constructor() {
    this.promptService = new PromptService();
    this.grokAPIService = new GrokAPIService();
    this.userService = new UserService();
  }

  async getPrompt(req, res) {
    try {
      console.log('[WebLLMController] getPrompt - req.user:', req.user);
      const prompt = await this.promptService.getPromptById(req.query.promptId);
      res.send(prompt.content);
    } catch (error) {
      console.error('[WebLLMController] Error getting prompt:', error);
      res.status(500).send('Failed to get Prompt');
    }
  }

  async getInboundPrompt(req, res) {
    try {
      const prompt = await this.promptService.getInboundPrompt();
      res.send(prompt);
    } catch (error) {
      console.error('[WebLLMController] Error getting inbound prompt:', error);
      res.status(500).send('Failed to get Inbound Prompt');
    }
  }

  async savePrompt(req, res) {
    try {
      const { promptId, name, content, provider, version } = req.body;
      console.log('[WebLLMController] savePrompt - req.user:', req.user);
      console.log('[WebLLMController] savePrompt - Request body:', req.body);
      if (!req.user || !req.user.userId) {
        console.log('[WebLLMController] savePrompt - User not authenticated');
        return res.status(401).send('User not authenticated');
      }
      const result = await this.promptService.savePrompt({
        promptId,
        name,
        content,
        provider,
        version,
        userId: req.user.userId,
      });
      res.json(result);
    } catch (error) {
      console.error('[WebLLMController] Error saving prompt:', error);
      res.status(500).send('Failed to save Prompt');
    }
  }

  async chatWithGrok(req, res) {
    try {
      const { promptId, message, provider, content } = req.body; // content 是用户选择的 Prompt
      console.log('[WebLLMController] chatWithGrok - req.user:', req.user);
      console.log('[WebLLMController] chatWithGrok - Request body:', req.body);

      if (!content) {
        throw new Error('Prompt content is required');
      }

      let result;
      switch (provider) {
        case 'Grok':
          // 使用用户选择的 Prompt 内容和消息直接调用 Grok API
          result = await this.grokAPIService.chatWithGrokSystemPrompt(message, content);
          // result = await this.grokAPIService.chatWithGrok(`${content}\nUser: ${message}`);
          break;
        case 'OpenAI':
          // 如果需要支持 OpenAI，可以在这里添加实现
          result = { response: `${content}\nUser: ${message}\nOpenAI: 未实现` };
          break;
        case 'Meta':
          result = { response: `${content}\nUser: ${message}\nMeta: 未实现` };
          break;
        default:
          throw new Error('Unsupported LLM provider');
      }

      res.json(result);
    } catch (error) {
      console.error('[WebLLMController] Error chatting with Grok:', error.message);
      res.status(500).send('Failed to chat with Grok');
    }
  }

  async clearChat(req, res) {
    try {
      console.log('[WebLLMController] clearChat - req.user:', req.user);
      const result = await this.grokAPIService.clearChatHistory();
      res.json(result);
    } catch (error) {
      console.error('[WebLLMController] Error clearing chat:', error);
      res.status(500).send('Failed to clear chat history');
    }
  }

  async getAllPrompts(req, res) {
    try {
      console.log('[WebLLMController] getAllPrompts - req.user:', req.user);
      if (!req.user || !req.user.userId) {
        console.log('[WebLLMController] getAllPrompts - User not authenticated');
        return res.status(401).send('User not authenticated');
      }
      const prompts = await this.promptService.getAllPrompts();
      res.json(prompts);
    } catch (error) {
      console.error('[WebLLMController] Error getting all prompts:', error);
      res.status(500).send('Failed to get Prompts list');
    }
  }

  async getCompanyPrompts(req, res) {
    try {
      console.log('[WebLLMController] getCompanyPrompts - req.user:', req.user);
      const user = await this.userService.getUserById(req.user.userId);
      if (!user || !user.Company_ID) {
        return res.status(404).json({ message: 'User or company not found' });
      }
      const companyId = user.Company_ID;
      const prompts = await this.promptService.getPromptsByCompanyId(companyId);
      res.json(prompts);
    } catch (error) {
      console.error('[WebLLMController] Error getting prompts by company:', error);
      res.status(500).send('Failed to get Prompts by company');
    }
  }

  async getPromptVersions(req, res) {
    try {
      console.log('[WebLLMController] getPromptVersions - req.user:', req.user);
      if (!req.user || !req.user.userId) {
        console.log('[WebLLMController] getPromptVersions - User not authenticated');
        return res.status(401).send('User not authenticated');
      }
      const promptId = req.query.promptId;
      if (!promptId) throw new Error('promptId is required');
      const versions = await this.promptService.getPromptVersions(promptId);
      res.json(versions);
    } catch (error) {
      console.error('[WebLLMController] Error getting prompt versions:', error);
      res.status(500).send('Failed to get Prompt versions list');
    }
  }

  async getPromptByMongoId(req, res) {
    try {
      console.log('[WebLLMController] getPromptByMongoId - req.user:', req.user);
      const mongoPromptId = req.query.mongoPromptId;
      if (!mongoPromptId) throw new Error('mongoPromptId is required');
      const mongoPrompt = await this.promptService.getPromptFromMongo(mongoPromptId);
      res.json(mongoPrompt);
    } catch (error) {
      console.error('[WebLLMController] Error getting prompt by mongo id:', error);
      res.status(500).send('Failed to get Prompt');
    }
  }

  async saveInboundPrompt(req, res) {
    try {
      const { prompt } = req.body;
      console.log('[WebLLMController] saveInboundPrompt - Request body:', req.body);
      const result = await this.promptService.saveInboundPrompt(prompt);
      res.json(result);
    } catch (error) {
      console.error('[WebLLMController] Error saving inbound prompt:', error);
      res.status(500).send('Failed to save Inbound Prompt');
    }
  }

  async chatWithInboundPrompt(req, res) {
    try {
      const { message } = req.body;
      console.log('[WebLLMController] chatWithInboundPrompt - Request body:', req.body);

      const prompt = await this.promptService.getInboundPrompt();
      const result = await this.grokAPIService.chatWithGrokSystemPrompt(message, prompt);
      res.json(result);
    } catch (error) {
      console.error('[WebLLMController] Error chatting with inbound prompt:', error.message);
      res.status(500).send('Failed to chat with Inbound Prompt');
    }
  }

  async clearInboundChat(req, res) {
    try {
      console.log('[WebLLMController] clearInboundChat');
      const result = await this.grokAPIService.clearChatHistory();
      res.json(result);
    } catch (error) {
      console.error('[WebLLMController] Error clearing inbound chat:', error);
      res.status(500).send('Failed to clear inbound chat history');
    }
  }

  // Simz Steak House 相关方法
  async getSimzSteakHousePrompt(req, res) {
    try {
      const prompt = await this.promptService.getSimzSteakHousePrompt();
      res.send(prompt);
    } catch (error) {
      console.error('[WebLLMController] Error getting Simz Steak House prompt:', error);
      res.status(500).send('Failed to get Simz Steak House Prompt');
    }
  }

  async saveSimzSteakHousePrompt(req, res) {
    try {
      const { prompt } = req.body;
      console.log('[WebLLMController] saveSimzSteakHousePrompt - Request body:', req.body);
      const result = await this.promptService.saveSimzSteakHousePrompt(prompt);
      res.json(result);
    } catch (error) {
      console.error('[WebLLMController] Error saving Simz Steak House prompt:', error);
      res.status(500).send('Failed to save Simz Steak House Prompt');
    }
  }

  async chatWithSimzSteakHousePrompt(req, res) {
    try {
      const { message } = req.body;
      console.log('[WebLLMController] chatWithSimzSteakHousePrompt - Request body:', req.body);

      const prompt = await this.promptService.getSimzSteakHousePrompt();
      const result = await this.grokAPIService.chatWithGrokSystemPrompt(message, prompt);
      res.json(result);
    } catch (error) {
      console.error(
        '[WebLLMController] Error chatting with Simz Steak House prompt:',
        error.message
      );
      res.status(500).send('Failed to chat with Simz Steak House Prompt');
    }
  }
}

module.exports = { WebLLMController };
