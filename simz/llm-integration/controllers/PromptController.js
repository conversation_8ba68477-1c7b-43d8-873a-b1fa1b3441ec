// llm-integration/controllers/PromptController.js
const { PromptService } = require('../services/PromptService');

class PromptController {
  constructor() {
    this.promptService = new PromptService();
  }

  // Get Prompt content by promptId and promptVersion
  async getPromptContent(req, res) {
    try {
      const { promptId, promptVersion } = req.query;
      if (!promptId || !promptVersion) {
        return res.status(400).send('promptId and promptVersion are required');
      }
      const promptContent = await this.promptService.getPromptContent(promptId, promptVersion);
      res.json({ content: promptContent });
    } catch (error) {
      console.error('[PromptController] Error getting prompt content:', error);
      res.status(500).send('Failed to get prompt content');
    }
  }
}

module.exports = { PromptController };
