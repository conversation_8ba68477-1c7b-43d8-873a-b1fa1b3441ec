const { GrokService } = require('../services/GrokLLMService');
const { OpenAIService } = require('../services/OpenAILLMService');
const { AzureOpenAIService } = require('../services/AzureOpenAILLMService');
const { AzureGrokService } = require('../services/AzureGrokLLMService');
const config = require('../../config/env');

class LLMController {
  constructor() {
    this.grokService = new GrokService();
    this.openaiService = new OpenAIService();
    this.azureOpenAIService = new AzureOpenAIService();
    this.azureGrokService = new AzureGrokService();
    this.useLLM = config.useLLM || 'openaiLLM';

    this.grokService.setDependencies({
      EventHandler: require('../../utils/HandleFunctionCalling').EventHandler,
    });
    this.openaiService.setDependencies({
      EventHandler: require('../../utils/HandleFunctionCalling').EventHandler,
    });
    this.azureOpenAIService.setDependencies({
      EventHandler: require('../../utils/HandleFunctionCalling').EventHandler,
    });
    this.azureGrokService.setDependencies({
      EventHandler: require('../../utils/HandleFunctionCalling').EventHandler,
    });
  }

  async handleLLMResponse(
    ws,
    msg,
    callService,
    sttService,
    ttsService,
    callSid,
    broadcastCallMessages
  ) {
    const streamSid =
      msg.streamSid ||
      callService.getCallState(msg.streamSid || 'default').getState('streamSid') ||
      'default';
    const state = callService.getCallState(streamSid);
    console.log('[LLMController] Handling LLM response streamSid:', streamSid);

    this.grokService.externalDependencies.queueAudio = callService.queueAudio.bind(callService);
    this.openaiService.externalDependencies.queueAudio = callService.queueAudio.bind(callService);
    this.azureOpenAIService.externalDependencies.queueAudio =
      callService.queueAudio.bind(callService);
    this.azureGrokService.externalDependencies.queueAudio =
      callService.queueAudio.bind(callService);

    let callerNumber = '';

    if (msg.start?.callSid) {
      const promptData = callService.getStagedPromptData(msg.start.callSid);
      const leadData = callService.getStagedLeadData(msg.start.callSid);

      console.log('[LLMController] LeadData:', leadData);

      callerNumber = leadData?.customerPhone || '';

      if (promptData) {
        state.updateState({ promptData });
      }

      if (leadData) {
        state.updateState({ leadData });
      }
    }

    try {
      if (this.useLLM === 'grokLLM') {
        await this.grokService.handleGrokResponse(
          msg.transcript || 'Hello. My number is ' + callerNumber,
          ws,
          streamSid,
          state,
          state.getState('messages') || [],
          callService,
          callSid,
          broadcastCallMessages
        );
      } else if (this.useLLM === 'azureOpenAILLM') {
        await this.azureOpenAIService.handleOpenAIResponse(
          msg.transcript || 'Hello. My number is ' + callerNumber,
          ws,
          streamSid,
          state,
          state.getState('messages') || [],
          callService,
          callSid,
          broadcastCallMessages
        );
      } else if (this.useLLM === 'azureGrokLLM') {
        await this.azureGrokService.handleOpenAIResponse(
          msg.transcript || 'Hello. My number is ' + callerNumber,
          ws,
          streamSid,
          state,
          state.getState('messages') || [],
          callService,
          callSid,
          broadcastCallMessages
        );
      } else {
        await this.openaiService.handleOpenAIResponse(
          msg.transcript || 'Hello. My number is ' + callerNumber,
          ws,
          streamSid,
          state,
          state.getState('messages') || [],
          callService,
          callSid,
          broadcastCallMessages
        );
      }
    } catch (error) {
      console.error('[LLMController] Error handling LLM response:', error);
    }
  }
}

module.exports = { LLMController };
