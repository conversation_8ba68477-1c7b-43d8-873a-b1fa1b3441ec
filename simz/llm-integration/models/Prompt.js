const mongoose = require('mongoose');

const PromptSchema = new mongoose.Schema({
  name: { type: String, required: true },
  provider: { type: String, enum: ['Grok', 'OpenAI', 'Meta'], required: true },
  lastModifiedByUser: { type: String, ref: 'User' },
  versions: [
    {
      version: { type: String, default: '1' },
      content: { type: String, required: true },
      createdAt: { type: Date, default: Date.now },
    },
  ],
  modelId: { type: String, default: null },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

module.exports = PromptSchema;
