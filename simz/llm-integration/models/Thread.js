// llm-integration/models/Thread.js
/**
 * Represents an OpenAI thread for conversation management. Grok do not use this.
 */
class Thread {
  constructor(id = null) {
    this.id = id; // Thread ID from OpenAI
    this.messages = []; // Optional: store message history locally
  }

  addMessage(role, content) {
    this.messages.push({ role, content });
  }

  getMessages() {
    return this.messages;
  }
}

module.exports = Thread;
