// llm-integration/models/Conversation.js
/**
 * Represents a conversation context for LLM interactions.
 */
class Conversation {
  constructor(threadId = null, messages = []) {
    this.threadId = threadId; // Thread ID (OpenAI) or null (Grok)
    this.messages = messages; // Array of { role: 'user'|'assistant', content: string }
    this.assistantConfig = null; // Assistant configuration or ID
    this.userQualify = false; // Whether the user is pre-qualified
  }

  addMessage(role, content) {
    this.messages.push({ role, content });
  }

  getMessages() {
    return this.messages;
  }

  setAssistant(configOrId) {
    this.assistantConfig = configOrId;
  }

  setUserQualify(qualified) {
    this.userQualify = !!qualified;
  }
}

module.exports = Conversation;
