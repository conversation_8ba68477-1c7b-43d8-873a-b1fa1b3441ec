// llm-integration/utils/llmUtils.js
const config = require('../../config/env');

/**
 * Utility functions for LLM integration.
 */
const llmUtils = {
  /**
   * Checks if a response indicates user qualification.
   * @param {string} text - Response text to evaluate
   * @returns {boolean} Whether the user is qualified
   */
  isUserQualified(text) {
    const lowerCaseText = text.toLowerCase();
    return /you may pre-qualify for a settlement since you have used one or more|you do indeed pre-qualify|you are pre-qualified/.test(
      lowerCaseText
    );
  },

  /**
   * Determines if a response requires switching assistants.
   * @param {string} text - Response text to evaluate
   * @returns {string|null} New assistant ID or config, or null if no switch needed
   */
  shouldSwitchAssistant(text) {
    const lowerCaseText = text.toLowerCase();
    if (/port catheter|hernia mesh/.test(lowerCaseText)) {
      return config.useLLM === 'grokLLM' ? 'intakeGrok' : 'azure5questionPreQualPart1';
    } else if (/car accident/.test(lowerCaseText)) {
      return config.useLLM === 'grokLLM' ? 'intakeGrok' : 'azure5questionPreQualPart1';
    } else if (/you do indeed pre-qualify|you are pre-qualified/.test(lowerCaseText)) {
      return config.useLLM === 'grokLLM' ? 'intakeGrok' : 'azure5questionPreQualPart4';
    }
    return null;
  },

  /**
   * Formats a message for LLM input.
   * @param {object} message - Message object with role and content
   * @returns {string} Formatted message
   */
  formatMessage(message) {
    return `${message.role}: ${message.content}`;
  },
};

module.exports = llmUtils;
