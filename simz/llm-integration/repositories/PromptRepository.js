// llm-integration/repositories/PromptRepository.js
const mysql = require('mysql2/promise');
const mongoose = require('mongoose');
const config = require('../../config/env');

const PromptSchema = new mongoose.Schema({
  content: { type: String, required: true },
  provider: { type: String },
  modelId: { type: String },
  version: { type: String, required: true },
  createdAt: { type: Date, default: Date.now },
});
const PromptModel = mongoose.model('Prompt', PromptSchema);

class PromptRepository {
  constructor() {
    this.pool = mysql.createPool({
      host: config.db.host,
      user: config.db.user,
      password: config.db.password,
      database: config.db.database,
    });
    this.PromptModel = PromptModel;
    this.mongoConnected = false;
    this.connectMongo();
  }

  async connectMongo() {
    if (!this.mongoConnected) {
      await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/refactor', {
        useNewUrlParser: true,
        useUnifiedTopology: true,
      });
      this.mongoConnected = true;
      console.log('[PromptRepository] Connected to MongoDB');
    }
  }

  async updatePromptInMongo(mongoPromptId, { content, provider, version }) {
    await PromptModel.updateOne({ _id: mongoPromptId }, { content, provider, version });
  }

  async getPromptById(promptId) {
    const [rows] = await this.pool.execute('SELECT * FROM Prompt_Store WHERE Prompt_ID = ?', [
      promptId,
    ]);
    return rows[0];
  }

  async createPrompt(promptId, name, userId) {
    await this.pool.execute(
      `INSERT INTO Prompt_Store (Prompt_ID, Prompt_Name, Versions, Last_Modified_By_User)
       VALUES (?, ?, ?, ?)`,
      [promptId, name, JSON.stringify([]), userId]
    );
  }

  async updatePromptVersions(promptId, versions) {
    await this.pool.execute(
      `UPDATE Prompt_Store
       SET Versions = ?, Updated_At = NOW()
       WHERE Prompt_ID = ?`,
      [JSON.stringify(versions), promptId]
    );
  }

  async createPromptVersion({ versionId, promptId, version, mongoPromptId }) {
    await this.pool.execute(
      `INSERT INTO Prompt_Versions (Version_ID, Prompt_ID, Version, mongo_prompt_id)
       VALUES (?, ?, ?, ?)`,
      [versionId, promptId, version, mongoPromptId]
    );
  }

  async getPromptsByUser(userId) {
    if (!userId) throw new Error('userId is required');
    const [rows] = await this.pool.execute(
      'SELECT * FROM Prompt_Store WHERE Last_Modified_By_User = ?',
      [userId]
    );
    return rows;
  }

  async savePromptToMongo({ content, provider, version }) {
    const promptDoc = new PromptModel({
      content,
      provider,
      version,
    });
    await promptDoc.save();
    return promptDoc._id.toString();
  }

  async getPromptFromMongo(mongoPromptId) {
    return await PromptModel.findById(mongoPromptId);
  }

  // Get Prompt version by promptId and version
  async getPromptVersion(promptId, version) {
    const [rows] = await this.pool.execute(
      `SELECT Version_ID, Prompt_ID, Version, mongo_prompt_id
       FROM Prompt_Versions
       WHERE Prompt_ID = ? AND Version = ?`,
      [promptId, version]
    );
    return rows[0] || null;
  }
}

module.exports = { PromptRepository };
