// llm-integration/repositories/CampaignRepository.js
const mysql = require('mysql2/promise');
const config = require('../../config/env');

class CampaignRepository {
  constructor() {
    this.pool = mysql.createPool({
      host: config.db.host,
      user: config.db.user,
      password: config.db.password,
      database: config.db.database,
    });
  }

  async findCampaignsByCancerTypeAndDate(cancerType, diagnosisDate) {
    try {
      console.log(
        '[CampaignRepository] Finding campaigns by cancer type and date:',
        cancerType,
        diagnosisDate
      );
      // Get campaigns that match the cancer type
      if (/^\d{4}-\d{2}$/.test(diagnosisDate)) {
        diagnosisDate += '-01';
      }

      const [campaigns] = await this.pool.execute(
        `SELECT c.*
           FROM Campaigns c
           JOIN Campaign_Cancer_Types cct ON c.Campaign_ID = cct.Campaign_ID
           WHERE (
                   cct.Cancer_Type = ?
                   OR ? LIKE CONCAT('%', cct.Cancer_Type, '%')
                 )
             AND STR_TO_DATE(c.Start_Date, '%Y-%m-%d') <= ?
           ORDER BY c.Value DESC;`,
        [cancerType, cancerType, diagnosisDate]
      );

      // For each campaign, get its associated cancer types
      for (const campaign of campaigns) {
        const [cancerTypes] = await this.pool.execute(
          'SELECT Cancer_Type FROM Campaign_Cancer_Types WHERE Campaign_ID = ?',
          [campaign.Campaign_ID]
        );
        campaign.cancerTypes = cancerTypes.map((ct) => ct.Cancer_Type);
      }

      return campaigns;
    } catch (error) {
      console.error('[CampaignRepository] Error finding campaigns:', error);
      throw error;
    }
  }

  async getCampaignById(campaignId) {
    try {
      const [campaigns] = await this.pool.execute('SELECT * FROM Campaigns WHERE Campaign_ID = ?', [
        campaignId,
      ]);

      if (campaigns.length === 0) {
        return null;
      }

      const campaign = campaigns[0];

      // Get cancer types
      const [cancerTypes] = await this.pool.execute(
        'SELECT Cancer_Type FROM Campaign_Cancer_Types WHERE Campaign_ID = ?',
        [campaignId]
      );

      campaign.cancerTypes = cancerTypes.map((ct) => ct.Cancer_Type);

      return campaign;
    } catch (error) {
      console.error('[CampaignRepository] Error getting campaign:', error);
      throw error;
    }
  }

  async getAllCampaigns() {
    try {
      const [campaigns] = await this.pool.execute('SELECT * FROM Campaigns ORDER BY Value DESC');

      // Get cancer types for each campaign
      for (const campaign of campaigns) {
        const [cancerTypes] = await this.pool.execute(
          'SELECT Cancer_Type FROM Campaign_Cancer_Types WHERE Campaign_ID = ?',
          [campaign.Campaign_ID]
        );
        campaign.cancerTypes = cancerTypes.map((ct) => ct.Cancer_Type);
      }

      return campaigns;
    } catch (error) {
      console.error('[CampaignRepository] Error getting all campaigns:', error);
      throw error;
    }
  }
  // Add a method to create the Campaigns and Campaign_Cancer_Types tables if they don't exist
  async createTablesIfNotExist() {
    try {
      // Create Campaigns table
      await this.pool.execute(`
        CREATE TABLE IF NOT EXISTS Campaigns (
          Campaign_ID VARCHAR(50) PRIMARY KEY,
          Name VARCHAR(255) NOT NULL,
          Start_Date VARCHAR(10) NOT NULL,
          End_Date VARCHAR(10) NOT NULL,
          Value INT NOT NULL,
          Prompt_ID VARCHAR(50) NOT NULL,
          Created_At TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          Updated_At TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `);
      // Create Campaign_Cancer_Types table
      await this.pool.execute(`
        CREATE TABLE IF NOT EXISTS Campaign_Cancer_Types (
          ID INT AUTO_INCREMENT PRIMARY KEY,
          Campaign_ID VARCHAR(50) NOT NULL,
          Cancer_Type VARCHAR(255) NOT NULL,
          FOREIGN KEY (Campaign_ID) REFERENCES Campaigns(Campaign_ID),
          UNIQUE KEY unique_campaign_cancer (Campaign_ID, Cancer_Type)
        )
      `);

      console.log('[CampaignRepository] Tables created successfully if they did not exist');
    } catch (error) {
      console.error('[CampaignRepository] Error creating tables:', error);
      throw error;
    }
  }
}

module.exports = { CampaignRepository };
