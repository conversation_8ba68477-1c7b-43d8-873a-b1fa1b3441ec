const WebSocket = require('ws');
const express = require('express');
const cors = require('cors');
const app = express();
const server = require('http').createServer(app);
const wss = new WebSocket.Server({ server });
const path = require('path');
const bodyParser = require('body-parser');
require('dotenv').config();
const twilio = require('twilio');

// Load configuration
const apiKeys = require('./config/api-keys');
const settings = require('./config/settings');
const env = require('./config/env');

// print environment variables
console.log('[index] Environment variables:');
console.log('[index] API_KEY:', process.env.API_KEY);

// add simple API key validation middleware
const validateApiKey = (req, res, next) => {
  console.log('[index > validateApiKey] Path:', req.path);
  console.log('[index > validateApiKey] Query params:', req.query);
  console.log('[index > validateApiKey] Headers:', req.headers);
  console.log('[index > validateApiKey] Expected API Key:', process.env.API_KEY);

  // ========== special handling for GET requests for Twilio validation ==========
  // Twilio will send GET requests when validating webhooks, we allow them through directly
  if (
    req.method === 'POST' &&
    (req.path.includes('/in-bound') ||
      req.path.includes('/out-bound') ||
      req.path.includes('/agent-confirm'))
  ) {
    console.log('[index > validateApiKey] Twilio validation request detected. Allowing through.');
    return next();
  }

  const apiKey = req.query.apikey || req.headers['x-api-key'];
  console.log('[index > validateApiKey] Received API Key:', apiKey);

  // check if it's a Twilio request (based on Content-Type or other characteristics)
  const isTwilioRequest =
    req.path.includes('/in-bound') ||
    req.path.includes('/out-bound') ||
    req.path.includes('/agent-confirm');

  if (!apiKey || apiKey !== process.env.API_KEY) {
    console.log('[index > validateApiKey] Invalid API key. Request rejected.');
    if (isTwilioRequest) {
      // return an XML error response that Twilio can handle
      res.set('Content-Type', 'text/xml');
      return res.send(`
        <Response>
          <Say>Unauthorized request. Invalid API key.</Say>
          <Hangup/>
        </Response>
      `);
    } else {
      // return a JSON error response for other requests
      return res.status(401).json({ error: 'Unauthorized: Invalid API key' });
    }
  }
  console.log('[index > validateApiKey] API key valid. Request allowed.');
  next();
};

// Load services and controllers
const { CallService } = require('./call-management/services/CallService');
const { WebSocketController } = require('./call-management/controllers/WebSocketController');
const { STTService } = require('./speech-to-text/services/DeepgramSTTService');
const { STTController } = require('./speech-to-text/controllers/STTController');
const { TTSService } = require('./text-to-speech/services/ElevenLabsTTSService');
const { TTSController } = require('./text-to-speech/controllers/TTSController');
const { LLMController } = require('./llm-integration/controllers/LLMController');
const { WebLLMController } = require('./llm-integration/controllers/WebLLMController');
const { AgentController } = require('./agents/controllers/AgentController');
const { CacheService } = require('./cache-management/CacheService');

// Initialize services
const callService = new CallService();
const sttService = new STTService(apiKeys.deepgram.apiKey);
const ttsService = new TTSService();
const llmController = new LLMController();
const webLLMController = new WebLLMController();
const agentController = new AgentController();
const twilioclient = twilio(apiKeys.twilio.accountSid, apiKeys.twilio.authToken);

// Set dependencies
sttService.setDependencies({ callService });
ttsService.setDependencies({ callService });

// Middleware
app.use(cors());
app.use(express.static('public'));
app.use(bodyParser.urlencoded({ extended: false }));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use('/public', express.static('public'));

// path handling, compatible with Nginx reverse proxy
app.use((req, res, next) => {
  // handle WebSocket connections under reverse proxy
  if (req.headers['x-forwarded-proto']) {
    req.headers.host = req.headers['x-forwarded-host'] || req.headers.host;
  }
  next();
});

// add WebSocket upgrade support
wss.on('connection', (ws, req) => {
  console.log('[index > websocket] New connection established');
  console.log('[index > websocket] Connection URL:', req.url);
  // console.log('[index > websocket] Connection headers:', req.headers);
  console.log('[index > websocket] Connection remote address:', req.socket.remoteAddress);

  // send test message
  try {
    ws.send(JSON.stringify({ type: 'welcome', message: 'WebSocket connection established!' }));
  } catch (error) {
    console.error('[index > websocket] Error sending welcome message:', error);
  }

  // listen for messages
  ws.on('message', (message) => {
    try {
      ws.send(JSON.stringify({ type: 'echo', message: message.toString() }));
    } catch (error) {
      console.error('[index > websocket] Error sending echo response:', error);
    }
  });

  ws.on('error', (error) => {
    console.error('[index > websocket] Connection error:', error);
  });

  ws.on('close', (code, reason) => {
    console.log('[index > websocket] Connection closed with code:', code, 'reason:', reason);
  });
});

// disable API key validation for Twilio related paths
// app.use('/twiml', validateApiKey);

// Load routes
const routes = require('./routes')(callService);
app.use('/', routes);

// Initialize controllers
const sttController = new STTController(wss, sttService, callService, llmController);
const webSocketController = new WebSocketController(
  wss,
  callService,
  sttController,
  ttsService,
  llmController,
  twilioclient
);
const ttsController = new TTSController(wss, ttsService, callService);

// Update CallService dependencies to include monitoring clients from WebSocketController
callService.setDependencies({
  ttsService,
  getMonitoringClients: () => webSocketController.monitoringClients,
});

// check PORT environment variable or set a fixed port (based on Nginx configuration)
const PORT = process.env.PORT || 30000; // default to 30000 as twiml endpoint

// Bootstrap the application
const { bootstrap } = require('./bootstrap');
bootstrap()
  .then(() => {
    server.listen(PORT, () => {
      console.log(`[index] Server running on port ${PORT} with log level ${settings.logLevel}`);
      console.log(`[index] Server configured for Nginx reverse proxy at ${env.server.url}`);
    });
  })
  .catch((error) => {
    console.error('[index] Failed to bootstrap application:', error);
    process.exit(1);
  });

module.exports = { app, server };
