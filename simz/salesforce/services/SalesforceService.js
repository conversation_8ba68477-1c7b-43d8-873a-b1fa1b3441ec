const jsforce = require('jsforce');
const env = require('../../config/env');

class SalesforceService {
  constructor() {
    this.conn = null;
    this.isConnected = false;
  }

  async connect() {
    try {
      this.conn = new jsforce.Connection({
        loginUrl: process.env.SALESFORCE_LOGIN_URL || 'https://thelegalleads.my.salesforce.com',
      });

      await this.conn.login(process.env.SALESFORCE_USERNAME, process.env.SALESFORCE_PASSWORD);

      this.isConnected = true;
      console.log('[SalesforceService] Successfully connected to Salesforce');
      return true;
    } catch (error) {
      console.error('[SalesforceService] Connection error:', error);
      this.isConnected = false;
      throw error;
    }
  }

  async disconnect() {
    if (this.conn) {
      try {
        this.conn.logout();
        this.isConnected = false;
        console.log('[SalesforceService] Disconnected from Salesforce');
      } catch (error) {
        console.warn('[SalesforceService] Warning during logout:', error.message);
        this.isConnected = false;
      }
    }
  }

  isAuthenticated() {
    return this.isConnected;
  }

  // CRUD Operations

  // Create a record
  async createRecord(objectType, data) {
    // for creating a record, company and LastName are mondotory.
    data.Company = 'SimZ AI';
    try {
      if (!this.isConnected) await this.connect();

      const result = await this.conn.sobject(objectType).create(data);
      const userTag = `[user:${data.Phone || data.MobilePhone || 'unknown'} ${data.LastName || data.Name || 'unknown'}]`;
      console.log(`${userTag} [SalesforceService] Created ${objectType} record:`, result);

      return result;
    } catch (error) {
      console.error(`[SalesforceService] Error creating ${objectType} record:`, error);
      throw error;
    }
  }

  async findLeadByPhoneNumber(phoneNumber, callState) {
    console.log('[SalesforceService] Finding lead by phone number:', phoneNumber);
    let leadData = callState ? callState.getState('leadData') || {} : {};
    try {
      if (!this.isConnected) await this.connect();

      const soqlQuery = `SELECT Id, LastModifiedDate FROM Lead WHERE SMS_Text_Phone__c = '${phoneNumber}' ORDER BY LastModifiedDate DESC LIMIT 1`;
      const result = await this.conn.query(soqlQuery);
      // If there is a record:
      if (result.records.length > 0) {
        console.log('[SalesforceService] Found lead by phone number:', result.records[0].Id);
        leadData.salesforceId = result.records[0].Id;
        if (callState) callState.updateState('leadData', leadData);
        return this.getLead('Lead', result.records[0].Id);
        // If there is no record, create a new record and return
      } else {
        console.log(
          '[SalesforceService] No lead found by phone number:',
          phoneNumber,
          'Creating new lead...'
        );
        const newLeadData = {
          LastName: 'New Client',
          Company: 'SimZ AI',
          Phone: phoneNumber,
          Description: 'Transfer call from AI agent',
          Status: 'Open - Not Contacted',
          LeadSource: 'Phone',
        };
        const newLead = await this.createRecord('Lead', newLeadData);
        console.log('[SalesforceService] Created new lead:', newLead);
        leadData.salesforceId = newLead.id;
        if (callState) callState.updateState('leadData', leadData);
        return this.getLead('Lead', newLead.id);
      }
    } catch (error) {
      console.error(`[SalesforceService] Error finding lead by phone number:`, error);
      throw error;
    }
  }

  // Read records with SOQL query
  async query(soqlQuery) {
    try {
      if (!this.isConnected) await this.connect();

      const result = await this.conn.query(soqlQuery);
      console.log(`[SalesforceService] Query result:`, {
        totalSize: result.totalSize,
        done: result.done,
      });
      return result;
    } catch (error) {
      console.error(`[SalesforceService] Query error:`, error);
      throw error;
    }
  }

  // Update a record
  async updateRecord(objectType, data, id) {
    try {
      if (!this.isConnected) await this.connect();

      // JSForce requires the ID to be included in the data object
      const updateData = { ...data, Id: id };
      const userTag = `[user:${data.Phone || data.MobilePhone || 'unknown'} ${data.LastName || data.Name || 'unknown'}]`;

      // 获取所有字段的更新结果
      const results = [];
      const errors = [];

      // 对每个字段单独进行更新
      for (const [field, value] of Object.entries(updateData)) {
        if (field === 'Id') continue; // 跳过 Id 字段

        try {
          const singleFieldUpdate = { Id: id, [field]: value };
          const result = await this.conn.sobject(objectType).update(singleFieldUpdate);
          results.push({ field, success: true, result });
          console.log(`${userTag} [SalesforceService] Updated field ${field} in ${objectType}:`, result);
        } catch (error) {
          console.warn(`[SalesforceService] Error updating field ${field}:`, error.message);
          errors.push({ field, error: error.message });
        }
      }

      // 返回更新结果
      return {
        success: errors.length === 0,
        results,
        errors: errors.length > 0 ? errors : undefined,
      };
    } catch (error) {
      console.error(`[SalesforceService] Error updating ${objectType} record:`, error);
      throw error;
    }
  }

  // Delete a record
  async deleteRecord(objectType, id) {
    try {
      if (!this.isConnected) await this.connect();
      const result = await this.conn.sobject(objectType).destroy(id);
      console.log(`[SalesforceService] Deleted ${objectType} record:`, result);
      return result;
    } catch (error) {
      console.error(`[SalesforceService] Error deleting ${objectType} record:`, error);
      throw error;
    }
  }

  // Get a lead record by ID
  async getLead(objectType, id) {
    try {
      if (!this.isConnected) await this.connect();
      const result = await this.conn.sobject(objectType).retrieve(id);
      return result;
    } catch (error) {
      console.error(`[SalesforceService] Error getting lead record:`, error);
      throw error;
    }
  }

  // Get metadata about an object
  async describeObject(objectType) {
    try {
      if (!this.isConnected) await this.connect();
      const meta = await this.conn.sobject(objectType).describe();
      return meta;
    } catch (error) {
      console.error(`[SalesforceService] Error getting metadata for ${objectType}:`, error);
      throw error;
    }
  }
}

module.exports = { SalesforceService };
