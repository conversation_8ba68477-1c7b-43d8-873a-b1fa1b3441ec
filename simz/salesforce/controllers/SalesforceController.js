const { SalesforceService } = require('../services/SalesforceService');

class SalesforceController {
  constructor() {
    this.salesforceService = new SalesforceService();
  }

  async testConnection(req, res) {
    try {
      await this.salesforceService.connect();
      res.json({
        success: true,
        message: 'Successfully connected to Salesforce',
      });
    } catch (error) {
      console.error('[SalesforceController] Connection test failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to connect to Salesforce',
        error: error.message,
      });
    }
  }

  async disconnect(req, res) {
    try {
      await this.salesforceService.disconnect();
      res.json({
        success: true,
        message: 'Successfully disconnected from Salesforce',
      });
    } catch (error) {
      console.error('[SalesforceController] Disconnect failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to disconnect from Salesforce',
        error: error.message,
      });
    }
  }

  // Create a record
  async createRecord(req, res) {
    try {
      const { objectType, data } = req.body;
      const userTag = `[user:${data?.Phone || data?.MobilePhone || 'unknown'} ${data?.LastName || data?.Name || 'unknown'}]`;
      console.log(`${userTag} [SalesforceController] Creating record:`, { objectType, data });

      if (!objectType || !data) {
        return res.status(400).json({
          success: false,
          message: 'Missing required parameters: objectType, data',
        });
      }

      const result = await this.salesforceService.createRecord(objectType, data);

      res.json({
        success: true,
        message: `Record created successfully in ${objectType}`,
        result,
      });
    } catch (error) {
      console.error('[SalesforceController] Create record failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create record',
        error: error.message,
      });
    }
  }

  // Query records
  async query(req, res) {
    try {
      const { soql } = req.body;

      if (!soql) {
        return res.status(400).json({
          success: false,
          message: 'Missing required parameter: soql',
        });
      }

      const result = await this.salesforceService.query(soql);

      res.json({
        success: true,
        totalSize: result.totalSize,
        done: result.done,
        records: result.records,
      });
    } catch (error) {
      console.error('[SalesforceController] Query failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to execute query',
        error: error.message,
      });
    }
  }

  // Update a record
  async updateRecord(req, res) {
    try {
      const { objectType, data } = req.body;
      const userTag = `[user:${data?.Phone || data?.MobilePhone || 'unknown'} ${data?.LastName || data?.Name || 'unknown'}]`;
      console.log(`${userTag} [SalesforceController] Updating record:`, { objectType, data });

      if (!objectType || !data || !data.Id) {
        return res.status(400).json({
          success: false,
          message: 'Missing required parameters: objectType, data with Id',
        });
      }

      const result = await this.salesforceService.updateRecord(objectType, data);

      res.json({
        success: true,
        message: `Record updated successfully in ${objectType}`,
        result,
      });
    } catch (error) {
      console.error('[SalesforceController] Update record failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update record',
        error: error.message,
      });
    }
  }

  // Delete a record
  async deleteRecord(req, res) {
    try {
      const { objectType, id } = req.body;

      if (!objectType || !id) {
        return res.status(400).json({
          success: false,
          message: 'Missing required parameters: objectType, id',
        });
      }

      const result = await this.salesforceService.deleteRecord(objectType, id);

      res.json({
        success: true,
        message: `Record deleted successfully from ${objectType}`,
        result,
      });
    } catch (error) {
      console.error('[SalesforceController] Delete record failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete record',
        error: error.message,
      });
    }
  }

  // Get object metadata
  async describeObject(req, res) {
    try {
      const { objectType } = req.params;

      if (!objectType) {
        return res.status(400).json({
          success: false,
          message: 'Missing required parameter: objectType',
        });
      }

      const metadata = await this.salesforceService.describeObject(objectType);

      res.json({
        success: true,
        objectType,
        metadata,
      });
    } catch (error) {
      console.error('[SalesforceController] Describe object failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get object metadata',
        error: error.message,
      });
    }
  }
}

module.exports = { SalesforceController };
