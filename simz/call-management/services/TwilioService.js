// call-management/services/TwilioService.js
const path = require('path');

class TwilioService {
  constructor(twilioclient) {
    this.twilioclient = twilioclient;
  }

  getIndexFilePath() {
    return path.join(__dirname, '../../public', 'GrokInterface.html');
  }

  generateTwiML(host, streamSid) {
    const twiml = `
      <Response>
        ${
          streamSid
            ? `
          <Connect>
            <Stream url="wss://${host}/" />
          </Connect>
        `
            : `
          <Say>Not able to connect</Say>
          <Pause length="60" />
        `
        }
      </Response>
    `;
    return twiml;
  }

  async updateCall(callSid, url, method = 'POST') {
    return await this.twilioclient.calls(callSid).update({ url, method });
  }

  /**
   * 创建带有 AMD (Answering Machine Detection) 的通话
   * @param {Object} callParams - 通话参数
   * @param {string} callParams.from - 发起号码
   * @param {string} callParams.to - 目标号码
   * @param {string} callParams.url - TwiML URL
   * @param {string} callParams.statusCallback - 状态回调 URL
   * @param {string} callParams.amdCallback - AMD 回调 URL
   * @param {Object} amdOptions - AMD 配置选项
   * @returns {Promise<Object>} Twilio 通话对象
   */
  async makeCallWithAMD(callParams, amdOptions = {}) {
    const {
      from,
      to,
      url,
      statusCallback,
      amdCallback,
      record = true,
      recordingStatusCallback,
      recordingStatusCallbackEvent = ['completed'],
      recordingStatusCallbackMethod = 'POST',
      statusCallbackEvent = ['initiated', 'ringing', 'answered', 'completed'],
      statusCallbackMethod = 'POST',
    } = callParams;

    // AMD 默认配置
    const defaultAmdOptions = {
      machineDetection: 'Enable', // 或 'DetectMessageEnd'
      asyncAmd: true, // 异步 AMD，立即连接通话
      asyncAmdStatusCallback: amdCallback,
      asyncAmdStatusCallbackMethod: 'POST',
      machineDetectionTimeout: 30, // 30秒超时
      machineDetectionSpeechThreshold: 2400, // 2.4秒语音阈值
      machineDetectionSpeechEndThreshold: 1200, // 1.2秒语音结束阈值
      machineDetectionSilenceTimeout: 5000, // 5秒静音超时
    };

    // 合并用户配置和默认配置
    const finalAmdOptions = { ...defaultAmdOptions, ...amdOptions };

    try {
      const call = await this.twilioclient.calls.create({
        from,
        to,
        url,
        record,
        recordingStatusCallback,
        recordingStatusCallbackEvent,
        recordingStatusCallbackMethod,
        statusCallback,
        statusCallbackEvent,
        statusCallbackMethod,
        // AMD 参数
        machineDetection: finalAmdOptions.machineDetection,
        asyncAmd: finalAmdOptions.asyncAmd,
        asyncAmdStatusCallback: finalAmdOptions.asyncAmdStatusCallback,
        asyncAmdStatusCallbackMethod: finalAmdOptions.asyncAmdStatusCallbackMethod,
        machineDetectionTimeout: finalAmdOptions.machineDetectionTimeout,
        machineDetectionSpeechThreshold: finalAmdOptions.machineDetectionSpeechThreshold,
        machineDetectionSpeechEndThreshold: finalAmdOptions.machineDetectionSpeechEndThreshold,
        machineDetectionSilenceTimeout: finalAmdOptions.machineDetectionSilenceTimeout,
      });

      console.log('[TwilioService] Call with AMD initiated:', {
        callSid: call.sid,
        amdEnabled: true,
        amdOptions: finalAmdOptions,
      });

      return call;
    } catch (error) {
      console.error('[TwilioService] Error creating call with AMD:', error);
      throw error;
    }
  }

  /**
   * 处理 AMD 回调结果
   * @param {Object} amdResult - AMD 回调数据
   * @returns {Object} 处理后的 AMD 结果
   */
  processAMDResult(amdResult) {
    const { CallSid, AccountSid, AnsweredBy, MachineDetectionDuration } = amdResult;

    // AMD 结果类型映射
    const amdResultTypes = {
      human: {
        type: 'human',
        description: 'Human Answer',
        shouldContinueCall: true,
      },
      machine_start: {
        type: 'machine',
        description: 'Answer Machine Detected',
        shouldContinueCall: false,
      },
      machine_end_beep: {
        type: 'machine',
        description: 'Answer Machine End Beep',
        shouldContinueCall: false,
      },
      machine_end_silence: {
        type: 'machine',
        description: 'Answer Machine End Silence',
        shouldContinueCall: false,
      },
      machine_end_other: {
        type: 'machine',
        description: 'Answer Machine End Other',
        shouldContinueCall: false,
      },
      fax: {
        type: 'fax',
        description: 'Fax',
        shouldContinueCall: false,
      },
      unknown: {
        type: 'unknown',
        description: 'Unknown',
        shouldContinueCall: true, // 继续通话，以防是人工接听
      },
    };

    const result = amdResultTypes[AnsweredBy] || amdResultTypes['unknown'];

    return {
      callSid: CallSid,
      accountSid: AccountSid,
      answeredBy: AnsweredBy,
      detectionDuration: MachineDetectionDuration,
      ...result,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 挂断通话
   * @param {string} callSid - 通话 SID
   * @returns {Promise<Object>} 更新后的通话对象
   */
  async hangupCall(callSid) {
    try {
      const call = await this.twilioclient.calls(callSid).update({
        status: 'completed',
      });

      console.log('[TwilioService] Call hung up:', callSid);
      return call;
    } catch (error) {
      console.error('[TwilioService] Error hanging up call:', error);
      throw error;
    }
  }
}

module.exports = { TwilioService };
