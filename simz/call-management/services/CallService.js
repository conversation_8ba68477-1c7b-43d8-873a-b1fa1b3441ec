// call-management/services/CallService.js
const path = require('path');
const fs = require('fs');
const { LiveTranscriptionEvents } = require('@deepgram/sdk');
const util = require('util');
const WebSocket = require('ws');
const https = require('https');
const ffmpeg = require('fluent-ffmpeg');
const ffmpegInstaller = require('@ffmpeg-installer/ffmpeg');
// Load models and utilities
const CallState = require('../models/CallState');

ffmpeg.setFfmpegPath(ffmpegInstaller.path);

/**
 * Manages call lifecycle, state, and audio processing for call management.
 */
class CallService {
  constructor() {
    this.callStates = new Map(); // Map<streamSid, CallState> to manage multiple call states
    this.stagedPromptData = new Map(); // Map<callSid, promptData>
    this.stagedLeadData = new Map(); // Map<callSid, promptData>
    this.waitClientResponseTime = 31000; // 25 seconds
    this.dependencies = {}; // For dependency injection
  }

  // Set dependencies (e.g., ttsService)
  setDependencies(deps) {
    this.dependencies = { ...this.dependencies, ...deps };
  }

  // New method to stage data before WebSocket connects
  stagePromptData(callSid, promptData) {
    console.log(`[CallService] Staging prompt data for callSid: ${callSid}`);
    this.stagedPromptData.set(callSid, promptData);
  }

  getStagedPromptData(callSid) {
    return this.stagedPromptData.get(callSid);
  }

  stageLeadData(callSid, leadData) {
    console.log(`[CallService] Staging lead data for callSid: ${callSid}`);
    this.stagedLeadData.set(callSid, leadData);
  }

  getStagedLeadData(callSid) {
    return this.stagedLeadData.get(callSid);
  }

  // Initialize call state for a specific streamSid
  initializeCallState(host = 'unknown', streamSid = 'default') {
    const callState = new CallState();
    callState.updateState({
      host,
      threadId: null,
      callID: null,
      streamSid,
      audioQueue: [],
      processingQueue: false,
      currentPlayingAudio: null,
      coughingFlag: false,
      lastTranscript: '',
      lastTimestamp: 0,
      firstAudio: true,
      isGeneratingResponse: false,
      introSaid: false,
      userQualify: false,
      currentRunId: '',
      hasTriedCancel: false,
      thisIsACallBack: false,
      withBGN: false,
      backgroundAudioPlaying: false,
      cannotBeCutBuffer: false,
      transferInProgress: false,
      recentPhrases: [],
      messages: [],
      textToSpeechConversionTimeMap: new Map(),
      openaiStartTime: Date.now(),
      agentId: null,
    });
    this.callStates.set(streamSid, callState);
    return callState;
  }

  // Get call state by streamSid, initialize if not exists
  getCallState(streamSid) {
    let callState = this.callStates.get(streamSid);
    if (!callState) {
      console.warn(
        `[CallService] Call state for streamSid ${streamSid} not found, initializing with default host`
      );
      callState = this.initializeCallState(streamSid, streamSid);
    }
    return callState;
  }

  // Get call state by callSid
  getCallStateByCallSid(callSid) {
    const callStatesArray = Array.from(this.callStates.values());
    for (let i = callStatesArray.length - 1; i >= 0; i--) {
      const callState = callStatesArray[i];
      if (callState.getState('currentCallSid') === callSid) {
        return callState;
      }
    }
    return null;
  }

  getAllCallStates() {
    return Array.from(this.callStates.values());
  }

  async handleConnected(ws, state, msg) {
    state.ws = ws; // Store WebSocket instance in state
    state.withBGN = false;
    state.introSaid = false;

    // Set agentId if available from prompt data
    const promptData = state.getState('promptData');

    if (promptData) {
      state.updateState({ agentId: promptData.agentId });
    }
    if (!state.thisIsACallBack) {
      console.log('Thread creation logic to be implemented in LLM integration.');
    }

    console.log(`Sending initial message for threadID: ${state.threadId || 'N/A'}`);
  }

  handleStart(ws, streamSid, callSid, audioBufferCallback) {
    const callState = this.getCallState(streamSid);

    // Retrieve staged prompt data
    const promptData = this.stagedPromptData.get(callSid);
    const leadData = this.stagedLeadData.get(callSid);

    callState.updateState({
      streamSid,
      currentCallSid: callSid,
      processingQueue: false,
      promptData, // Store the entire prompt data object
      leadData, // Store the entire lead data object
    });

    // Print all stagedPromptData with their callSid
    // console.log('[CallService] All stagedPromptData:');
    // for (const [sid, data] of this.stagedPromptData.entries()) {
    //   console.log(`  callSid: ${sid}, data:`, data);
    // }

    // console.log('[CallService] callSid', callSid);
    // console.log('[CallService] promptData', promptData);
    // console.log('[CallService] CallState:', callState);

    if (promptData) {
      callState.updateState({ agentId: promptData.agentId });
      this.stagedPromptData.delete(callSid); // Clean up the staged data
    }

    if (leadData) {
      this.stagedLeadData.delete(callSid); // Clean up the staged data
    }

    this.startWaitClientResponseTimer(ws, streamSid);

    audioBufferCallback((audioBuffer) => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(
          JSON.stringify({
            event: 'media',
            streamSid,
            media: { payload: audioBuffer.toString('base64') },
          })
        );
      }
    });
  }

  async handleStop(streamSid) {
    const callState = this.getCallState(streamSid);
    callState.reset();
    console.log(`[CallService]Call ended for streamSid: ${streamSid}`);
    console.log(
      '[CallService]Salesforce update logic to be implemented in Salesforce integration.'
    );
    this.callStates.delete(streamSid); // Clean up state after call ends
  }

  queueAudio(
    text,
    filename,
    ws,
    streamSid,
    cannotBeCut,
    callSid = null,
    broadcastCallMessages = null
  ) {
    const callState = this.getCallState(streamSid);
    if (!callState.audioQueue || !Array.isArray(callState.audioQueue)) {
      console.error(
        `[CallService] audioQueue is invalid for streamSid ${streamSid}, reinitializing`
      );
      callState.audioQueue = [];
    }
    
    const queueItem = { 
      text, 
      filename, 
      ws, 
      streamSid, 
      cannotBeCut, 
      queuedAt: Date.now(),
      queueIndex: callState.audioQueue.length 
    };
    
    callState.audioQueue.push(queueItem);
    console.log(`[CallService] Queued audio #${queueItem.queueIndex}, streamSid: ${streamSid}, text: "${text.substring(0, 50)}..."`);
    console.log(`[CallService] Current queue length: ${callState.audioQueue.length}, processing: ${callState.processingQueue}`);
    
    if (!callState.processingQueue) {
      console.log('[CallService] Starting queue processing');
      this.processQueue(ws, streamSid, callSid, broadcastCallMessages);
    } else {
      console.log('[CallService] Queue already processing, item will be processed in order');
    }
  }

  async processQueue(ws, streamSid, callSid = null, broadcastCallMessages = null) {
    const callState = this.getCallState(streamSid);
    
    if (callState.processingQueue) {
      console.log('[CallService] Queue already processing, skipping duplicate call');
      return;
    }
    
    if (!callState.audioQueue || callState.audioQueue.length === 0) {
      callState.processingQueue = false;
      if (callState.withBGN) {
        await this.playStoredAudio(
          ws,
          streamSid,
          './audios/converted_sound_effect_CallCenter2_mulaw_modified.wav'
        );
      }
      return;
    }
    
    callState.processingQueue = true;
    const queueItem = callState.audioQueue.shift();
    const {
      text,
      filename,
      ws: queueWs,
      streamSid: queueStreamSid,
      cannotBeCut,
      queuedAt,
      queueIndex
    } = queueItem;
    
    console.log(`[CallService] Processing queue item #${queueIndex}, queued at: ${queuedAt}, text: "${text.substring(0, 50)}..."`);
    console.log(`[CallService] Remaining queue length: ${callState.audioQueue.length}`);

    try {
      await this.synthesizeAndPlay(
        text,
        filename,
        queueWs,
        queueStreamSid,
        cannotBeCut,
        callSid,
        broadcastCallMessages
      );
      if (callSid && broadcastCallMessages) {
        broadcastCallMessages(callSid, text);
      }
    } catch (error) {
      console.error('[CallService] Error processing queue item:', error);
      callState.processingQueue = false;
      setTimeout(() => {
        this.processQueue(ws, streamSid, callSid, broadcastCallMessages);
      }, 100);
    }
  }

  async synthesizeAndPlay(
    text,
    filename,
    ws,
    streamSid,
    cannotBeCut,
    callSid = null,
    broadcastCallMessages = null
  ) {
    const callState = this.getCallState(streamSid);
    const ttsService = this.dependencies.ttsService;
    console.log('[CallService] Synthesizing speech:', text);
    
    return new Promise((resolve, reject) => {
      ttsService.synthesizeSpeech(
        text,
        filename,
        ws,
        streamSid,
        cannotBeCut,
        callState.withBGN,
        (error) => {
          // 音频播放完成后的回调
          if (error) {
            console.error('[CallService] TTS synthesis error:', error);
            callState.processingQueue = false;
            reject(error);
          } else {
            console.log('[CallService] Audio synthesis and playback completed for:', filename);
            callState.processingQueue = false;
            // 继续处理下一个队列项
            this.processQueue(ws, streamSid, callSid, broadcastCallMessages);
            resolve();
          }
        },
        callSid,
        this.dependencies.getMonitoringClients()
      );
    });
  }

  async playStoredAudio(ws, streamSid, audioPath, callback) {
    const callState = this.getCallState(streamSid);
    const audioFilePath = path.join(__dirname, '../../audios', audioPath.replace('./audios/', ''));
    fs.readFile(audioFilePath, (err, data) => {
      if (err) {
        console.error('❗[CallService]Failed to read audio file:', err);
        if (callback) callback();
        return;
      }
      const base64Audio = data.toString('base64');
      callState.currentPlayingAudio = { ws, streamSid, payload: base64Audio };
      ws.send(
        JSON.stringify({
          event: 'media',
          streamSid,
          media: { payload: base64Audio },
        })
      );
      const audioDuration = (data.length / (8000 * 2)) * 1000;
      setTimeout(() => {
        if (callback) callback();
      }, audioDuration);
    });
  }

  playRandomBackgroundNoise(ws, streamSid) {
    const callState = this.getCallState(streamSid);
    const backgroundNoises = [
      'converted_sound_effect_CallCenter_chunk_1',
      'converted_sound_effect_CallCenter_chunk_2',
      'converted_sound_effect_CallCenter_chunk_3',
      'converted_sound_effect_CallCenter_chunk_4',
      'converted_sound_effect_CallCenter_chunk_5',
      'converted_sound_effect_CallCenter_chunk_6',
      'converted_sound_effect_CallCenter_chunk_7',
    ];
    const unusedPhrases = backgroundNoises.filter(
      (phrase) => !this.isRecentlyUsed(phrase, streamSid)
    );
    if (unusedPhrases.length === 0) {
      this.playRandomBackgroundNoise(backgroundNoises, ws, streamSid);
    } else {
      const phrase = unusedPhrases[Math.floor(Math.random() * unusedPhrases.length)];
      this.addRecentPhrase(phrase, streamSid);
      this.playStoredAudio(ws, streamSid, `./audios/${phrase}.wav`);
    }
  }

  addRecentPhrase(phrase, streamSid) {
    const callState = this.getCallState(streamSid);
    if (callState.recentPhrases.length >= 1) {
      callState.recentPhrases.shift();
    }
    callState.recentPhrases.push(phrase);
  }

  isRecentlyUsed(phrase, streamSid) {
    const callState = this.getCallState(streamSid);
    return callState.recentPhrases.includes(phrase);
  }

  startWaitClientResponseTimer(ws, streamSid) {
    const callState = this.getCallState(streamSid);
    callState.waitClientResponseTimer = setTimeout(() => {
      console.log(
        '[CallService] Waiting for client response timeout, playing random prompt audio.'
      );
      this.playRandomPromptAudio(ws, streamSid);
    }, this.waitClientResponseTime);
  }

  // Play random prompt audio when user is silent
  playRandomPromptAudio(ws, streamSid) {
    const promptAudios = [
      'https://audioelevenlabs.s3.us-west-2.amazonaws.com/Maia_AreYouStillThere.mp3',
      'https://audioelevenlabs.s3.us-west-2.amazonaws.com/Maia_WhenYouAreReady.wav'
    ];
    
    const randomIndex = Math.floor(Math.random() * promptAudios.length);
    const selectedAudio = promptAudios[randomIndex];
    
    console.log(`[CallService] Playing random prompt audio: ${selectedAudio}`);
    this.playRemoteAudio(ws, streamSid, selectedAudio, () => {
      // After playing the prompt audio, restart the timer
      console.log('[CallService] Prompt audio finished, restarting wait timer');
      this.startWaitClientResponseTimer(ws, streamSid);
    });
  }

  // Reset the client response timer (call when user speaks)
  resetWaitClientResponseTimer(ws, streamSid) {
    const callState = this.getCallState(streamSid);
    
    // Clear existing timer
    if (callState.waitClientResponseTimer) {
      clearTimeout(callState.waitClientResponseTimer);
      callState.waitClientResponseTimer = null;
    }
    
    // Start new timer
    this.startWaitClientResponseTimer(ws, streamSid);
  }

  // Download and play remote audio file
  async playRemoteAudio(ws, streamSid, audioUrl, callback) {
    const callState = this.getCallState(streamSid);
    console.log(`[CallService] Playing remote audio: ${audioUrl}`);
    
    try {
      const audioBuffer = await this.downloadAudioFromUrl(audioUrl);
      
      // Convert MP3 to mulaw 8000Hz for Twilio
      const convertedAudio = await this.convertAudioToMulaw(audioBuffer);
      
      // Convert to base64 and send to Twilio
      const base64Audio = convertedAudio.toString('base64');
      callState.currentPlayingAudio = { ws, streamSid, payload: base64Audio };
      
      ws.send(
        JSON.stringify({
          event: 'media',
          streamSid,
          media: { payload: base64Audio },
        })
      );
      
      // Calculate duration for mulaw 8000Hz
      const audioDuration = (convertedAudio.length / 8000) * 1000;
      console.log(`[CallService] Playing remote audio, duration: ${audioDuration}ms`);
      
      setTimeout(() => {
        if (callback) callback();
      }, audioDuration);
      
    } catch (error) {
      console.error('[CallService] Error playing remote audio:', error);
      if (callback) callback(error);
    }
  }

  // Convert audio buffer to mulaw 8000Hz format
  convertAudioToMulaw(inputBuffer) {
    return new Promise((resolve, reject) => {
      const inputPath = path.join(__dirname, '../../audios', `temp_audio_${Date.now()}.mp3`);
      const outputPath = path.join(__dirname, '../../audios', `temp_audio_${Date.now()}.wav`);
      
      try {
        // Write input buffer to temporary file
        fs.writeFileSync(inputPath, inputBuffer);
        
        ffmpeg(inputPath)
          .audioCodec('pcm_mulaw')
          .audioFrequency(8000)
          .audioChannels(1)
          .format('mulaw')
          .outputOptions(['-ar', '8000', '-ac', '1'])
          .on('end', () => {
            try {
              // Read converted file
              const convertedBuffer = fs.readFileSync(outputPath);
              
              // Clean up temporary files
              fs.unlinkSync(inputPath);
              fs.unlinkSync(outputPath);
              
              resolve(convertedBuffer);
            } catch (error) {
              reject(error);
            }
          })
          .on('error', (error) => {
            // Clean up temporary files on error
            try {
              if (fs.existsSync(inputPath)) fs.unlinkSync(inputPath);
              if (fs.existsSync(outputPath)) fs.unlinkSync(outputPath);
            } catch (cleanupError) {
              console.error('[CallService] Error cleaning up temp files:', cleanupError);
            }
            reject(error);
          })
          .save(outputPath);
      } catch (error) {
        reject(error);
      }
    });
  }

  // Download audio from URL
  downloadAudioFromUrl(url) {
    return new Promise((resolve, reject) => {
      const chunks = [];
      
      https.get(url, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`Failed to download audio: ${response.statusCode}`));
          return;
        }
        
        response.on('data', (chunk) => {
          chunks.push(chunk);
        });
        
        response.on('end', () => {
          const audioBuffer = Buffer.concat(chunks);
          resolve(audioBuffer);
        });
        
        response.on('error', (error) => {
          reject(error);
        });
      }).on('error', (error) => {
        reject(error);
      });
    });
  }

  stopPlayingAudioAndClearAudioQueue(ws, streamSid) {
    const callState = this.getCallState(streamSid);
    callState.isGeneratingResponse = false;
    ws.send(JSON.stringify({ event: 'clear', streamSid }));
    callState.audioQueue = [];
    if (callState.currentPlayingAudio) {
      callState.currentPlayingAudio = null;
    }
    callState.processingQueue = false;
  }

  async transferToLawFirm(streamSid) {
    const callState = this.getCallState(streamSid);
    if (callState.transferInProgress) {
      console.log('Transfer already in progress. Skipping...');
      return;
    }
    callState.transferInProgress = true;
    try {
      console.log('Waiting 10 seconds before initiating the transfer...');
      await new Promise((resolve) => setTimeout(resolve, 15000));
      const voiceWebhookUrl = `https://${callState.host}/voice`;
      const twilio = require('twilio');
      const twilioclient = new twilio(
        process.env.TWILIO_ACCOUNT_SID,
        process.env.TWILIO_AUTH_TOKEN
      );
      await twilioclient.calls(callState.currentCallSid).update({
        url: voiceWebhookUrl,
        method: 'POST',
      });
      console.log('Call redirected to /voice for conference setup.');
    } catch (error) {
      console.error('❗[CallService]Error during transfer:', error);
    } finally {
      callState.transferInProgress = false;
    }
  }
}

module.exports = { CallService };
