const { CallRepository } = require('../repositories/CallRepository');
const twilio = require('twilio');
const axios = require('axios');
const jwt = require('jsonwebtoken');
const Transcript = require('../models/Transcript');
const { PromptService } = require('../../llm-integration/services/PromptService');
const { UserService } = require('../../auth/services/UserService');
const { PromptRepository } = require('../../llm-integration/repositories/PromptRepository');

class CallController {
  constructor(apiKeys, callService) {
    this.callRepository = new CallRepository();
    this.twilioClient = new twilio(apiKeys.twilio.accountSid, apiKeys.twilio.authToken);
    this.apiKeys = apiKeys;
    this.promptService = new PromptService();
    this.userService = new UserService();
    this.callService = callService;
    this.promptRepository = new PromptRepository();
  }

  getUserTagByCallSid(callSid, fallbackDirection, fallbackTo, fallbackFrom) {
    let phone = 'unknown';
    let name = 'unknown';
    
    const leadData = this.callService.getStagedLeadData(callSid) || {};
    if (leadData.customerPhone) phone = leadData.customerPhone;
    if (leadData.clientName) name = leadData.clientName;
    
    if (fallbackDirection === 'inbound' && phone === 'unknown') phone = fallbackTo;
    if (fallbackDirection === 'outbound-api' && phone === 'unknown') phone = fallbackFrom;
    return `[user:${phone} ${name}]`;
  }

  async getCallHistory(req, res) {
    try {
      if (!req.user || !req.user.userId) {
        return res.status(401).send('User not authenticated');
      }
      const user = await this.userService.getUserById(req.user.userId);
      const companyId = user.Company_ID;

      // Join Calls with Call_Agents and Prompt_Store to get additional info
      const [calls] = await this.callRepository.mysqlPool.query(`
        SELECT 
          c.Call_ID AS callId,
          c.Call_DateTime AS callDateTime,
          c.Twilio_Stream_ID AS twilioStreamId,
          c.Client_Name AS clientName,
          c.Call_Duration AS callDuration,
          c.Recording_URL AS recordingUrl,
          c.Caller_Number AS callerNumber,
          c.To_Number AS toNumber,
          c.AMD_Result AS amdResult,
          ca.Agent_Name AS agentName,
          ps.Prompt_Name AS promptName
        FROM Calls c
        LEFT JOIN Call_Agents ca ON c.Agent_ID = ca.Agent_ID
        LEFT JOIN Prompt_Store ps ON ca.Prompt_ID = ps.Prompt_ID
        WHERE c.Company_ID = ?
        ORDER BY c.Call_DateTime DESC
      `, [companyId]);

      res.json(calls);
    } catch (error) {
      console.error('[CallController] Error getting call history:', error);
      res.status(500).send('Failed to fetch call history');
    }
  }

  async getTranscript(req, res) {
    const token = req.headers.authorization?.split(' ')[1];
    if (!token) {
      return res.status(401).json({ message: 'No token provided' });
    }

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const userId = decoded.userId;
      const callId = req.params.callId;

      const [calls] = await this.callRepository.mysqlPool.query(
        'SELECT Transcript_ID FROM Calls WHERE Call_ID = ?',
        [callId]
      );
      if (calls.length === 0) {
        return res.status(404).json({ message: 'Call not found or access denied' });
      }

      const transcriptId = calls[0].Transcript_ID;

      const transcript = await Transcript.findOne({ conversation_id: callId });
      if (!transcript) {
        return res.status(404).json({ message: 'Transcript not found' });
      }

      res.json(transcript);
    } catch (error) {
      console.error('[CallController] Get transcript error:', error);
      if (error.name === 'JsonWebTokenError') {
        return res.status(401).json({ message: 'Invalid token' });
      }
      res.status(500).json({ message: 'Failed to fetch transcript', error: error.message });
    }
  }

  async getCallRecordings(req, res) {
    try {
      // Get all calls with Twilio Call SIDs
      const [calls] = await this.callRepository.mysqlPool.query(`
        SELECT CALL_ID, Twilio_Call_SID FROM Calls 
        WHERE Twilio_Call_SID IS NOT NULL
      `);

      if (!calls || calls.length === 0) {
        return res.status(404).json({ message: 'No calls found with Twilio Call SIDs' });
      }

      // Get all recordings from Twilio
      const recordingPromises = calls.map(async (call) => {
        try {
          const recording = await this.twilioClient.calls(call.Twilio_Call_SID).recordings.list();

          if (recording && recording.length > 0) {
            return {
              callId: call.CALL_ID,
              callSid: call.Twilio_Call_SID,
              recordingSid: recording[0].sid,
              duration: recording[0].duration,
              dateCreated: recording[0].dateCreated,
            };
          }
          throw new Error('No recording found');
        } catch (error) {
          console.error(
            `[CallController] Error fetching recording for call ${call.Twilio_Call_SID}:`,
            error
          );
          throw {
            callId: call.CALL_ID,
            callSid: call.Twilio_Call_SID,
            error: error.message,
          };
        }
      });

      const results = await Promise.allSettled(recordingPromises);

      const successfulResults = results
        .filter((result) => result.status === 'fulfilled')
        .map((result) => result.value);

      const failedResults = results
        .filter((result) => result.status === 'rejected')
        .map((result) => result.reason);

      // Update database with successful results
      if (successfulResults.length > 0) {
        const updateQuery = `
          UPDATE Calls 
          SET Recording_URL = CASE CALL_ID
            ${successfulResults.map((recording) => `WHEN '${recording.callId}' THEN '${recording.recordingSid}'`).join('\n')}
            ELSE Recording_URL
          END
          WHERE CALL_ID IN (${successfulResults.map((recording) => `'${recording.callId}'`).join(',')})
        `;

        await this.callRepository.mysqlPool.query(updateQuery);
      }

      res.json({
        success: successfulResults,
        failed: failedResults,
      });
    } catch (error) {
      console.error('[CallController] Error getting call recordings:', error);
      res.status(500).json({
        message: 'Failed to fetch call recordings',
        error: error.message,
      });
    }
  }

  async getRecordingFile(req, res) {
    try {
      const { recordingSid } = req.params;

      // Get the recording data from Twilio
      const recording = await this.twilioClient.recordings(recordingSid).fetch();

      if (!recording) {
        return res.status(404).json({ message: 'Recording not found' });
      }

      // Get the recording URI
      const mediaUrl = recording.mediaUrl;
      const audioUrl = `${mediaUrl}.mp3`;

      const response = await axios({
        method: 'get',
        url: audioUrl,
        auth: {
          username: this.apiKeys.twilio.accountSid,
          password: this.apiKeys.twilio.authToken,
        },
        responseType: 'arraybuffer',
      });

      res.setHeader('Content-Type', 'audio/mpeg');
      res.setHeader('Content-Length', response.data.length);
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Accept-Ranges', 'bytes');
      res.setHeader('Content-Disposition', `inline; filename="recording-${recordingSid}.mp3"`);

      res.send(response.data);
    } catch (error) {
      console.error('[CallController] Error getting recording file:', error);
      res.status(500).json({
        message: 'Failed to fetch recording file',
        error: error.message,
      });
    }
  }

  async handleRecordingCallback(req, res) {
    console.log('[CallController] Received recording callback:', req.body);

    const { CallSid, RecordingSid, RecordingStatus, RecordingDuration } = req.body;

    if (RecordingStatus === 'completed') {
      try {
        const [calls] = await this.callRepository.mysqlPool.query(
          'SELECT Call_ID FROM Calls WHERE Twilio_Call_SID = ?',
          [CallSid]
        );

        if (calls && calls.length > 0) {
          const callId = calls[0].Call_ID;

          await this.callRepository.mysqlPool.query(
            'UPDATE Calls SET Recording_URL = ?, Call_Duration = ? WHERE Call_ID = ?',
            [RecordingSid, RecordingDuration, callId]
          );

          console.log(`[CallController] Updated recording URL for call ${callId}: ${RecordingSid}`);
        } else {
          console.warn(`[CallController] No call found with Twilio Call SID: ${CallSid}`);
        }
      } catch (error) {
        console.error('[CallController] Error updating recording URL:', error);
      }
    }

    res.status(200).send('OK');
  }

  async getLiveCalls(req, res) {
    const data = await this.twilioClient.calls.list({ status: 'in-progress' });
    res.json(data);
  }

  async handleInboundCall(callSid, fromNumber, agentNumber) {
    try {
      // Generate internal call ID
      const randomNum = Math.floor(10000 + Math.random() * 90000);
      const internalCallID = `call_${Date.now()}_${randomNum}`;
      
      const userTag = `[user:${fromNumber || 'unknown'} ${agentNumber || 'unknown'}]`;
      console.log(`${userTag} [CallController] Inbound call:`, internalCallID);

      // Get agent by phone number
      const [agent] = await this.callRepository.mysqlPool.query(
        'SELECT Agent_ID, Prompt_ID, Prompt_Version FROM Call_Agents WHERE Twilio_Number = ?',
        [agentNumber]
      );

      const agentId = agent && agent.length > 0 ? agent[0].Agent_ID : null;
      const promptId = agent && agent.length > 0 ? agent[0].Prompt_ID : null;
      const promptVersion = agent && agent.length > 0 ? agent[0].Prompt_Version : null;
      
      let companyId = null;
      if (promptId) {
        const prompt = await this.promptRepository.getPromptById(promptId);
        companyId = prompt ? prompt.Company_ID : null;
      }

      if (!agentId) {
        throw new Error('Agent not found');
      }

      // Fetch prompt content if promptId is available
      let promptContent = null;
      if (promptId && promptId !== 'unknown') {
        try {
          promptContent = await this.promptService.getPromptContent(promptId, promptVersion);
        } catch (err) {
          console.warn(
            `[CallController] Could not fetch prompt content for promptId ${promptId}:`,
            err.message
          );
        }
      }

      // Insert call record
      await this.callRepository.mysqlPool.query(
        `
        INSERT INTO Calls (
          Call_ID, Call_DateTime, Twilio_Call_SID, Caller_Number, Call_Status, Call_Direction, Agent_ID, To_Number, Company_ID
        ) VALUES (?, Now(), ?, ?, ?, ?, ?, ?, ?)
      `,
        [internalCallID, callSid, fromNumber, 'In Progress', 'Inbound', agentId, agentNumber, companyId]
      );

      // Store prompt data in callService staging area
      const promptData = {
        internalCallID,
        promptContent: promptContent, // Set the fetched prompt content
        promptId: promptId || 'unknown',
        promptVersion: '1',
        agentId,
        clientName: 'Unknown',
        campaignType: 'Inbound',
        fromNumber: agentNumber,
        toNumber: fromNumber,
        twilioCallSid: callSid,
      };

      this.callService.stagePromptData(callSid, promptData);

      console.log(`[CallController] Initial call record inserted:`, internalCallID);
      console.log(`[CallController] Staged prompt data for callSid:`, callSid, promptData);

      return internalCallID;
    } catch (error) {
      const userTag = `[user:${fromNumber || 'unknown'} ${agentNumber || 'unknown'}]`;
      console.error(`${userTag} [CallController] Error inserting call record:`, error);
      throw error;
    }
  }

  async getOngoingCallsByAgent(req, res) {
    const { agentId } = req.params;
    try {
      const [calls] = await this.callRepository.mysqlPool.query(
        `SELECT 
            Twilio_Stream_ID as streamId, 
            Client_Name as clientName, 
            Caller_Number as callerNumber, 
            Call_Direction as callDirection,
            DATE_FORMAT(Created_At, '%Y-%m-%d %H:%i:%s') as createdAt,
            To_Number as toNumber, 
            Twilio_Call_SID as twilioCallSid 
          FROM Calls 
          WHERE Agent_ID = ? AND Call_Status = ?`,
        [agentId, 'In Progress']
      );

      const formattedCalls = calls.map((call) => {
        let createdAtIso = null;
        if (call.createdAt) {
          createdAtIso = new Date(call.createdAt + ' UTC').toISOString();
        }

        return {
          ...call,
          createdAt: createdAtIso,
        };
      });

      res.json(formattedCalls);
    } catch (error) {
      console.error('[CallController] Error getting ongoing calls by agent:', error);
      res
        .status(500)
        .json({ message: 'Failed to fetch ongoing calls by agent', error: error.message });
    }
  }

  async handleCallStatus(req, res) {
    const { CallSid, CallStatus, Direction, To, From } = req.body;
    const userTag = this.getUserTagByCallSid(CallSid, Direction, To, From);
    console.log(`${userTag} [CallController] Agent call status update:`, req.body);

    try {
      let status = 'In Progress';
      switch (CallStatus) {
        case 'completed':
          status = 'Success';
          break;
        case 'canceled':
          status = 'Canceled';
          break;
        case 'busy':
        case 'no-answer':
        case 'failed':
          status = 'Failed';
          break;
        case 'queued':
        case 'ringing':
        case 'in-progress':
        default:
          status = 'In Progress';
      }
      if (!['queued', 'ringing', 'in-progress'].includes(CallStatus)) {
        await this.callRepository.mysqlPool.query(
          'UPDATE Calls SET Call_Status = ?, Updated_At = NOW() WHERE Twilio_Call_SID = ?',
          [status, CallSid]
        );
        console.log(
          `[CallRepository] Call_Status updated to ${status} for Twilio_Call_SID: ${CallSid}`
        );
      }

      const statusesToMakeAvailable = ['completed', 'no-answer', 'failed', 'busy', 'canceled'];
      let twilioNumber;
      if (Direction === 'inbound') {
        twilioNumber = To;
      } else if (Direction === 'outbound-api') {
        twilioNumber = From;
      }
      if (statusesToMakeAvailable.includes(CallStatus) && this.callService.agentStatusService) {
        this.callService.agentStatusService.updateAgentStatusByPhoneNumber(twilioNumber, 'available');
      }

      res.sendStatus(200);
    } catch (error) {
      console.error('[CallController] Error handling call status:', error);
      res.status(500).json({ message: 'Failed to handle Call Status', error: error.message });
    }
  }
}

module.exports = { CallController };
