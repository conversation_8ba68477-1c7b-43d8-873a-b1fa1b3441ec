// call-management/controllers/TwilioController.js
const twilio = require('twilio');
const VoiceResponse = require('twilio').twiml.VoiceResponse;
const { TwilioService } = require('../services/TwilioService');
const { CallRepository } = require('../repositories/CallRepository');

class TwilioController {
  constructor(app, apiKeys, callService) {
    this.app = app;
    this.twilioclient = new twilio(apiKeys.twilio.accountSid, apiKeys.twilio.authToken);
    this.twilioService = new TwilioService(this.twilioclient);
    this.callService = callService;
    this.callRepository = new CallRepository();
    this.setupRoutes();
  }

  setupRoutes() {
    this.app.post('/voice', this.handleVoiceCallback.bind(this));

    this.app.get('/', this.handleIndex.bind(this));
  }

  handleIndex(req, res) {
    const filePath = this.twilioService.getIndexFilePath();
    res.sendFile(filePath);
  }

  handleVoiceCallback(req, res) {
    const twiml = new VoiceResponse();
    const streamSid = this.callService.getCallState(req.body.CallSid)?.streamSid;

    if (streamSid) {
      const connect = twiml.connect();
      connect.stream({
        url: `wss://${req.headers.host}/`,
        name: 'CallStream',
      });
    } else {
      twiml.say('Not able to connect');
      twiml.pause({ length: 60 });
    }

    res.type('text/xml');
    res.send(twiml.toString());
  }

  async transferCall(callSid, host) {
    try {
      const voiceWebhookUrl = `https://${host}/voice`;
      await this.twilioclient.calls(callSid).update({
        url: voiceWebhookUrl,
        method: 'POST',
      });
      console.log('Call redirected to /voice for conference setup.');
    } catch (error) {
      console.error('Error during transfer:', error);
      throw error;
    }
  }
}

module.exports = { TwilioController };
