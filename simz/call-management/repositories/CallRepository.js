// call-management/repositories/CallRepository.js
const mysql = require('mysql2/promise');
const mongoose = require('mongoose');
const Transcript = require('../models/Transcript');

class CallRepository {
  constructor() {
    this.mysqlPool = mysql.createPool({
      host: process.env.MYSQL_HOST,
      user: process.env.MYSQL_USER,
      password: process.env.MYSQL_PASSWORD,
      database: process.env.MYSQL_DATABASE,
    });
  }

  async saveCallData(streamSid, messages, callSid) {
    if (!messages.length) {
      console.log('[CallRepository] No messages to save');
      return;
    }

    const isValidTimestamp = (timestamp) => {
      try {
        const date = new Date(timestamp);
        return date instanceof Date && !isNaN(date) && date.getTime() > 0;
      } catch (error) {
        console.error('[CallRepository] Invalid timestamp:', timestamp, error);
        return false;
      }
    };

    const validMessages = messages.filter((msg) => {
      if (!msg.timestamp) {
        console.error('[CallRepository] Missing timestamp in message:', msg);
        return false;
      }
      return isValidTimestamp(msg.timestamp);
    });

    if (validMessages.length === 0) {
      console.error('[CallRepository] No valid timestamps found in messages');
      return;
    }

    const randomNum = Math.floor(10000 + Math.random() * 90000);
    const internalCallID = `call_${Date.now()}_${randomNum}`;

    try {
      const firstMessageTime = new Date(validMessages[0].timestamp);
      const lastMessageTime = new Date(validMessages[validMessages.length - 1].timestamp);
      const callDateTime = firstMessageTime.toISOString().slice(0, 19).replace('T', ' ');
      const callDuration = (lastMessageTime.getTime() - firstMessageTime.getTime()) / 1000;
      const clientName =
        validMessages
          .find((m) => m.role === 'assistant' && m.content.includes('speaking with'))
          ?.content.match(/speaking with (\w+)/)?.[1] || 'Unknown';
      const endTime = new Date().toISOString();

      const transcriptDoc = new Transcript({
        _id: new mongoose.Types.ObjectId(),
        conversation_id: internalCallID,
        twilio_sid: streamSid,
        transcript: validMessages
          .map((msg) => ({
            speaker: msg.role === 'user' ? 'user' : msg.role === 'admin' ? 'admin' : 'agent',
            text: msg.content,
            timestamp: new Date(msg.timestamp).toISOString(),
            Status: msg.interrupted ? 'Interrupted' : 'Complete',
          }))
          .filter((msg) => msg.text !== ''),
      });
      await transcriptDoc.save();
      const transcriptId = transcriptDoc._id.toString();
      console.log(
        `[CallRepository] Transcript saved to MongoDB: ${internalCallID}, Transcript_ID: ${transcriptId}`
      );

      await this.mysqlPool.query(
        `
        INSERT INTO Calls (
          Call_ID, Call_DateTime, Twilio_Stream_ID, Client_Name, Call_Status, Call_Duration, Transcription_Status, Transcript_ID, End_Time
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `,
        [
          internalCallID,
          callDateTime,
          streamSid,
          clientName,
          'Success',
          callDuration,
          'Completed',
          transcriptId,
          endTime,
        ]
      );
      console.log(`[CallRepository] Call saved to MySQL: ${internalCallID}`);
    } catch (error) {
      console.error('[CallRepository] Error saving call data:', error);
      throw error;
    }
  }

  // Update existing call data with specified fields
  async updateCallData(internalCallID, streamSid, messages, endTime) {
    if (!internalCallID) {
      console.error('[CallRepository] internalCallID is required for update');
      return;
    }
    if (!messages || !messages.length) {
      console.log('[CallRepository] No messages to update');
      return;
    }

    // 验证时间戳
    const isValidTimestamp = (timestamp) => {
      try {
        const date = new Date(timestamp);
        return date instanceof Date && !isNaN(date) && date.getTime() > 0;
      } catch (error) {
        console.error('[CallRepository] Invalid timestamp:', timestamp, error);
        return false;
      }
    };

    // 确保所有消息都有有效的时间戳
    const validMessages = messages.filter((msg) => {
      if (!msg.timestamp) {
        console.error('[CallRepository] Missing timestamp in message:', msg);
        return false;
      }
      return isValidTimestamp(msg.timestamp);
    });
    if (validMessages.length === 0) {
      console.error('[CallRepository] No valid timestamps found in messages');
      return;
    }

    try {
      const firstMessageTime = new Date(validMessages[0].timestamp);
      const lastMessageTime = new Date(validMessages[validMessages.length - 1].timestamp);
      const callDateTime = firstMessageTime.toISOString().slice(0, 19).replace('T', ' ');
      const callDuration = (lastMessageTime.getTime() - firstMessageTime.getTime()) / 1000;
      const clientName =
        validMessages
          .find((m) => m.role === 'assistant' && m.content.includes('speaking with'))
          ?.content.match(/speaking with (\w+)/)?.[1] || 'Unknown';

      const transcriptDoc = new Transcript({
        _id: new mongoose.Types.ObjectId(),
        conversation_id: internalCallID,
        twilio_sid: streamSid,
        transcript: validMessages
          .map((msg) => ({
            speaker: msg.role === 'user' ? 'user' : msg.role === 'admin' ? 'admin' : 'agent',
            text: msg.content,
            timestamp: new Date(msg.timestamp).toISOString(),
            Status: msg.interrupted ? 'Interrupted' : 'Complete',
          }))
          .filter((msg) => msg.text !== ''),
      });
      await transcriptDoc.save();
      const transcriptId = transcriptDoc._id.toString();
      console.log(
        `[CallRepository] Transcript saved to MongoDB: ${internalCallID}, Transcript_ID: ${transcriptId}`
      );

      const updates = {
        Twilio_Stream_ID: streamSid,
        Call_DateTime: callDateTime,
        Call_Duration: callDuration,
        Call_Status: 'Success',
        Transcription_Status: 'Completed',
        Transcript_ID: transcriptId,
        End_Time: endTime || new Date().toISOString(),
      };

      const fields = Object.keys(updates)
        .map((key) => `${key} = ?`)
        .join(', ');
      const values = Object.values(updates);
      values.push(internalCallID);

      await this.mysqlPool.query(
        `UPDATE Calls SET ${fields}, Updated_At = NOW() WHERE Call_ID = ?`,
        values
      );
      console.log(`[CallRepository] Call updated in MySQL: ${internalCallID}`);
    } catch (error) {
      console.error('[CallRepository] Error updating call data:', error);
      throw error;
    }
  }
}

module.exports = { CallRepository };
