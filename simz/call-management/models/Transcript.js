const mongoose = require('mongoose');

const transcriptEntrySchema = new mongoose.Schema({
  speaker: {
    type: String,
    enum: ['user', 'agent', 'admin'],
    required: true,
  },
  text: {
    type: String,
    required: true,
  },
  timestamp: {
    type: String, // 存储 ISO 格式时间戳
    required: true,
  },
  Status: {
    type: String,
    enum: ['Complete', 'Interrupted'],
    required: true,
  },
});

const transcriptSchema = new mongoose.Schema({
  conversation_id: {
    type: String,
    required: true,
  },
  twilio_sid: {
    type: String,
    required: true,
  },
  transcript: [transcriptEntrySchema],
});

module.exports = mongoose.model('Transcript', transcriptSchema, 'transcripts');
