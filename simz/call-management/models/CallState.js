// call-management/models/CallState.js
/**
 * Represents the state of a single call, managing all relevant properties and behaviors.
 */
// call-management/models/CallState.js
class CallState {
  constructor() {
    this.state = {
      host: null,
      threadId: null,
      callID: null,
      streamSid: null,
      currentCallSid: null,
      audioQueue: [],
      processingQueue: false,
      currentPlayingAudio: null,
      coughingFlag: false,
      lastTranscript: '',
      lastTimestamp: 0,
      firstAudio: true,
      isGeneratingResponse: false,
      introSaid: false,
      userQualify: false,
      currentRunId: '',
      hasTriedCancel: false,
      thisIsACallBack: false,
      withBGN: false,
      backgroundAudioPlaying: false,
      cannotBeCutBuffer: false,
      transferInProgress: false,
      waitClientResponseTimer: null,
      ws: null,
      recentPhrases: [],
      messages: [],
      textToSpeechConversionTimeMap: new Map(),
      openaiStartTime: Date.now(),
      audioTimeline: [], // Store the audio timeline of each response
      isProcessingLLM: false,
      shouldCancelLLM: false,
      lastTranscriptTime: 0,
      initialMessagePlayed: false,
      promptData: null,
      leadData: null,
    };
  }

  updateState(updates) {
    this.state = { ...this.state, ...updates };
  }

  reset() {
    if (this.state.waitClientResponseTimer) {
      clearTimeout(this.state.waitClientResponseTimer);
    }
    this.state = {
      host: null,
      threadId: null,
      callID: null,
      streamSid: null,
      currentCallSid: null,
      audioQueue: [],
      processingQueue: false,
      currentPlayingAudio: null,
      coughingFlag: false,
      lastTranscript: '',
      lastTimestamp: 0,
      firstAudio: true,
      isGeneratingResponse: false,
      introSaid: false,
      userQualify: false,
      currentRunId: '',
      hasTriedCancel: false,
      thisIsACallBack: false,
      withBGN: false,
      backgroundAudioPlaying: false,
      cannotBeCutBuffer: false,
      transferInProgress: false,
      waitClientResponseTimer: null,
      ws: null,
      recentPhrases: [],
      messages: [],
      textToSpeechConversionTimeMap: new Map(),
      openaiStartTime: Date.now(),
      audioTimeline: [],
      isProcessingLLM: false,
      shouldCancelLLM: false,
      lastTranscriptTime: 0,
      initialMessagePlayed: false,
      promptData: null,
      leadData: null,
    };
  }

  getState(property) {
    if (property) {
      return this.state[property];
    }
    return { ...this.state };
  }

  addToAudioTimeline(text, filename, startTime, duration) {
    console.log(`[CallState] Adding to audio timeline:`, { text, filename, startTime, duration });
    const endTime = startTime + duration;
    this.state.audioTimeline.push({ text, filename, startTime, endTime });
  }

  getInterruptedSentence(currentTime) {
    const timeline = this.state.audioTimeline;
    for (let i = 0; i < timeline.length; i++) {
      const { startTime, endTime, text } = timeline[i];
      if (currentTime >= startTime && currentTime <= endTime) {
        const interruptedSentence = { text, startTime, endTime, interruptedAt: currentTime };
        console.log(`[CallState] Found interrupted sentence:`, interruptedSentence);
        this.updateMessagesWithInterruption(interruptedSentence);
        return interruptedSentence;
      }
    }
    return null;
  }

  updateMessagesWithInterruption(interruptedSentence) {
    console.log(`[CallState] Updating messages with interruption`);
    const messages = this.state.messages || [];
    if (!messages.length) return;

    const lastMessage = messages[messages.length - 1];
    if (lastMessage && lastMessage.role === 'assistant') {
      const fullResponse = lastMessage.content;
      const interruptedText = interruptedSentence.text.replace(/\.{3}$/, '').trim();

      const interruptIndex = fullResponse.indexOf(interruptedText);
      if (interruptIndex !== -1) {
        const uninterruptedContent = fullResponse.substring(0, interruptIndex).trim();
        lastMessage.content = uninterruptedContent || '...'; // Concer case: do not remove whole respose, grok will return you an error
        lastMessage.interrupted = true;
        console.log(`[CallState] Updated message due to interruption:`, lastMessage);
      } else {
        console.warn(
          `[CallState] Interrupted text "${interruptedText}" not found in full response`
        );
      }
    }
    this.state.messages = messages;
  }
}

module.exports = CallState;
