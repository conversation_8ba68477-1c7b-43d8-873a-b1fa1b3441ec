const express = require('express');
const path = require('path');
const { GrokAPIService } = require('../llm-integration/services/GrokAPIService.js'); 
const { PromptService } = require('../llm-integration/services/PromptService.js');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const app = express();


app.use(express.json());
app.use(express.static(__dirname)); 


const grokService = new GrokAPIService();
const promptService = new PromptService();


app.get('/inbound-prompt', (req, res) => {
    res.sendFile(path.join(__dirname, 'GrokInterface.html')); 
});

app.get('/simz-steak-prompt', (req, res) => {
    res.sendFile(path.join(__dirname, 'SimzSteakInterface.html')); 
});

app.get('/api/get-inbound-prompt', async (req, res) => {
    try {
        const prompt = await promptService.getInboundPrompt();
        res.send(prompt); 
        console.log('[public/server.js > app.get] get prompt success');
    } catch (error) {
        res.status(500).send('Failed to get prompt');
        console.error('[public/server.js > app.get] get prompt failed:', error);
    }
});

app.get('/api/get-simz-steak-prompt', async (req, res) => {
    try {
        const prompt = await promptService.getSimzSteakHousePrompt();
        res.send(prompt); 
        console.log('[public/server.js > app.get] get Simz Steak House prompt success');
    } catch (error) {
        res.status(500).send('Failed to get Simz Steak House prompt');
        console.error('[public/server.js > app.get] get Simz Steak House prompt failed:', error);
    }
});

app.post('/api/save-prompt', async (req, res) => {
    const { prompt } = req.body;
    try {
        const result = await grokService.savePrompt(prompt);
        res.json(result);
        console.log('[public/server.js > app.post] save prompt success');
    } catch (error) {
        res.status(500).json({ message: 'Failed to save prompt' });
        console.error('[public/server.js > app.post] save prompt failed:', error);
    }
});

app.post('/api/save-inbound-prompt', async (req, res) => {
    const { prompt } = req.body;
    try {
        const result = await promptService.saveInboundPrompt(prompt);
        res.json(result);
        console.log('[public/server.js > app.post] save inbound prompt success');
    } catch (error) {
        res.status(500).json({ message: 'Failed to save inbound prompt' });
        console.error('[public/server.js > app.post] save inbound prompt failed:', error);
    }
});

app.post('/api/save-simz-steak-prompt', async (req, res) => {
    const { prompt } = req.body;
    try {
        const result = await promptService.saveSimzSteakHousePrompt(prompt);
        res.json(result);
        console.log('[public/server.js > app.post] save Simz Steak House prompt success');
    } catch (error) {
        res.status(500).json({ message: 'Failed to save Simz Steak House prompt' });
        console.error('[public/server.js > app.post] save Simz Steak House prompt failed:', error);
    }
});

app.post('/api/grok-chat', async (req, res) => {
    const { message } = req.body;
    try {
        const result = await grokService.chatWithGrokSystemPrompt(message);
        res.json(result);
        console.log('[public/server.js > app.post] chat with Grok success');
    } catch (error) {
        res.status(500).json({ message: 'Failed to chat with Grok' });
        console.error('[public/server.js > app.post] chat with Grok failed:', error);
    }
});

app.post('/api/chat-with-inbound-prompt', async (req, res) => {
    const { message } = req.body;
    try {
        const prompt = await promptService.getInboundPrompt();
        const result = await grokService.chatWithGrokSystemPrompt(message, prompt);
        res.json(result);
        console.log('[public/server.js > app.post] chat with inbound prompt success');
    } catch (error) {
        res.status(500).json({ message: 'Failed to chat with inbound prompt' });
        console.error('[public/server.js > app.post] chat with inbound prompt failed:', error);
    }
});

app.post('/api/chat-with-simz-steak-prompt', async (req, res) => {
    const { message } = req.body;
    try {
        const prompt = await promptService.getSimzSteakHousePrompt();
        const result = await grokService.chatWithGrokSystemPrompt(message, prompt);
        res.json(result);
        console.log('[public/server.js > app.post] chat with Simz Steak House prompt success');
    } catch (error) {
        res.status(500).json({ message: 'Failed to chat with Simz Steak House prompt' });
        console.error('[public/server.js > app.post] chat with Simz Steak House prompt failed:', error);
    }
});

app.post('/api/clear-chat', async (req, res) => {
    try {
        const result = await grokService.clearChatHistory();
        res.json(result);
    } catch (error) {
        res.status(500).json({ message: 'Failed to clear chat' });
        console.error('[public/server.js > app.post] clear chat failed:', error);
    }
});

app.post('/api/clear-inbound-chat', async (req, res) => {
    try {
        const result = await grokService.clearChatHistory();
        res.json(result);
        console.log('[public/server.js > app.post] clear inbound chat success');
    } catch (error) {
        res.status(500).json({ message: 'Failed to clear inbound chat' });
        console.error('[public/server.js > app.post] clear inbound chat failed:', error);
    }
});

app.post('/api/clear-simz-steak-chat', async (req, res) => {
    try {
        const result = await grokService.clearChatHistory();
        res.json(result);
        console.log('[public/server.js > app.post] clear Simz Steak House chat success');
    } catch (error) {
        res.status(500).json({ message: 'Failed to clear Simz Steak House chat' });
        console.error('[public/server.js > app.post] clear Simz Steak House chat failed:', error);
    }
});


const PORT = 3001;
app.listen(PORT, () => {
    console.log(`[public/server.js > app.listen] Server running on port ${PORT}`);
});