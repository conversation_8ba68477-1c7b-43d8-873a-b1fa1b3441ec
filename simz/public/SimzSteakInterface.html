<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grok Chat Interface - Simz Steak House</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f8f5f0;
            color: #333;
        }
        #prompt-area, #chat-area {
            margin-bottom: 20px;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        #prompt-text {
            width: 100%;
            height: 150px;
            margin-bottom: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: inherit;
        }
        #chat-history {
            border: 1px solid #ddd;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            margin-bottom: 10px;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        #user-input {
            width: 80%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            padding: 10px 15px;
            background-color: #8b0000;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 5px;
        }
        button:hover {
            background-color: #6d0000;
        }
        .message {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .user-message {
            background-color: #e6f7ff;
            border-left: 3px solid #1890ff;
        }
        .grok-message {
            background-color: #f6ffed;
            border-left: 3px solid #52c41a;
        }
        h2 {
            color: #8b0000;
            border-bottom: 2px solid #8b0000;
            padding-bottom: 5px;
        }
    </style>
</head>
<body>
    <div id="prompt-area">
        <h2>Simz Steak House - Prompt Settings (833-953-1720)</h2>
        <textarea id="prompt-text"></textarea>
        <button id="save-prompt">Save Prompt</button>
    </div>

    <div id="chat-area">
        <h2>Chat with Simz Steak House Prompt</h2>
        <div id="chat-history"></div>
        <input type="text" id="user-input" placeholder="Input Message...">
        <button id="send-message">Send</button>
        <button id="clear-chat">Clear Chat</button>
    </div>

    <script>
        // Load initial Prompt
        fetch('/api/get-simz-steak-prompt')
            .then(response => response.text())
            .then(data => {
                document.getElementById('prompt-text').value = data;
            })
            .catch(error => console.error('Failed to load the Prompt:', error));

        // Save Prompt
        document.getElementById('save-prompt').addEventListener('click', () => {
            const prompt = document.getElementById('prompt-text').value;
            fetch('/api/save-simz-steak-prompt', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ prompt })
            })
            .then(response => response.json())
            .then(data => alert(data.message))
            .catch(error => console.error('Failed to save the Prompt:', error));
        });

        // Function to send message
        function sendMessage() {
            const input = document.getElementById('user-input');
            const message = input.value.trim();
            if (message) {
                addMessageToHistory('user', message);
                input.value = '';

                fetch('/api/chat-with-simz-steak-prompt', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message })
                })
                .then(response => response.json())
                .then(data => addMessageToHistory('grok', data.response))
                .catch(error => console.error('Failed to send message:', error));
            }
        }

        // Send message on button click
        document.getElementById('send-message').addEventListener('click', sendMessage);

        // Send message on Enter key press
        document.getElementById('user-input').addEventListener('keydown', (event) => {
            if (event.key === 'Enter') {
                event.preventDefault();
                sendMessage();
            }
        });

        // Clear chat history
        document.getElementById('clear-chat').addEventListener('click', () => {
            fetch('/api/clear-simz-steak-chat', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('chat-history').innerHTML = '';
                alert(data.message);
            })
            .catch(error => console.error('Failed to clear chat:', error));
        });

        function addMessageToHistory(sender, content) {
            const history = document.getElementById('chat-history');
            const div = document.createElement('div');
            div.className = `message ${sender}-message`;
            div.textContent = `${sender === 'user' ? 'You' : 'Grok'}: ${content}`;
            history.appendChild(div);
            history.scrollTop = history.scrollHeight;
        }
    </script>
</body>
</html> 