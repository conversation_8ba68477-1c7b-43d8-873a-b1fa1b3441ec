<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Log Viewer</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    #logs { white-space: pre-wrap; background: #f4f4f4; padding: 10px; border: 1px solid #ddd; height: 300px; overflow-y: scroll; }
    table { width: 100%; border-collapse: collapse; margin-top: 20px; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
  </style>
</head>
<body>
  

  <h2>Call Information</h2>
  <table id="callTable">
    <thead>
      <tr>
        <th>Call Time</th>
        <th>Online</th>
        <th>Call Duration</th>
      </tr>
    </thead>
    <tbody></tbody>
  </table>

  <h1>Call Logs</h1>
  <div id="logs"></div>

  <script>
    const logContainer = document.getElementById('logs');
    const callTableBody = document.getElementById('callTable').querySelector('tbody');

    const ws = new WebSocket('ws://**********:30000');

    // Dynamic counters map
    const dynamicCounters = {};

    ws.onmessage = function(event) {
      const message = JSON.parse(event.data);
      console.log("Received message:", message);
      if (message.event === "log") {
        logContainer.textContent += message.data + '\n';
      } else if (message.event === "call-update") {
        updateCallTable(message.data);
      }
    };

    ws.onerror = function(event) {
      console.error("WebSocket error observed:", event);
    };

    ws.onopen = function(event) {
      console.log("WebSocket is open now.");
    };

    ws.onclose = function(event) {
      console.log("WebSocket is closed now.");
    };

    function updateCallTable(calls) {
      callTableBody.innerHTML = '';  // Clear the existing rows

      calls.forEach(call => {
        let row = document.createElement('tr');
        let startTimeCell = document.createElement('td');
        let isConnectedCell = document.createElement('td');
        let durationCell = document.createElement('td');
        
        startTimeCell.textContent = new Date(call.startTime).toLocaleString();
        isConnectedCell.textContent = call.isConnected ? "âœ”ï¸" : "âŒ";
        
        if (call.isConnected) {
          // If the call is currently connected, show a dynamic counter
          if (!dynamicCounters[call.callId]) {
            dynamicCounters[call.callId] = setInterval(() => {
              const start = new Date(call.startTime).getTime();
              const now = new Date().getTime();
              const seconds = Math.floor((now - start) / 1000);
              durationCell.textContent = formatDuration(seconds);
            }, 1000);
          }
        } else {
          if (dynamicCounters[call.callId]) {
            clearInterval(dynamicCounters[call.callId]);
            delete dynamicCounters[call.callId];
          }
          const duration = Math.floor((new Date(call.endTime).getTime() - new Date(call.startTime).getTime()) / 1000);
          durationCell.textContent = formatDuration(duration);
        }

        row.appendChild(startTimeCell);
        row.appendChild(isConnectedCell);
        row.appendChild(durationCell);
        callTableBody.appendChild(row);
      });
    }

    function formatDuration(seconds) {
      const h = Math.floor(seconds / 3600);
      const m = Math.floor((seconds % 3600) / 60);
      const s = seconds % 60;
      return `${h}h ${m}m ${s}s`;
    }
  </script>
</body>
</html>