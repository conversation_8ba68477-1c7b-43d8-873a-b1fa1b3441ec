<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grok Chat Interface</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        #prompt-area, #chat-area {
            margin-bottom: 20px;
        }
        #prompt-text {
            width: 100%;
            height: 150px;
            margin-bottom: 10px;
        }
        #chat-history {
            border: 1px solid #ccc;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            margin-bottom: 10px;
        }
        #user-input {
            width: 80%;
            padding: 5px;
        }
        .message {
            margin: 10px 0;
        }
        .user-message {
            color: blue;
        }
        .grok-message {
            color: green;
        }
    </style>
</head>
<body>
    <div id="prompt-area">
        <h2>Inbound Prompt Settings - SimZ Fine Hotel - 3239917886</h2>
        <textarea id="prompt-text"></textarea>
        <button id="save-prompt">Save Prompt</button>
    </div>

    <div id="chat-area">
        <h2>Chat with Inbound Prompt</h2>
        <div id="chat-history"></div>
        <input type="text" id="user-input" placeholder="Input Message...">
        <button id="send-message">Send</button>
        <button id="clear-chat">Clear Chat</button>
    </div>

    <script>
        // Load initial Prompt
        fetch('/api/get-inbound-prompt')
            .then(response => response.text())
            .then(data => {
                document.getElementById('prompt-text').value = data;
            })
            .catch(error => console.error('Failed to load the Prompt:', error));

        // Save Prompt
        document.getElementById('save-prompt').addEventListener('click', () => {
            const prompt = document.getElementById('prompt-text').value;
            fetch('/api/save-inbound-prompt', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ prompt })
            })
            .then(response => response.json())
            .then(data => alert(data.message))
            .catch(error => console.error('Failed to save the Prompt:', error));
        });

        // Function to send message
        function sendMessage() {
            const input = document.getElementById('user-input');
            const message = input.value.trim();
            if (message) {
                addMessageToHistory('user', message);
                input.value = '';

                fetch('/api/chat-with-inbound-prompt', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message })
                })
                .then(response => response.json())
                .then(data => addMessageToHistory('grok', data.response))
                .catch(error => console.error('Failed to send message:', error));
            }
        }

        // Send message on button click
        document.getElementById('send-message').addEventListener('click', sendMessage);

        // Send message on Enter key press
        document.getElementById('user-input').addEventListener('keydown', (event) => {
            if (event.key === 'Enter') {
                event.preventDefault();
                sendMessage();
            }
        });

        // Clear chat history
        document.getElementById('clear-chat').addEventListener('click', () => {
            fetch('/api/clear-inbound-chat', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('chat-history').innerHTML = '';
                alert(data.message);
            })
            .catch(error => console.error('Failed to clear chat:', error));
        });

        function addMessageToHistory(sender, content) {
            const history = document.getElementById('chat-history');
            const div = document.createElement('div');
            div.className = `message ${sender}-message`;
            div.textContent = `${sender === 'user' ? 'You' : 'Grok'}: ${content}`;
            history.appendChild(div);
            history.scrollTop = history.scrollHeight;
        }
    </script>
</body>
</html>