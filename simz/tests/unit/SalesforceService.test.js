const { SalesforceService } = require('../../salesforce/services/SalesforceService');
const jsforce = require('jsforce');

// Mock jsforce
jest.mock('jsforce');

describe('SalesforceService', () => {
  let salesforceService;
  let mockConnection;

  beforeEach(() => {
    // Reset environment variables
    process.env.SALESFORCE_USERNAME = '<EMAIL>';
    process.env.SALESFORCE_PASSWORD = 'testpassword';
    process.env.SALESFORCE_SECURITY_TOKEN = 'testtoken';

    mockConnection = {
      login: jest.fn(),
      query: jest.fn(),
      sobject: jest.fn().mockReturnValue({
        create: jest.fn(),
        update: jest.fn(),
        retrieve: jest.fn(),
        destroy: jest.fn(),
      }),
      logout: jest.fn(),
    };

    jsforce.Connection.mockImplementation(() => mockConnection);
    salesforceService = new SalesforceService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('connect', () => {
    it('should connect to Salesforce successfully', async () => {
      mockConnection.login.mockResolvedValue({ id: 'user123', organizationId: 'org123' });

      const result = await salesforceService.connect();

      expect(result).toBe(true);
      expect(salesforceService.isConnected).toBe(true);
      expect(mockConnection.login).toHaveBeenCalledWith('<EMAIL>', 'testpassword');
    });

    it('should throw error when connection fails', async () => {
      mockConnection.login.mockRejectedValue(new Error('Login failed'));

      await expect(salesforceService.connect()).rejects.toThrow('Login failed');
      expect(salesforceService.isConnected).toBe(false);
    });

    it('should throw error when credentials are missing', async () => {
      delete process.env.SALESFORCE_USERNAME;
      mockConnection.login.mockRejectedValue(
        new Error('INVALID_LOGIN: Invalid username, password, security token; or user locked out.')
      );

      await expect(salesforceService.connect()).rejects.toThrow('INVALID_LOGIN');
    });
  });

  describe('findLeadByPhoneNumber', () => {
    beforeEach(() => {
      mockConnection.login.mockResolvedValue({ id: 'user123' });
    });

    it('should find existing lead by phone number', async () => {
      const mockQueryResult = {
        records: [{ Id: '00QUi00000RVEYdMAP', LastModifiedDate: '2024-01-01' }],
      };
      const mockLead = {
        Id: '00QUi00000RVEYdMAP',
        FirstName: 'John',
        LastName: 'Doe',
        Phone: '+1234567890',
      };

      mockConnection.query.mockResolvedValue(mockQueryResult);

      const mockSobject = {
        retrieve: jest.fn().mockResolvedValue(mockLead),
      };
      mockConnection.sobject.mockReturnValue(mockSobject);

      const result = await salesforceService.findLeadByPhoneNumber('+1234567890');

      expect(result).toEqual(mockLead);
      expect(mockConnection.query).toHaveBeenCalledWith(
        "SELECT Id, LastModifiedDate FROM Lead WHERE SMS_Text_Phone__c = '+1234567890' ORDER BY LastModifiedDate DESC LIMIT 1"
      );
    });

    it('should create new lead when no existing lead found', async () => {
      mockConnection.query.mockResolvedValue({ records: [] });

      const mockSobject = {
        create: jest.fn().mockResolvedValue({ id: '00QUi00000NEW123', success: true }),
        retrieve: jest.fn().mockResolvedValue({
          Id: '00QUi00000NEW123',
          LastName: 'New Client',
          Phone: '+9999999999',
        }),
      };
      mockConnection.sobject.mockReturnValue(mockSobject);

      const result = await salesforceService.findLeadByPhoneNumber('+9999999999');

      expect(mockSobject.create).toHaveBeenCalledWith(
        expect.objectContaining({
          LastName: 'New Client',
          Company: 'SimZ AI',
          Phone: '+9999999999',
          Description: 'Transfer call from AI agent',
          Status: 'Open - Not Contacted',
          LeadSource: 'Phone',
        })
      );
      expect(result.Id).toBe('00QUi00000NEW123');
    });

    it('should throw error when query fails', async () => {
      mockConnection.query.mockRejectedValue(new Error('Query failed'));

      await expect(salesforceService.findLeadByPhoneNumber('+1234567890')).rejects.toThrow(
        'Query failed'
      );
    });
  });

  describe('createRecord', () => {
    beforeEach(() => {
      mockConnection.login.mockResolvedValue({ id: 'user123' });
    });

    it('should create Lead record successfully', async () => {
      const leadData = {
        First_Name__c: 'Jane',
        Last_Name__c: 'Smith',
        Phone: '+1555123456',
        Email: '<EMAIL>',
      };

      const mockSobject = {
        create: jest.fn().mockResolvedValue({
          id: '00QUi00000NEW123',
          success: true,
        }),
      };
      mockConnection.sobject.mockReturnValue(mockSobject);

      const result = await salesforceService.createRecord('Lead', leadData);

      expect(result.success).toBe(true);
      expect(result.id).toBe('00QUi00000NEW123');
      expect(mockConnection.sobject).toHaveBeenCalledWith('Lead');

      // Verify that Company was automatically added
      expect(mockSobject.create).toHaveBeenCalledWith(
        expect.objectContaining({
          ...leadData,
          Company: 'SimZ AI',
        })
      );
    });

    it('should handle creation failure', async () => {
      const leadData = { First_Name__c: 'Test' };

      const mockSobject = {
        create: jest.fn().mockResolvedValue({
          success: false,
          errors: ['Required field missing'],
        }),
      };
      mockConnection.sobject.mockReturnValue(mockSobject);

      const result = await salesforceService.createRecord('Lead', leadData);

      expect(result.success).toBe(false);
      expect(result.errors).toEqual(['Required field missing']);
    });

    it('should throw error when create operation fails', async () => {
      const leadData = { First_Name__c: 'Test' };

      const mockSobject = {
        create: jest.fn().mockRejectedValue(new Error('Network error')),
      };
      mockConnection.sobject.mockReturnValue(mockSobject);

      await expect(salesforceService.createRecord('Lead', leadData)).rejects.toThrow(
        'Network error'
      );
    });
  });

  describe('updateRecord', () => {
    const leadId = '00QUi00000RVEYdMAP';
    const leadData = {
      FirstName: 'John',
      LastName: 'Doe',
      Email: '<EMAIL>',
    };

    test('should update Lead record successfully', async () => {
      const mockSobject = {
        update: jest.fn().mockImplementation(async (data) => {
          return { id: data.Id, success: true };
        }),
      };
      mockConnection.sobject.mockReturnValue(mockSobject);

      const result = await salesforceService.updateRecord('Lead', leadData, leadId);

      expect(result.success).toBe(true);
      expect(result.results).toHaveLength(3); // FirstName, LastName, Email
      expect(result.errors).toBeUndefined();
      expect(mockConnection.sobject).toHaveBeenCalledWith('Lead');
      expect(mockSobject.update).toHaveBeenCalledTimes(3);
    });

    test('should handle update failure', async () => {
      const mockSobject = {
        update: jest.fn().mockImplementation(async (data) => {
          if (data.Email) {
            throw new Error('Invalid email format');
          }
          return { id: data.Id, success: true };
        }),
      };
      mockConnection.sobject.mockReturnValue(mockSobject);

      const result = await salesforceService.updateRecord('Lead', leadData, leadId);

      expect(result.success).toBe(false);
      expect(result.results).toHaveLength(2); // FirstName, LastName
      expect(result.errors).toHaveLength(1); // Email error
      expect(result.errors[0].field).toBe('Email');
      expect(result.errors[0].error).toBe('Invalid email format');
    });

    test('should handle connection timeout for all fields', async () => {
      const mockSobject = {
        update: jest.fn().mockRejectedValue(new Error('Connection timeout')),
      };
      mockConnection.sobject.mockReturnValue(mockSobject);

      const result = await salesforceService.updateRecord('Lead', leadData, leadId);

      expect(result.success).toBe(false);
      expect(result.results).toHaveLength(0);
      expect(result.errors).toHaveLength(3); // All fields failed
      expect(result.errors[0].error).toBe('Connection timeout');
      expect(result.errors[0].field).toBe('FirstName');
      expect(result.errors[1].field).toBe('LastName');
      expect(result.errors[2].field).toBe('Email');
    });
  });

  describe('deleteRecord', () => {
    it('should delete record successfully', async () => {
      mockConnection.sobject.mockReturnValue({
        destroy: jest.fn().mockResolvedValue({ id: '003XXXXXXXXXXXX', success: true }),
      });

      const result = await salesforceService.deleteRecord('Lead', '003XXXXXXXXXXXX');

      expect(mockConnection.sobject).toHaveBeenCalledWith('Lead');
      expect(result).toEqual({ id: '003XXXXXXXXXXXX', success: true });
    });

    it('should handle deletion failure', async () => {
      mockConnection.sobject.mockReturnValue({
        destroy: jest.fn().mockRejectedValue(new Error('删除失败')),
      });

      await expect(salesforceService.deleteRecord('Lead', '003XXXXXXXXXXXX')).rejects.toThrow(
        '删除失败'
      );
    });
  });

  describe('getLead', () => {
    it('should retrieve lead record successfully', async () => {
      const mockLead = { Id: '003XXXXXXXXXXXX', FirstName: '张', LastName: '三' };
      mockConnection.sobject.mockReturnValue({
        retrieve: jest.fn().mockResolvedValue(mockLead),
      });

      const result = await salesforceService.getLead('Lead', '003XXXXXXXXXXXX');

      expect(mockConnection.sobject).toHaveBeenCalledWith('Lead');
      expect(result).toEqual(mockLead);
    });

    it('should handle retrieval failure', async () => {
      mockConnection.sobject.mockReturnValue({
        retrieve: jest.fn().mockRejectedValue(new Error('记录未找到')),
      });

      await expect(salesforceService.getLead('Lead', '003XXXXXXXXXXXX')).rejects.toThrow(
        '记录未找到'
      );
    });
  });

  describe('query', () => {
    it('should execute SOQL query successfully', async () => {
      const mockQueryResult = {
        records: [{ Id: '003XXXXXXXXXXXX', Phone: '+1234567890' }],
        totalSize: 1,
      };
      mockConnection.query.mockResolvedValue(mockQueryResult);

      const result = await salesforceService.query(
        "SELECT Id, Phone FROM Lead WHERE Phone = '+1234567890'"
      );

      expect(mockConnection.query).toHaveBeenCalledWith(
        "SELECT Id, Phone FROM Lead WHERE Phone = '+1234567890'"
      );
      expect(result).toEqual(mockQueryResult);
    });

    it('should handle query failure', async () => {
      mockConnection.query.mockRejectedValue(new Error('查询失败'));

      await expect(salesforceService.query('INVALID SQL')).rejects.toThrow('查询失败');
    });
  });

  describe('describeObject', () => {
    it('should get object metadata successfully', async () => {
      const mockMetadata = {
        name: 'Lead',
        fields: [
          { name: 'Id', type: 'id' },
          { name: 'Phone', type: 'phone' },
        ],
      };
      mockConnection.sobject.mockReturnValue({
        describe: jest.fn().mockResolvedValue(mockMetadata),
      });

      const result = await salesforceService.describeObject('Lead');

      expect(mockConnection.sobject).toHaveBeenCalledWith('Lead');
      expect(result).toEqual(mockMetadata);
    });

    it('should handle describe failure', async () => {
      mockConnection.sobject.mockReturnValue({
        describe: jest.fn().mockRejectedValue(new Error('无法获取元数据')),
      });

      await expect(salesforceService.describeObject('Lead')).rejects.toThrow('无法获取元数据');
    });
  });

  describe('disconnect', () => {
    beforeEach(() => {
      mockConnection.login.mockResolvedValue({ id: 'user123' });
    });

    it('should disconnect from Salesforce successfully', async () => {
      // First connect
      await salesforceService.connect();

      // Then disconnect
      await salesforceService.disconnect();

      expect(mockConnection.logout).toHaveBeenCalled();
      expect(salesforceService.isConnected).toBe(false);
    });

    it('should handle logout errors gracefully', async () => {
      await salesforceService.connect();
      mockConnection.logout.mockImplementation(() => {
        throw new Error('Logout failed');
      });

      // Should not throw, just log warning and set disconnected state
      await expect(salesforceService.disconnect()).resolves.toBeUndefined();
      expect(salesforceService.isConnected).toBe(false);
    });
  });

  describe('integration scenarios', () => {
    beforeEach(() => {
      mockConnection.login.mockResolvedValue({ id: 'user123' });
    });

    it('should handle complete lead creation workflow', async () => {
      // First check if lead exists
      mockConnection.query.mockResolvedValue({
        records: [],
      });

      // Then create new lead
      const mockSobject = {
        create: jest.fn().mockResolvedValue({
          id: '00QUi00000NEW456',
          success: true,
        }),
        retrieve: jest.fn().mockResolvedValue({
          Id: '00QUi00000NEW456',
          LastName: 'New Client',
          Phone: '+1333444555',
        }),
      };
      mockConnection.sobject.mockReturnValue(mockSobject);

      const phoneNumber = '+1333444555';

      // Call findLeadByPhoneNumber which should create a new lead
      const result = await salesforceService.findLeadByPhoneNumber(phoneNumber);

      expect(result.Id).toBe('00QUi00000NEW456');
      expect(mockConnection.query).toHaveBeenCalled();
      expect(mockSobject.create).toHaveBeenCalled();
    });

    test('should handle complete lead update workflow', async () => {
      const existingLead = {
        Id: '00QUi00000EXISTING',
        Phone: '+1234567890',
      };

      // Mock findLeadByPhoneNumber to return existing lead
      mockConnection.query.mockResolvedValue({
        records: [existingLead],
        totalSize: 1,
      });

      // Mock getLead to return full lead details
      mockConnection.sobject.mockReturnValue({
        retrieve: jest.fn().mockResolvedValue(existingLead),
      });

      const foundLead = await salesforceService.findLeadByPhoneNumber('+1234567890');
      expect(foundLead.Id).toBe(existingLead.Id);

      const updateData = {
        FirstName: 'Updated',
        LastName: 'Name',
      };

      // Mock update to return success for each field
      const mockSobject = {
        update: jest.fn().mockImplementation(async (data) => {
          return { id: data.Id, success: true };
        }),
      };
      mockConnection.sobject.mockReturnValue(mockSobject);

      const updateResult = await salesforceService.updateRecord('Lead', updateData, foundLead.Id);
      expect(updateResult.success).toBe(true);
      expect(updateResult.results).toHaveLength(2); // FirstName, LastName
      expect(updateResult.errors).toBeUndefined();
    });
  });
});
