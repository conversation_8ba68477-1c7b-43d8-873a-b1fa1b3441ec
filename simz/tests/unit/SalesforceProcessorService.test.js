const {
  SalesforceProcessorService,
} = require('../../llm-integration/services/SalesforceProcessorService');
const { SalesforceService } = require('../../salesforce/services/SalesforceService');

// Mock dependencies
jest.mock('../../salesforce/services/SalesforceService');
jest.mock('openai');

describe('SalesforceProcessorService', () => {
  let salesforceProcessor;
  let mockSalesforceService;
  let mockOpenAI;

  beforeEach(() => {
    // Reset global variables
    global.messageHistory = [];
    global.callData = {};

    // Create mocks
    mockSalesforceService = {
      connect: jest.fn(),
      disconnect: jest.fn(),
      findLeadByPhoneNumber: jest.fn(),
      createRecord: jest.fn(),
      updateRecord: jest.fn(),
    };

    mockOpenAI = {
      chat: {
        completions: {
          create: jest.fn(),
        },
      },
    };

    // Mock OpenAI constructor
    require('openai').mockImplementation(() => mockOpenAI);

    // Mock SalesforceService constructor
    SalesforceService.mockImplementation(() => mockSalesforceService);

    salesforceProcessor = new SalesforceProcessorService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('storeConversationHistory', () => {
    it('should store conversation history correctly', () => {
      const messages = [
        { role: 'user', content: 'Hello' },
        { role: 'assistant', content: 'Hi there!' },
      ];
      const callSid = 'CA123456789';

      salesforceProcessor.storeConversationHistory(messages, callSid);

      expect(global.messageHistory).toHaveLength(1);
      expect(global.messageHistory[0]).toMatchObject({
        callSid,
        messages,
        processed: false,
      });
      expect(global.messageHistory[0].timestamp).toBeDefined();
    });

    it('should append multiple conversation histories', () => {
      const messages1 = [{ role: 'user', content: 'First call' }];
      const messages2 = [{ role: 'user', content: 'Second call' }];

      salesforceProcessor.storeConversationHistory(messages1, 'CA111');
      salesforceProcessor.storeConversationHistory(messages2, 'CA222');

      expect(global.messageHistory).toHaveLength(2);
      expect(global.messageHistory[0].callSid).toBe('CA111');
      expect(global.messageHistory[1].callSid).toBe('CA222');
    });
  });

  describe('processConversationForSalesforce', () => {
    it('should extract customer data from conversation successfully', async () => {
      const mockResponse = {
        choices: [
          {
            message: {
              content: JSON.stringify({
                customerName: 'John Smith',
                customerPhone: '+**********',
                customerEmail: '<EMAIL>',
                qualificationInfo: 'Qualified for settlement',
                clientQualifiedCampaign: 'Roundup',
                clientDiagnosis: "Non-Hodgkin's Lymphoma",
                clientDiagnosisDate: '03/15/2020',
                clientPersonallyPrepareHandleProduct: 'Yes',
                clientUnderstandFirmWillVerifyDiagnosis: 'Yes',
                clientEvidenceTypeForYourInjury: 'Medical Records',
                client1stExposed: '01/01/2010',
                clientLastExposed: '12/31/2019',
              }),
            },
          },
        ],
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse);

      const messages = [
        { role: 'user', content: 'My name is John Smith' },
        { role: 'assistant', content: 'Hello John, tell me about your case' },
        { role: 'user', content: 'I used Roundup and got lymphoma' },
      ];

      const result = await salesforceProcessor.processConversationForSalesforce(messages);

      expect(result.customerName).toBe('John Smith');
      expect(result.customerPhone).toBe('+**********');
      expect(result.clientQualifiedCampaign).toBe('Roundup');
      expect(result.clientDiagnosis).toBe("Non-Hodgkin's Lymphoma");
      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledWith({
        model: 'gpt-4',
        messages: expect.arrayContaining([
          {
            role: 'system',
            content: expect.stringContaining('You are an AI assistant specialized'),
          },
          { role: 'user', content: expect.stringContaining('Analyze this conversation') },
        ]),
        temperature: 0.1,
        max_tokens: 2000,
      });
    });

    it('should return default values when AI processing fails', async () => {
      mockOpenAI.chat.completions.create.mockRejectedValue(new Error('AI processing failed'));
      global.callData = { customerPhone: '+1987654321' };

      const messages = [{ role: 'user', content: 'Test message' }];
      const result = await salesforceProcessor.processConversationForSalesforce(messages);

      expect(result.customerName).toBe('Unknown Customer');
      expect(result.customerPhone).toBe('+1987654321');
      expect(result.qualificationInfo).toBe('Processing failed - data extraction error');
      expect(result.clientQualifiedCampaign).toBe('Not provided');
    });
  });

  describe('processWarmTransferAsync', () => {
    it('should process warm transfer and update existing lead', async () => {
      const callSid = 'CA123456789';
      const messages = [
        { role: 'user', content: 'My name is Jane Doe, phone +**********' },
        { role: 'assistant', content: 'Tell me about your case' },
      ];

      // Setup global message history
      global.messageHistory = [
        {
          callSid,
          messages,
          processed: false,
        },
      ];

      // Mock AI response
      const mockAIResponse = {
        choices: [
          {
            message: {
              content: JSON.stringify({
                customerName: 'Jane Doe',
                customerPhone: '+**********',
                customerEmail: '<EMAIL>',
                qualificationInfo: 'Qualified case',
                clientQualifiedCampaign: 'PFAS',
                clientDiagnosis: 'Kidney Cancer',
                clientDiagnosisDate: '05/10/2021',
                clientPersonallyPrepareHandleProduct: 'Yes',
                clientUnderstandFirmWillVerifyDiagnosis: 'Yes',
                clientEvidenceTypeForYourInjury: 'Medical Records',
                client1stExposed: '01/01/2015',
                clientLastExposed: '12/31/2020',
              }),
            },
          },
        ],
      };
      mockOpenAI.chat.completions.create.mockResolvedValue(mockAIResponse);

      // Mock existing lead found
      const existingLead = { Id: '00QUi00000RVEYdMAP', Name: 'Jane Doe' };
      mockSalesforceService.findLeadByPhoneNumber.mockResolvedValue(existingLead);
      mockSalesforceService.updateRecord.mockResolvedValue({ success: true });

      await salesforceProcessor.processWarmTransferAsync(callSid);

      expect(mockSalesforceService.connect).toHaveBeenCalled();
      expect(mockSalesforceService.findLeadByPhoneNumber).toHaveBeenCalledWith('+**********');
      expect(mockSalesforceService.updateRecord).toHaveBeenCalledWith(
        'Lead',
        expect.objectContaining({
          First_Name__c: 'Jane',
          Last_Name__c: 'Doe',
          Phone: '+**********',
          Campaign_Type__c: 'PFAS',
          Diagnosis__c: 'Kidney Cancer',
        }),
        '00QUi00000RVEYdMAP'
      );
      expect(mockSalesforceService.disconnect).toHaveBeenCalled();
      expect(global.messageHistory[0].processed).toBe(true);
    });

    it('should process warm transfer and create new lead when none exists', async () => {
      const callSid = 'CA987654321';
      const messages = [{ role: 'user', content: 'I am Bob Wilson' }];

      global.messageHistory = [{ callSid, messages, processed: false }];

      const mockAIResponse = {
        choices: [
          {
            message: {
              content: JSON.stringify({
                customerName: 'Bob Wilson',
                customerPhone: '+1777888999',
                customerEmail: 'Not provided',
                qualificationInfo: 'New case',
                clientQualifiedCampaign: 'MVA',
                clientDiagnosis: 'Not provided',
                clientDiagnosisDate: '01/01/1900',
                clientPersonallyPrepareHandleProduct: 'No',
                clientUnderstandFirmWillVerifyDiagnosis: 'No',
                clientEvidenceTypeForYourInjury: 'Not Applicable',
                client1stExposed: '01/01/1900',
                clientLastExposed: '01/01/1900',
              }),
            },
          },
        ],
      };
      mockOpenAI.chat.completions.create.mockResolvedValue(mockAIResponse);

      // Mock no existing lead found
      mockSalesforceService.findLeadByPhoneNumber.mockResolvedValue(null);
      mockSalesforceService.createRecord.mockResolvedValue({
        id: '00QUi00000NEW123',
        success: true,
      });

      await salesforceProcessor.processWarmTransferAsync(callSid);

      expect(mockSalesforceService.findLeadByPhoneNumber).toHaveBeenCalledWith('+1777888999');
      expect(mockSalesforceService.createRecord).toHaveBeenCalledWith(
        'Lead',
        expect.objectContaining({
          First_Name__c: 'Bob',
          Last_Name__c: 'Wilson',
          Phone: '+1777888999',
          Campaign_Type__c: 'MVA',
        })
      );
      expect(global.messageHistory[0].processed).toBe(true);
    });

    it('should handle case when no conversation history found', async () => {
      const callSid = 'CA_NONEXISTENT';
      global.messageHistory = [];

      // Should not throw error, just return early
      await expect(salesforceProcessor.processWarmTransferAsync(callSid)).resolves.toBeUndefined();

      expect(mockSalesforceService.connect).not.toHaveBeenCalled();
      expect(mockOpenAI.chat.completions.create).not.toHaveBeenCalled();
    });

    it('should handle Salesforce processing errors gracefully', async () => {
      const callSid = 'CA123456789';
      global.messageHistory = [
        {
          callSid,
          messages: [{ role: 'user', content: 'Test' }],
          processed: false,
        },
      ];

      const mockAIResponse = {
        choices: [
          {
            message: {
              content: JSON.stringify({
                customerName: 'Test User',
                customerPhone: '+**********',
                customerEmail: '<EMAIL>',
                qualificationInfo: 'Test case',
              }),
            },
          },
        ],
      };
      mockOpenAI.chat.completions.create.mockResolvedValue(mockAIResponse);
      mockSalesforceService.findLeadByPhoneNumber.mockRejectedValue(
        new Error('Salesforce connection failed')
      );

      await expect(salesforceProcessor.processWarmTransferAsync(callSid)).rejects.toThrow(
        'Salesforce connection failed'
      );
    });
  });

  describe('cleanupMessageHistory', () => {
    it('should remove old conversation histories', () => {
      const now = Date.now();
      const oldTimestamp = now - 7200000; // 2 hours ago
      const recentTimestamp = now - 1800000; // 30 minutes ago

      global.messageHistory = [
        { callSid: 'CA_OLD', messages: [], timestamp: oldTimestamp, processed: true },
        { callSid: 'CA_RECENT', messages: [], timestamp: recentTimestamp, processed: false },
      ];

      salesforceProcessor.cleanupMessageHistory(3600000); // 1 hour max age

      expect(global.messageHistory).toHaveLength(1);
      expect(global.messageHistory[0].callSid).toBe('CA_RECENT');
    });

    it('should not remove recent histories', () => {
      const now = Date.now();
      global.messageHistory = [
        { callSid: 'CA_RECENT1', messages: [], timestamp: now - 1800000 }, // 30 minutes ago
        { callSid: 'CA_RECENT2', messages: [], timestamp: now - 900000 }, // 15 minutes ago
      ];

      salesforceProcessor.cleanupMessageHistory(3600000); // 1 hour max age

      expect(global.messageHistory).toHaveLength(2);
    });
  });
});
