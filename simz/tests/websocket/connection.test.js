const WebSocket = require('ws');

describe('WebSocket Connection', () => {
  let ws;

  afterEach(() => {
    if (ws) {
      ws.close();
    }
  });

  test('should connect to WebSocket server successfully', (done) => {
    ws = new WebSocket('wss://api.simz.com/');

    ws.on('open', () => {
      expect(ws.readyState).toBe(WebSocket.OPEN);
      done();
    });

    ws.on('error', (err) => {
      console.error('WebSocket error:', err);
      done(err);
    });
  });

  test('should receive messages from server', (done) => {
    ws = new WebSocket('wss://api.simz.com/');

    ws.on('open', () => {
      expect(ws.readyState).toBe(WebSocket.OPEN);
    });

    ws.on('message', (data) => {
      expect(data).toBeDefined();
      console.log('Received:', data.toString());
      done();
    });

    ws.on('error', (err) => {
      console.error('WebSocket error:', err);
      done(err);
    });
  });

  test('should handle connection errors gracefully', (done) => {
    ws = new WebSocket('wss://invalid-url.simz.com/');

    ws.on('error', (err) => {
      expect(err).toBeDefined();
      console.error('Expected error:', err);
      done();
    });
  });
});
