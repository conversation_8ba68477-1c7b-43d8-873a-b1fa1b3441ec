// Jest test setup file

// Set global variables
global.messageHistory = [];
global.callData = {};

// Set environment variables
process.env.NODE_ENV = 'test';
process.env.SALESFORCE_USERNAME = '<EMAIL>';
process.env.SALESFORCE_PASSWORD = 'testpassword';
process.env.SALESFORCE_SECURITY_TOKEN = 'testtoken';
process.env.OPENAI_API_KEY = 'test-openai-key';

// Mock console methods to reduce test output noise
global.console = {
  ...console,
  // Keep important log methods but can be selectively muted
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Setup before each test
beforeEach(() => {
  // Reset global state
  global.messageHistory = [];
  global.callData = {};

  // Clear all mocks
  jest.clearAllMocks();
});

// Cleanup after each test
afterEach(() => {
  // Clear any asynchronous operations
  jest.clearAllTimers();
});
