# Test Documentation

This document explains how to run unit tests and integration tests for the project.

## Test Structure

```
tests/
├── unit/                           # Unit tests
│   ├── SalesforceProcessorService.test.js
│   └── SalesforceService.test.js
├── integration/                    # Integration tests
│   └── warmTransfer.integration.test.js
├── setup.js                       # Jest test setup
└── README.md                      # This document
```

## Running Tests

### Install Dependencies

```bash
npm install
```

### Run All Tests

```bash
npm test
```

### Run Unit Tests

```bash
npm run test:unit
```

### Run Integration Tests

```bash
npm run test:integration
```

### Run Tests with Coverage Report

```bash
npm run test:coverage
```

### Run Tests in Watch Mode (for development)

```bash
npm run test:watch
```

## Test Coverage

### SalesforceProcessorService Unit Tests

- ✅ Store conversation history
- ✅ Process conversations and extract customer information
- ✅ Asynchronous warm transfer processing
- ✅ Clean up old message history
- ✅ Error handling

### SalesforceService Unit Tests

- ✅ Salesforce connection and disconnection
- ✅ Find leads by phone number
- ✅ Create new lead records
- ✅ Update existing lead records
- ✅ Error handling and edge cases

### Warm Transfer Integration Tests

- ✅ End-to-end new lead creation workflow
- ✅ End-to-end existing lead update workflow
- ✅ Parallel conversation processing
- ✅ Error handling and recovery
- ✅ Message history management

## Test Configuration

Tests use Jest framework with configuration in `jest.config.js`:

- **Test Environment**: Node.js
- **Coverage Threshold**: 75%
- **Timeout**: 10 seconds
- **Mocks**: Automatic cleanup of all mocks

## Mocked Dependencies

The following external dependencies are mocked in tests:

- `jsforce` - Salesforce connection
- `openai` - OpenAI API
- Environment variables and global state

## Environment Variables

Tests require the following environment variables (automatically set in `tests/setup.js`):

- `SALESFORCE_USERNAME`
- `SALESFORCE_PASSWORD`
- `SALESFORCE_SECURITY_TOKEN`
- `OPENAI_API_KEY`

## Continuous Integration

These tests can be run in CI/CD pipelines:

```bash
# Run tests in CI environment
npm test -- --ci --coverage --watchAll=false
```

## Debugging Tests

To debug failed tests:

1. Run specific test file:

```bash
npx jest tests/unit/SalesforceProcessorService.test.js
```

2. Run specific test case:

```bash
npx jest -t "should store conversation history correctly"
```

3. Verbose output:

```bash
npx jest --verbose
```

## Adding New Tests

When creating new tests:

1. Place unit tests in `tests/unit/` directory
2. Place integration tests in `tests/integration/` directory
3. Use descriptive test names
4. Include positive and negative test cases
5. Test edge conditions and error handling

## Coverage Reports

After running `npm run test:coverage`, coverage reports will be generated in the `coverage/` directory:

- `coverage/lcov-report/index.html` - HTML report
- `coverage/lcov.info` - LCOV format report
- Console output summary
