const sgMail = require('@sendgrid/mail');
const env = require('../../config/env');

describe('Email Messaging', () => {
  beforeAll(() => {
    sgMail.setApiKey(env.sendgrid.apiKey);
  });

  test('should send email with dynamic template successfully', async () => {
    const msg = {
      to: '<EMAIL>',
      from: '<EMAIL>',
      templateId: 'd-95922878391240468d8f4371a71cb145',
      dynamicTemplateData: {
        name: 'Tao',
        reservationDate: '2024-03-15',
        reservationTime: '19:00',
        numberOfGuests: 2,
        restaurantName: 'SimZ Steak House',
        restaurantAddress: '123 Main St, San Francisco, CA 94105',
        restaurantPhone: '+****************',
        roomType: 'Deluxe Suite',
        checkInDate: '2024-03-15',
        checkOutDate: '2024-03-17',
        numberOfNights: 2,
      },
    };

    const response = await sgMail.send(msg);
    expect(response).toBeDefined();
    console.log('Email sent successfully!');
    console.log('Response:', response);
  });

  test('should handle email sending errors gracefully', async () => {
    const invalidMsg = {
      to: 'invalid-email',
      from: '<EMAIL>',
      templateId: 'invalid-template-id',
    };

    try {
      await sgMail.send(invalidMsg);
    } catch (error) {
      expect(error).toBeDefined();
      if (error.response) {
        expect(error.response.body).toBeDefined();
        console.error('Error response body:', error.response.body);
      }
    }
  });
});
