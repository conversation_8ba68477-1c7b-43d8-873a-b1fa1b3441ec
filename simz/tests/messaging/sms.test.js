const twilio = require('twilio');
const env = require('../../config/env');

describe('SMS Messaging', () => {
  let client;

  beforeAll(() => {
    client = twilio(env.twilio.accountSid, env.twilio.authToken);
  });

  async function checkMessageStatus(messageSid) {
    try {
      const message = await client.messages(messageSid).fetch();
      console.log('Current message status:', message.status);
      return message.status;
    } catch (error) {
      console.error('Error checking message status:', error);
      return null;
    }
  }

  test('should send SMS successfully', async () => {
    const message = await client.messages.create({
      body: 'This is a test message from Sim Zee Steak House!',
      from: env.twilio.phoneNumber,
      to: '+***********',
    });

    expect(message.sid).toBeDefined();
    expect(message.status).toBeDefined();
    console.log('Message SID:', message.sid);
    console.log('Initial status:', message.status);

    // Wait and check status
    await new Promise((resolve) => setTimeout(resolve, 2000));
    const status1 = await checkMessageStatus(message.sid);
    expect(status1).toBeDefined();

    await new Promise((resolve) => setTimeout(resolve, 5000));
    const status2 = await checkMessageStatus(message.sid);
    expect(status2).toBeDefined();
  });
});
