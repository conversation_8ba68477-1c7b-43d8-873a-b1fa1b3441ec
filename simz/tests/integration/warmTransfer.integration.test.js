const {
  SalesforceProcessorService,
} = require('../../llm-integration/services/SalesforceProcessorService');
const { SalesforceService } = require('../../salesforce/services/SalesforceService');

// Mock external dependencies but keep the integration flow
jest.mock('../../salesforce/services/SalesforceService');
jest.mock('openai');

describe('Warm Transfer Integration Tests', () => {
  let salesforceProcessor;
  let mockSalesforceService;
  let mockOpenAI;

  beforeEach(() => {
    // Reset global state
    global.messageHistory = [];
    global.callData = {};

    // Setup mocks
    mockSalesforceService = {
      connect: jest.fn(),
      disconnect: jest.fn(),
      findLeadByPhoneNumber: jest.fn(),
      createRecord: jest.fn(),
      updateRecord: jest.fn(),
    };

    mockOpenAI = {
      chat: {
        completions: {
          create: jest.fn(),
        },
      },
    };

    require('openai').mockImplementation(() => mockOpenAI);
    require('../../salesforce/services/SalesforceService').SalesforceService.mockImplementation(
      () => mockSalesforceService
    );

    salesforceProcessor = new SalesforceProcessorService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Complete Warm Transfer Workflow', () => {
    it('should handle end-to-end warm transfer with new lead creation', async () => {
      const callSid = 'CA123456789';
      const conversationMessages = [
        { role: 'assistant', content: 'Hello, how can I help you today?' },
        {
          role: 'user',
          content: 'Hi, my name is Sarah Johnson and my phone number is +**********',
        },
        { role: 'assistant', content: 'Hi Sarah, tell me about your legal case.' },
        {
          role: 'user',
          content: 'I was diagnosed with kidney cancer after using PFAS-contaminated water',
        },
        { role: 'assistant', content: 'I understand. When were you first exposed to PFAS?' },
        {
          role: 'user',
          content: 'I lived near the military base from 2010 to 2020, so about 10 years',
        },
        { role: 'assistant', content: 'And when were you diagnosed with kidney cancer?' },
        { role: 'user', content: 'In March 2021. I have all my medical records.' },
        {
          role: 'assistant',
          content: 'Thank you for the information. Let me connect you with a specialist.',
        },
      ];

      // Step 1: Store conversation history (simulating WebSocketController behavior)
      salesforceProcessor.storeConversationHistory(conversationMessages, callSid);

      // Verify conversation is stored
      expect(global.messageHistory).toHaveLength(1);
      expect(global.messageHistory[0].callSid).toBe(callSid);
      expect(global.messageHistory[0].processed).toBe(false);

      // Step 2: Mock AI response for conversation analysis
      const aiAnalysisResponse = {
        choices: [
          {
            message: {
              content: JSON.stringify({
                customerName: 'Sarah Johnson',
                customerPhone: '+**********',
                customerEmail: 'Not provided',
                qualificationInfo:
                  'PFAS exposure case - kidney cancer diagnosis, 10 years exposure near military base',
                clientQualifiedCampaign: 'PFAS',
                clientDiagnosis: 'Kidney Cancer',
                clientDiagnosisDate: '03/01/2021',
                clientPersonallyPrepareHandleProduct: 'Yes',
                clientUnderstandFirmWillVerifyDiagnosis: 'Yes',
                clientEvidenceTypeForYourInjury: 'Medical Records',
                client1stExposed: '01/01/2010',
                clientLastExposed: '12/31/2020',
              }),
            },
          },
        ],
      };
      mockOpenAI.chat.completions.create.mockResolvedValue(aiAnalysisResponse);

      // Step 3: Mock Salesforce interactions - no existing lead found
      mockSalesforceService.findLeadByPhoneNumber.mockResolvedValue(null);
      mockSalesforceService.createRecord.mockResolvedValue({
        id: '00QUi00000NEW789',
        success: true,
      });

      // Step 4: Process the warm transfer
      await salesforceProcessor.processWarmTransferAsync(callSid);

      // Step 5: Verify the complete workflow

      // Verify AI was called for conversation analysis
      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledWith({
        model: 'gpt-4',
        messages: expect.arrayContaining([
          {
            role: 'system',
            content: expect.stringContaining('You are an AI assistant specialized'),
          },
          { role: 'user', content: expect.stringContaining('Analyze this conversation') },
        ]),
        temperature: 0.1,
        max_tokens: 2000,
      });

      // Verify Salesforce connection was established
      expect(mockSalesforceService.connect).toHaveBeenCalled();

      // Verify lead lookup by phone number
      expect(mockSalesforceService.findLeadByPhoneNumber).toHaveBeenCalledWith('+**********');

      // Verify new lead was created with correct data structure matching actual implementation
      expect(mockSalesforceService.createRecord).toHaveBeenCalledWith(
        'Lead',
        expect.objectContaining({
          First_Name__c: 'Sarah',
          Last_Name__c: 'Johnson',
          LastName: 'Johnson',
          Phone: '+**********',
          Campaign_Type__c: 'PFAS',
          Diagnosis__c: 'Kidney Cancer',
          Date_Diagnosed_Injury_Date__c: '03/01/2021',
          Personally_prepare_handle_product__c: 'Yes',
          Understand_firm_will_verify_claim_dx__c: 'Yes',
          Evidence_type_for_your_injury__c: 'Medical Records',
          When_1st_exposed_used__c: '01/01/2010',
          When_last_exposed_used__c: '12/31/2020',
          SimZ_Notes__c:
            'PFAS exposure case - kidney cancer diagnosis, 10 years exposure near military base',
          Status: 'Open - Not Contacted',
          LeadSource: 'SimZ AI',
        })
      );

      // Verify Salesforce connection was closed
      expect(mockSalesforceService.disconnect).toHaveBeenCalled();

      // Verify conversation is marked as processed
      expect(global.messageHistory[0].processed).toBe(true);
    });

    it('should handle end-to-end warm transfer with existing lead update', async () => {
      const callSid = 'CA987654321';
      const conversationMessages = [
        { role: 'assistant', content: 'Welcome back, how can I help you?' },
        {
          role: 'user',
          content: 'This is Michael Brown, +**********. I have updates on my Roundup case',
        },
        { role: 'assistant', content: 'Hi Michael, what updates do you have?' },
        {
          role: 'user',
          content:
            'I was recently diagnosed with non-Hodgkin lymphoma in addition to my other issues',
        },
        { role: 'assistant', content: 'I see. When did you receive this new diagnosis?' },
        { role: 'user', content: 'Last week, on June 15th, 2024. My oncologist confirmed it.' },
        {
          role: 'assistant',
          content:
            'Thank you for the update. Let me transfer you to a specialist to discuss this new information.',
        },
      ];

      // Store conversation
      salesforceProcessor.storeConversationHistory(conversationMessages, callSid);

      // Mock AI response
      const aiAnalysisResponse = {
        choices: [
          {
            message: {
              content: JSON.stringify({
                customerName: 'Michael Brown',
                customerPhone: '+**********',
                customerEmail: 'Not provided',
                qualificationInfo: 'Roundup case update - new lymphoma diagnosis',
                clientQualifiedCampaign: 'Roundup',
                clientDiagnosis: "Non-Hodgkin's Lymphoma",
                clientDiagnosisDate: '06/15/2024',
                clientPersonallyPrepareHandleProduct: 'Yes',
                clientUnderstandFirmWillVerifyDiagnosis: 'Yes',
                clientEvidenceTypeForYourInjury: 'Medical Records',
                client1stExposed: '01/01/2015',
                clientLastExposed: '12/31/2020',
              }),
            },
          },
        ],
      };
      mockOpenAI.chat.completions.create.mockResolvedValue(aiAnalysisResponse);

      // Mock existing lead found
      const existingLead = {
        Id: '00QUi00000EXIST123',
        FirstName: 'Michael',
        LastName: 'Brown',
        Phone: '+**********',
        Email: '<EMAIL>',
      };
      mockSalesforceService.findLeadByPhoneNumber.mockResolvedValue(existingLead);
      mockSalesforceService.updateRecord.mockResolvedValue({
        id: existingLead.Id,
        success: true,
      });

      // Process warm transfer
      await salesforceProcessor.processWarmTransferAsync(callSid);

      // Verify workflow
      expect(mockSalesforceService.connect).toHaveBeenCalled();
      expect(mockSalesforceService.findLeadByPhoneNumber).toHaveBeenCalledWith('+**********');

      // Verify existing lead was updated (not created)
      expect(mockSalesforceService.createRecord).not.toHaveBeenCalled();
      expect(mockSalesforceService.updateRecord).toHaveBeenCalledWith(
        'Lead',
        expect.objectContaining({
          First_Name__c: 'Michael',
          Last_Name__c: 'Brown',
          LastName: 'Brown',
          Phone: '+**********',
          Campaign_Type__c: 'Roundup',
          Diagnosis__c: "Non-Hodgkin's Lymphoma",
          Date_Diagnosed_Injury_Date__c: '06/15/2024',
          Personally_prepare_handle_product__c: 'Yes',
          Understand_firm_will_verify_claim_dx__c: 'Yes',
          Evidence_type_for_your_injury__c: 'Medical Records',
          When_1st_exposed_used__c: '01/01/2015',
          When_last_exposed_used__c: '12/31/2020',
          SimZ_Notes__c: 'Roundup case update - new lymphoma diagnosis',
        }),
        existingLead.Id
      );

      expect(mockSalesforceService.disconnect).toHaveBeenCalled();
      expect(global.messageHistory[0].processed).toBe(true);
    });

    it('should handle multiple conversations and process them separately', async () => {
      const callSid1 = 'CA111111111';
      const callSid2 = 'CA222222222';

      // Store two different conversations
      salesforceProcessor.storeConversationHistory(
        [{ role: 'user', content: 'First caller - Alice Smith +**********' }],
        callSid1
      );

      salesforceProcessor.storeConversationHistory(
        [{ role: 'user', content: 'Second caller - Bob Jones +**********' }],
        callSid2
      );

      expect(global.messageHistory).toHaveLength(2);

      // Mock AI responses for both
      mockOpenAI.chat.completions.create
        .mockResolvedValueOnce({
          choices: [
            {
              message: {
                content: JSON.stringify({
                  customerName: 'Alice Smith',
                  customerPhone: '+**********',
                  customerEmail: 'Not provided',
                  qualificationInfo: 'First case',
                  clientQualifiedCampaign: 'MVA',
                  clientDiagnosis: 'Not provided',
                  clientDiagnosisDate: '01/01/1900',
                  clientPersonallyPrepareHandleProduct: 'No',
                  clientUnderstandFirmWillVerifyDiagnosis: 'No',
                  clientEvidenceTypeForYourInjury: 'Not Applicable',
                  client1stExposed: '01/01/1900',
                  clientLastExposed: '01/01/1900',
                }),
              },
            },
          ],
        })
        .mockResolvedValueOnce({
          choices: [
            {
              message: {
                content: JSON.stringify({
                  customerName: 'Bob Jones',
                  customerPhone: '+**********',
                  customerEmail: 'Not provided',
                  qualificationInfo: 'Second case',
                  clientQualifiedCampaign: 'PFAS',
                  clientDiagnosis: 'Not provided',
                  clientDiagnosisDate: '01/01/1900',
                  clientPersonallyPrepareHandleProduct: 'No',
                  clientUnderstandFirmWillVerifyDiagnosis: 'No',
                  clientEvidenceTypeForYourInjury: 'Not Applicable',
                  client1stExposed: '01/01/1900',
                  clientLastExposed: '01/01/1900',
                }),
              },
            },
          ],
        });

      // Mock no existing leads
      mockSalesforceService.findLeadByPhoneNumber.mockResolvedValue(null);
      mockSalesforceService.createRecord.mockResolvedValue({ id: 'NEW_LEAD', success: true });

      // Process both calls
      await salesforceProcessor.processWarmTransferAsync(callSid1);
      await salesforceProcessor.processWarmTransferAsync(callSid2);

      // Verify both were processed independently
      expect(mockSalesforceService.findLeadByPhoneNumber).toHaveBeenCalledTimes(2);
      expect(mockSalesforceService.findLeadByPhoneNumber).toHaveBeenCalledWith('+**********');
      expect(mockSalesforceService.findLeadByPhoneNumber).toHaveBeenCalledWith('+**********');

      expect(mockSalesforceService.createRecord).toHaveBeenCalledTimes(2);

      // Verify both conversations are marked as processed
      expect(global.messageHistory[0].processed).toBe(true);
      expect(global.messageHistory[1].processed).toBe(true);
    });

    it('should handle errors gracefully without breaking the workflow', async () => {
      const callSid = 'CA_ERROR_TEST';

      salesforceProcessor.storeConversationHistory(
        [{ role: 'user', content: 'Test error handling' }],
        callSid
      );

      // Mock AI processing to succeed
      mockOpenAI.chat.completions.create.mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify({
                customerName: 'Error Test',
                customerPhone: '+1999999999',
                customerEmail: 'Not provided',
                qualificationInfo: 'Test case',
                clientQualifiedCampaign: 'Not provided',
                clientDiagnosis: 'Not provided',
                clientDiagnosisDate: '01/01/1900',
                clientPersonallyPrepareHandleProduct: 'No',
                clientUnderstandFirmWillVerifyDiagnosis: 'No',
                clientEvidenceTypeForYourInjury: 'Not Applicable',
                client1stExposed: '01/01/1900',
                clientLastExposed: '01/01/1900',
              }),
            },
          },
        ],
      });

      // Mock Salesforce to fail
      mockSalesforceService.connect.mockResolvedValue();
      mockSalesforceService.findLeadByPhoneNumber.mockRejectedValue(
        new Error('Salesforce query failed')
      );

      // Should throw error but not crash
      await expect(salesforceProcessor.processWarmTransferAsync(callSid)).rejects.toThrow(
        'Salesforce query failed'
      );

      // Verify attempt was made
      expect(mockSalesforceService.connect).toHaveBeenCalled();
      expect(mockSalesforceService.findLeadByPhoneNumber).toHaveBeenCalled();
    });
  });

  describe('Message History Management', () => {
    it('should cleanup old conversation histories', () => {
      const now = Date.now();

      // Add old and new conversations
      global.messageHistory = [
        {
          callSid: 'CA_OLD_1',
          messages: [],
          timestamp: now - 3600000 * 3, // 3 hours old
          processed: true,
        },
        {
          callSid: 'CA_OLD_2',
          messages: [],
          timestamp: now - 3600000 * 2, // 2 hours old
          processed: true,
        },
        {
          callSid: 'CA_RECENT',
          messages: [],
          timestamp: now - 1800000, // 30 minutes old
          processed: false,
        },
      ];

      // Cleanup conversations older than 1 hour
      salesforceProcessor.cleanupMessageHistory(3600000);

      // Only recent conversation should remain
      expect(global.messageHistory).toHaveLength(1);
      expect(global.messageHistory[0].callSid).toBe('CA_RECENT');
    });
  });
});
