const { scheduleJob } = require('node-schedule');
const fs = require('fs');
const axios = require('axios');
const twilio = require('twilio');
require('dotenv').config();

const SCHEDULED_CALLS_FILE = './utils/scheduled_calls.json';

let scheduledCalls = [];
try {
  scheduledCalls = JSON.parse(fs.readFileSync(SCHEDULED_CALLS_FILE, 'utf8'));
} catch (error) {
  console.error('Error loading scheduled calls:', error);
}

const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
const twilioPhoneNumber = process.env.TWILIO_PHONE_NUMBER;
const twilioClient = twilio(accountSid, authToken);

function saveScheduledCalls() {
  try {
    fs.writeFileSync(SCHEDULED_CALLS_FILE, JSON.stringify(scheduledCalls, null, 2), 'utf8');
  } catch (error) {
    console.error('Error saving scheduled calls:', error);
  }
}

async function scheduleCall(phoneNumber, callTimeinMin) {
  try {
    const callTime = new Date(Date.now() + callTimeinMin * 60000);
    const callJob = scheduleJob(callTime, () => {
      makePhoneCall(phoneNumber);
    });

    const reminderTime = new Date(callTime.getTime() - 5 * 60000); // 5 minutes before
    const reminderJob = scheduleJob(reminderTime, () => {
      sendTextReminder(phoneNumber);
    });

    // Store only the necessary information
    scheduledCalls.push({
      phoneNumber,
      callTime: callTime.toString(),
      reminderTime: reminderTime.toString(),
      triggered: false,
    });
    saveScheduledCalls();
  } catch (error) {
    console.error('Error scheduling call:', error);
  }
}

async function makePhoneCall(phoneNumber) {
  try {
    // Update the shared state directly in index.js
    const threadId = 'thread_u5yHTUNi3agt9qSK2pxLK4LG'; // Define the threadId

    // Send the threadId and phone number to the /outbound-call endpoint
    const response = await axios.post('http://localhost:30000/callback-call', {
      originalThreadId: threadId,
      phoneNumber: phoneNumber, // Include phoneNumber in the request body
    });
    // const call = await twilioClient.calls.create({
    //   url: "https://neatly-busy-snake.ngrok-free.app/makecallback",
    //   to: phoneNumber,
    //   from: twilioPhoneNumber,
    // });
    // console.log(`Call placed to ${phoneNumber}: ${call.sid}`);
    // scheduledCalls = scheduledCalls.filter(callObj => callObj.phoneNumber !== phoneNumber);
    scheduledCalls.forEach((callObj) => {
      if (callObj.phoneNumber === phoneNumber) {
        callObj.triggered = true;
      }
    });
    saveScheduledCalls();
  } catch (error) {
    console.error('Error making Twilio call:', error);
  }
}

async function sendTextReminder(phoneNumber) {
  try {
    const message = await twilioClient.messages.create({
      body: 'test reminder message',
      from: twilioPhoneNumber,
      to: phoneNumber,
    });
    console.log(`Text reminder sent to ${phoneNumber}: ${message.sid}`);
  } catch (error) {
    console.error('Error sending text reminder:', error);
  }
}

module.exports = {
  scheduleCall,
};
