// handleFunctionCalling.js  Use Azure OpenAI
const { AzureOpenAI } = require('openai');
require('dotenv').config();
const axios = require('axios');
const https = require('https');
// Generate ICS
const ics = require('ics');
const twilio = require('twilio');
// Import components:
const { scheduleCall } = require('./Scheduler.js');
// Send out email
const nodemailer = require('nodemailer');
// Connect to Salesforce
var jsforce = require('jsforce');
var conn = new jsforce.Connection({
  loginUrl: 'https://thelegalleads.my.salesforce.com',
});

// include the google api key
const GOOGLE_PLACES_API_KEY = process.env.GOOGLE_PLACES_API_KEY;

// include the Azure openai
const azureOpenAIKey = process.env.AZURE_OPENAI_KEY;
const azureOpenAIVersion = '2024-05-01-preview';
const azoaiendpoint = 'https://tll-west.openai.azure.com/';
// const azoaiendpoint = 'https://legal-liaison.openai.azure.com/';
const getClient = () => {
  const assistantsClient = new AzureOpenAI({
    endpoint: azoaiendpoint,
    apiVersion: azureOpenAIVersion,
    apiKey: azureOpenAIKey,
  });
  return assistantsClient;
};
const assistantsClient = getClient();
let openai = assistantsClient; // <-- ä½¿ç”¨ Azure OpenAI
const EventEmitter = require('events');

// const assitantID = "asst_DexwsrZFxrWpeMXi7EqWF2KJ"; // Test_Assistant_Tao
// const assitantID = "asst_6HGV7BcDGFYKXIN53JjFg7Y2"; // Test_Assistant_AP
const assitantID = 'asst_en08dtQQwPgJih98CpLLuVQw'; // Test_Assistant_AP_V2

// List of cancer products with their associated cancers, start date, and end date
const cancerProducts = [
  {
    name: 'Talcum Baby Powder',
    cancers: [
      'Ovarian Cancer',
      'Primary Peritoneal Cancer',
      'Fallopian Tube Cancer',
      'Cancer in Ovaries',
      'Ovarian',
    ],
    startDate: '2020-01',
    endDate: '2025-07',
  },
  {
    name: "Hair Relaxers from L'Oreal",
    cancers: [
      'Uterine Cancer',
      'Endometrial Cancer',
      'Ovarian Cancer',
      'Cancer in Ovaries',
      'Ovarian',
      'Endometrial',
    ],
    startDate: '2010-01',
    endDate: '2025-07',
  },
  {
    name: 'Aqueous Film Forming Foam (A Triple F)',
    cancers: [
      'Kidney Cancer',
      'Thyroid Cancer',
      'Testicular Cancer',
      'Ulcerative Colitis',
      'Liver Cancer',
      'Liver',
      'Kidney',
    ],
    startDate: '1960-01',
    endDate: '2025-07',
  },
  {
    name: 'Roundup Weed Killer',
    cancers: [
      "Non-Hodgkin's Lymphoma",
      'Leukemia',
      'CLL',
      'Chronic Lymphocytic Leukemia',
      'Lymphoma',
      'Lymphoma Cancer',
    ],
    startDate: '2004-01',
    endDate: '2025-07',
  },
  {
    name: 'Contaminated Water at Camp Lejeune',
    cancers: [
      'Kidney Cancer',
      'Liver Cancer',
      "Non-Hodgkin's Lymphoma",
      'Leukemia',
      'Bladder Cancer',
      'Multiple Myeloma',
      "Parkinson's Disease",
      'Kidney/Renal Disease',
      'Kidney/Renal Failure',
      'Systemic Sclerosis/Systemic Scleroderma',
      'Lymphoma',
      'Kidney',
      'Lymphoma Cancer',
    ],
    startDate: '1953-08',
    endDate: '2025-07',
  },
];

// List of medical qualifications for different products
const medicalProducts = [
  {
    name: 'Paraquat',
    productUsed: ['Paraquat'],
    complications: [
      "Parkinson's Disease",
      'Tremors',
      'Rigidity',
      'Balance Impairment',
      'Bradykinesia',
      'Difficulty speaking',
      'Reduced facial expression',
      'Drooling',
      'Paralysis',
      'Small Handwriting',
      'Decreased ROM',
      'Sleep Disorders',
      'Spasms',
      'Difficulty Swallowing',
    ],
    startDate: '1964-01',
    endDate: '2025-07',
  },
  {
    name: 'Semaglutide',
    productUsed: ['Ozempic', 'Wegovy'],
    complications: [
      'Persistent vomiting for 4+ weeks',
      'Gastric Injury',
      'Ileus',
      'Gastroparesis',
      'Pulmonary Aspiration',
      'Deep Vein Thrombosis',
    ],
    startDate: '2018-01',
    endDate: '2025-07',
  },
  {
    name: 'Catheter Port',
    productUsed: ['Catheter'],
    complications: [
      'Blood clots',
      'Cardiac arrhythmia',
      'Cardiac punctures',
      'Cardiac/pericardial tamponade',
      'Death',
      'Migration due to breakage',
      'Hemorrhage',
      'Leakage at port site',
      'Laceration to blood vessels',
      'Necrosis',
      'Pulmonary embolism',
      'Severe pain around port/catheter',
    ],
    startDate: '2010-01',
    endDate: '2025-07',
  },
  {
    name: 'Hernia Mesh',
    productUsed: ['Hernia Mesh'],
    complications: [
      'With a lot of pain',
      'lot of pain',
      'Abdominal pain',
      'Adhesion',
      'Pain and movement out of place',
      'Bowel perforation',
      'Edge of mesh curled',
      'Infection',
      'Hernia recurrence',
      'Intestinal blockage',
      'Mesh migration',
      'Mesh contraction',
      'Intestinal fistula',
      'Surgery to remove mesh',
      'Death',
      'Open wound',
      'Mesh ring broken',
      'Mesh balled up',
    ],
    startDate: '2010-01',
    endDate: '2025-09',
  },
];

const stateToTimeZone = {
  Alabama: 'America/Chicago',
  Alaska: 'America/Anchorage',
  Arizona: 'America/Phoenix',
  Arkansas: 'America/Chicago',
  California: 'America/Los_Angeles',
  Colorado: 'America/Denver',
  Connecticut: 'America/New_York',
  Delaware: 'America/New_York',
  Florida: 'America/New_York',
  Georgia: 'America/New_York',
  Hawaii: 'Pacific/Honolulu',
  Idaho: 'America/Boise',
  Illinois: 'America/Chicago',
  Indiana: 'America/Indiana/Indianapolis',
  Iowa: 'America/Chicago',
  Kansas: 'America/Chicago',
  Kentucky: 'America/New_York',
  Louisiana: 'America/Chicago',
  Maine: 'America/New_York',
  Maryland: 'America/New_York',
  Massachusetts: 'America/New_York',
  Michigan: 'America/Detroit',
  Minnesota: 'America/Chicago',
  Mississippi: 'America/Chicago',
  Missouri: 'America/Chicago',
  Montana: 'America/Denver',
  Nebraska: 'America/Chicago',
  Nevada: 'America/Los_Angeles',
  'New Hampshire': 'America/New_York',
  'New Jersey': 'America/New_York',
  'New Mexico': 'America/Denver',
  'New York': 'America/New_York',
  'North Carolina': 'America/New_York',
  'North Dakota': 'America/Chicago',
  Ohio: 'America/New_York',
  Oklahoma: 'America/Chicago',
  Oregon: 'America/Los_Angeles',
  Pennsylvania: 'America/New_York',
  'Rhode Island': 'America/New_York',
  'South Carolina': 'America/New_York',
  'South Dakota': 'America/Chicago',
  Tennessee: 'America/Chicago',
  Texas: 'America/Chicago',
  Utah: 'America/Denver',
  Vermont: 'America/New_York',
  Virginia: 'America/New_York',
  Washington: 'America/Los_Angeles',
  'West Virginia': 'America/New_York',
  Wisconsin: 'America/Chicago',
  Wyoming: 'America/Denver',
};

class EventHandler extends require('events') {
  constructor(client, ws, streamSid) {
    super();
    this.client = client;
    this.ws = ws;
    this.streamSid = streamSid;
    this.accumulatedText = '';
    this.processToolCalls = this.processToolCalls.bind(this);
  }

  async onEvent(event) {
    try {
      if (event.event === 'thread.run.requires_action') {
        await this.handleRequiresAction(event.data, event.data.id, event.data.thread_id);
      }
    } catch (error) {
      console.error('Error handling event:', error);
    }
  }

  async handleRequiresAction(data, runId, threadId) {
    try {
      // get the output from the tool call
      const toolOutputs = await this.processToolCalls(
        data.required_action.submit_tool_outputs.tool_calls
      );
      // submit the tool outputs
      await this.submitToolOutputs(toolOutputs, runId, threadId);

      // æäº¤å·¥å…·è¾“å‡ºåŽï¼Œç»§ç»­ç›‘å¬è¿›ä¸€æ­¥çš„å“åº”
      this.listenForResponses(threadId, runId);
    } catch (error) {
      console.error('Error processing required action:', error);
    }
  }

  // ç¡®ä¿ processToolCalls æ–¹æ³•å·²æ­£ç¡®å®šä¹‰
  async processToolCalls(toolCalls) {
    const toolOutputs = await Promise.all(
      toolCalls.map(async (toolCall) => {
        let output = '';
        console.log(`Processing tool: ${toolCall.function.name}`);

        console.log('Tool call arguments:', toolCall.function.arguments);

        if (toolCall.function.name === 'check_cancer_qualification') {
          const args = JSON.parse(toolCall.function.arguments);
          output = await processCancerQualification(
            args.product_used,
            args.cancer_diseases_type,
            args.diagnosis_date,
            args.signed_with_attorney
          );
        } else if (toolCall.function.name === 'check_available_product') {
          const args = JSON.parse(toolCall.function.arguments);
          output = await checkAvailableProduct(args.cancer_type, args.diagnosis_date);
        } else if (toolCall.function.name === 'check_medical_qualification') {
          const args = JSON.parse(toolCall.function.arguments);
          output = await processMedicalQualification(
            args.product_or_procedure,
            args.experience_date,
            args.complications,
            args.signed_with_attorney
          );
        } else if (toolCall.function.name === 'check_accident_qualification') {
          const args = JSON.parse(toolCall.function.arguments);
          output = await processAccidentQualification(
            args.had_accident,
            args.accident_date,
            args.signed_with_attorney,
            args.not_at_fault,
            args.occurred_in_california,
            args.insured,
            args.injured,
            args.undergone_treatment
          );
        } else if (toolCall.function.name === 'check_hospital_info') {
          const args = JSON.parse(toolCall.function.arguments);
          output = await checkHospitalInfo(args.hospital_city, args.state, args.hospital_name);
        } else if (toolCall.function.name === 'check_pharmacy_info') {
          const args = JSON.parse(toolCall.function.arguments);
          output = await checkPharmacyInfo(args.state, args.city, args.pharmacy_name);
        } else if (toolCall.function.name === 'trigger_PLM_process') {
          const args = JSON.parse(toolCall.function.arguments);
          output = await sendPLMProcess(args.SFLeadID, args.sendMethod);
        } else if (toolCall.function.name === 'check_user_complete_PLM') {
          const args = JSON.parse(toolCall.function.arguments);
          // output = await sendPLMProcess(args.SFLeadID);
        } else if (toolCall.function.name === 'Send_Opt-in_Message') {
          const args = JSON.parse(toolCall.function.arguments);
          output = await sendSMSOptIn(args.Phone, args.Name);
        } else if (toolCall.function.name === 'Check_SMS_Opt-in') {
          const args = JSON.parse(toolCall.function.arguments);
          output = await checkSMSOptedIn(args.SFLeadID);
        } else if (toolCall.function.name === 'Update_User_Info_To_Salesforce') {
          const args = JSON.parse(toolCall.function.arguments);
          output = await updateSingleInfoToSalesforce(
            args.SFLeadID,
            args.Field_To_Update,
            args.New_Value
          );
        } else if (toolCall.function.name === 'check_user_info_from_salesforce') {
          const args = JSON.parse(toolCall.function.arguments);
          output = await checkAndGetClientInfoFromSalesforce(args.name, args.phone_number);
        } else if (toolCall.function.name === 'send_appointment_confirmation') {
          const args = JSON.parse(toolCall.function.arguments);
          output = await sendAppointmentConfirmation(
            args.state,
            args.phone_number,
            args.email,
            args.callback_time,
            args.notification_method
          );
        }
        // å‘ index.js å‘é€å·¥å…·è°ƒç”¨åç§°å’Œç»“æžœ
        this.emit('toolCallProcessed', {
          toolCallName: toolCall.function.name,
          output,
        });

        return {
          tool_call_id: toolCall.id,
          output: JSON.stringify(output),
        };
      })
    );
    return toolOutputs;
  }

  async listenForResponses(threadId, runId) {
    try {
      const threadMessagesFetched = await openai.beta.threads.messages.list(threadId);
      const assistantMessages = threadMessagesFetched.data.filter(
        (message) => message.role === 'assistant'
      );
      let responseText = '';
      if (assistantMessages.length > 0) responseText = assistantMessages[0].content[0].text.value;
      this.sendResponseToUser(responseText);
      // å‘ index.js å‘é€å·¥å…·è°ƒç”¨åç§°å’Œç»“æžœ
      this.emit('toolCallProcessedTextResult', {
        textResult: responseText,
      });

      // for await (const event of run) {
      //     this.emit("event", event);
      // }
    } catch (error) {
      console.error('Error listening for responses:', error);
    }
  }

  async sendResponseToUser(response) {
    console.log('Sending response to user:', response);
    const audioFileName = `responseAudio_${Date.now()}_${Math.random()}.wav`;
    // Wait for 1.5 seconds
    await new Promise((resolve) => setTimeout(resolve, 1500));
    // Send the "clear" event to the WebSocket before queuing the audio
    // this.ws.send(JSON.stringify({
    //     "event": "clear",
    //     "streamSid": this.streamSid
    // }));

    // Ã¨Â°Æ’Ã§â€Â¨ queueAudio Ã¥â€¡Â½Ã¦â€¢Â°Ã¥Â°â€ Ã¦â€“â€¡Ã¦Å“Â¬Ã¨Â½Â¬Ã¦ÂÂ¢Ã¤Â¸ÂºÃ©Å¸Â³Ã©Â¢â€˜Ã¥Â¹Â¶Ã¦â€™Â­Ã¦â€Â¾ SMS&PLMTest
    const { queueAudio } = require('./index');
    queueAudio(response, audioFileName, this.ws, this.streamSid, true);
  }

  async submitToolOutputs(toolOutputs, runId, threadId) {
    try {
      const stream = this.client.beta.threads.runs.submitToolOutputsStream(threadId, runId, {
        tool_outputs: toolOutputs,
      });
      for await (const event of stream) {
        this.emit('event', event);
      }
    } catch (error) {
      console.error('Error submitting tool outputs:', error);
    }
  }
}

/*
 * Process the qualification for a given product based on the cancer type, diagnosis date, and whether the user has signed with an attorney.
 * The function checks if the specified cancer type and diagnosis date qualify the user for the given product and whether the user has not signed with an attorney.
 * Time complexity: O(n*m*k), where n is the number of products, m is the number of cancers for each product, and k is the number of characters in the cancer type.
 * @param {string} productUsed - The product used by the user
 * @param {string} cancerType - The type of cancer to check for qualification
 * @param {string} diagnosisDate - The diagnosis date of the cancer
 * @param {boolean} signedWithAttorney - Whether the user has signed with an attorney
 * @returns {object} - An object containing the qualification status as a boolean (isQualified: true or false)
 */
async function processCancerQualification(
  productUsed,
  cancerType,
  diagnosisDate,
  signedWithAttorney
) {
  console.log(
    'Processing qualification for product used',
    productUsed,
    ', cancer type:',
    cancerType,
    'diagnosis date:',
    diagnosisDate,
    'signed with attorney:',
    signedWithAttorney
  );
  if (signedWithAttorney) {
    // return { isQualified: false };
    return false;
  }
  for (let product of cancerProducts) {
    if (product.name === productUsed) {
      if (diagnosisDate >= product.startDate && diagnosisDate <= product.endDate) {
        for (let productCancer of product.cancers) {
          // Use includes() to perform fuzzy matching
          if (
            productCancer.toLowerCase().includes(cancerType.toLowerCase()) ||
            cancerType.toLowerCase().includes(productCancer.toLowerCase())
          ) {
            // return { isQualified: true };
            return true;
          }
        }
      }
    }
  }

  // return { isQualified: false };
  return false;
}

/*
 * Process the qualification for a given product or procedure based on the experience date, complications, and whether the user has signed with an attorney.
 * The function checks if the specified product or procedure and experience date qualify the user for the given product and whether the user has not signed with an attorney.
 * Time complexity: O(n*m*k), where n is the number of products, m is the number of complications for each product, and k is the number of characters in the complication type.
 * @param {string} productOrProcedure - The product or procedure used by the user
 * @param {string} experienceDate - The date of the experience with the product or procedure
 * @param {string} complications - The complications experienced by the user
 * @param {boolean} signed_with_attorney - Whether the user has signed with an attorney
 * @returns {object} - An object containing the qualification status as a boolean (isQualified: true or false)
 */
async function processMedicalQualification(
  productOrProcedure,
  experienceDate,
  complications,
  signed_with_attorney
) {
  console.log(
    'Processing qualification for product or procedure:',
    productOrProcedure,
    'experience date:',
    experienceDate,
    'complications:',
    complications,
    'signed with attorney:',
    signed_with_attorney
  );

  if (signed_with_attorney) {
    // return { isQualified: false };
    return false;
  }

  for (let product of medicalProducts) {
    if (product.productUsed.includes(productOrProcedure)) {
      if (experienceDate >= product.startDate && experienceDate <= product.endDate) {
        for (let productComplication of product.complications) {
          if (
            productComplication.toLowerCase().includes(complications.toLowerCase()) ||
            complications.toLowerCase().includes(productComplication.toLowerCase())
          ) {
            // return { isQualified: true };
            return true;
          }
        }
      }
    }
  }

  // return { isQualified: false };
  return false;
}

/*
 * Check available products for a given cancer type and diagnosis date, and return the list of available products
 * Time complexity: O(n*m*k), where n is the number of products and m is the number of cancers for each product, k is the number of characters in the cancer type
 * @param {string} cancerType - The cancer type to check for available products
 * @param {string} diagnosisDate - The diagnosis date of the cancer
 */
async function checkAvailableProduct(cancerType, diagnosisDate) {
  console.log(
    'Checking available products for cancer type:',
    cancerType,
    'diagnosis date:',
    diagnosisDate
  );
  let availableProducts = [];

  for (let product of cancerProducts) {
    // First determine if the diagnosis date is within the product's start and end date
    if (diagnosisDate >= product.startDate && diagnosisDate <= product.endDate) {
      for (let productCancer of product.cancers) {
        // Use includes() to check if the product's cancer type contains the input cancer type or vice versa
        if (
          productCancer.toLowerCase().includes(cancerType.toLowerCase()) ||
          cancerType.toLowerCase().includes(productCancer.toLowerCase())
        ) {
          availableProducts.push(product.name);
          break; // If found, break the loop and move to the next product
        }
      }
    }
  }

  // const result = {"Available Products": availableProducts};
  const result = availableProducts;
  return result;
}

/*
 * Process the qualification for an accident claim based on various factors.
 * The function checks if the accident occurred within the last 24 months and whether several other conditions are met.
 * @param {boolean} had_accident - Whether the user had an accident
 * @param {string} accident_date - The date of the accident in YYYY-MM format
 * @param {boolean} signed_with_attorney - Whether the user has signed with an attorney
 * @param {boolean} not_at_fault - Whether the user was not at fault
 * @param {boolean} occurred_in_california - Whether the accident occurred in California
 * @param {boolean} insured - Whether the user was insured at the time of the accident
 * @param {boolean} injured - Whether the user was injured in the accident
 * @param {string} undergone_treatment - The treatment undergone by the user, must not be an empty string
 * @returns {object} - Returns an object with the qualification status as a boolean (isQualified: true or false)
 */
async function processAccidentQualification(
  had_accident,
  accident_date,
  signed_with_attorney,
  not_at_fault,
  occurred_in_california,
  insured,
  injured,
  undergone_treatment
) {
  console.log('Processing accident qualification...');

  // Calculate the date 24 months ago in YYYY-MM format
  const past24MonthsDate = new Date();
  past24MonthsDate.setMonth(past24MonthsDate.getMonth() - 24);
  const past24Months = past24MonthsDate.toISOString().slice(0, 7);

  // Check if the basic required conditions are not met
  if (!had_accident) {
    console.log('User did not have an accident.');
    return false;
  }

  if (signed_with_attorney) {
    console.log('User has already signed with an attorney.');
    return false;
  }

  if (!not_at_fault) {
    console.log('User is at fault for the accident.');
    return false;
  }

  if (!occurred_in_california) {
    console.log('Accident did not occur in California.');
    return false;
  }

  if (!insured) {
    console.log('User is not insured.');
    return false;
  }

  if (!injured) {
    console.log('User is not injured.');
    return false;
  }

  if (undergone_treatment === 'no') {
    console.log('User has undergone treatment.');
    return false;
  }

  if (accident_date < past24Months) {
    console.log('Accident did not occur within the past 24 months.');
    return false;
  }

  // All conditions met, user is qualified
  return true;
}

/*
This one only returns one single hospital, not used at this time

* Check information about a hospital based on the state and hospital name provided by the user.
* The function simulates a lookup and returns a placeholder object with the result.
* @param {string} state - The state where the hospital is located.
* @param {string} hospital_name - The name of the hospital.
* @returns {object} - An object containing basic hospital information.
*/
// async function checkHospitalInfo(City, state, hospital_name) {
//   console.log(`Checking hospital info for ${hospital_name} in ${state} on ${City}...`);

//   try {
//     // Step 1: Get the place ID using the Find Place from Query API
//     const findPlaceUrl = `https://maps.googleapis.com/maps/api/place/findplacefromtext/json`;
//     const findPlaceResponse = await axios.get(findPlaceUrl, {
//       params: {
//         input: `${hospital_name} Medical ${City} ${state}`,
//         inputtype: 'textquery',
//         fields: 'name,formatted_address,place_id,type',
//         key: GOOGLE_PLACES_API_KEY,
//       },
//     });

//     console.log('findplaceï¼š ',findPlaceResponse.data.candidates[0]);

//     if (!findPlaceResponse.data.candidates || findPlaceResponse.data.candidates.length === 0) {
//       throw new Error('No place found with the given hospital name and state.');
//     }

//     const placeId = findPlaceResponse.data.candidates[0].place_id;

//     // Step 2: Get details using the Place Details API
//     const placeDetailsUrl = `https://maps.googleapis.com/maps/api/place/details/json`;
//     const placeDetailsResponse = await axios.get(placeDetailsUrl, {
//       params: {
//         place_id: placeId,
//         fields: 'name,formatted_address,formatted_phone_number',
//         key: GOOGLE_PLACES_API_KEY,
//       },
//     });

//     const placeDetails = placeDetailsResponse.data.result;
//     if (!placeDetails) {
//       throw new Error('Unable to fetch details for the given place ID.');
//     }

//     // Construct the result
//     return {
//       name: placeDetails.name,
//       address: placeDetails.formatted_address,
//       phone_number: placeDetails.formatted_phone_number,
//     };
//   } catch (error) {
//     console.error('Error fetching hospital info:', error.message);
//     return {
//       error: 'Unable to retrieve hospital information. Please try again later.',
//     };
//   }
// }

/*
 * Check information about hospitals based on the state and hospital name provided by the user.
 * The function simulates a lookup and returns a placeholder object with the result.
 * @param {string} state - The state where the hospital is located.
 * @param {string} hospitalName - The name of the hospital.
 * @returns {object} - An object containing basic hospital information.
 */
async function checkHospitalInfo(city, state, hospitalName) {
  console.log(`Searching hospital info for ${hospitalName} in ${city}, ${state}...`);

  try {
    const url = 'https://places.googleapis.com/v1/places:searchText';

    const requestBody = {
      textQuery: `${hospitalName} Medical in ${city}, ${state}`,
      pageSize: 20, // Max results per page
    };

    const headers = {
      'Content-Type': 'application/json',
      'X-Goog-Api-Key': GOOGLE_PLACES_API_KEY,
      'X-Goog-FieldMask': 'places.displayName,places.formattedAddress,places.primaryType',
    };

    const response = await axios.post(url, requestBody, { headers });

    if (!response.data || !response.data.places || response.data.places.length === 0) {
      throw new Error('No places found with the given query.');
    }

    // Extract relevant fields
    const results = response.data.places.map((place) => ({
      name: place.displayName?.text || 'N/A',
      address: place.formattedAddress || 'N/A',
      primaryType: place.primaryType || 'N/A',
    }));

    console.log('Search hospital result: ', results);

    return results;
  } catch (error) {
    console.error('Error fetching hospital info:', error.message);
    return {
      error: 'Unable to retrieve hospital information. Please try again later.',
    };
  }
}

/*
 * Check information about a pharmacy using Google Places API to fetch the phone number and address.
 * @param {string} state - The state where the pharmacy is located.
 * @param {string} city - The city where the pharmacy is located.
 * @param {string} pharmacy_name - The name of the pharmacy.
 * @returns {object} - An object containing pharmacy information including name, address, and phone number.
 */
async function checkPharmacyInfo(state, city, pharmacy_name) {
  console.log(`Checking pharmacy info for ${pharmacy_name} in ${city}, ${state}...`);

  try {
    // Step 1: Get the place ID using the Find Place from Query API
    const findPlaceUrl = `https://maps.googleapis.com/maps/api/place/findplacefromtext/json`;
    const findPlaceResponse = await axios.get(findPlaceUrl, {
      params: {
        input: `${pharmacy_name} medical, ${city}, ${state}`,
        inputtype: 'textquery',
        fields: 'place_id',
        key: GOOGLE_PLACES_API_KEY,
      },
    });

    if (!findPlaceResponse.data.candidates || findPlaceResponse.data.candidates.length === 0) {
      throw new Error('No place found with the given pharmacy name, city, and state.');
    }

    const placeId = findPlaceResponse.data.candidates[0].place_id;

    // Step 2: Get details using the Place Details API
    const placeDetailsUrl = `https://maps.googleapis.com/maps/api/place/details/json`;
    const placeDetailsResponse = await axios.get(placeDetailsUrl, {
      params: {
        place_id: placeId,
        fields: 'name,formatted_address,formatted_phone_number',
        key: GOOGLE_PLACES_API_KEY,
      },
    });

    const placeDetails = placeDetailsResponse.data.result;
    if (!placeDetails) {
      throw new Error('Unable to fetch details for the given place ID.');
    }

    // Construct the result
    return {
      name: placeDetails.name,
      address: placeDetails.formatted_address.replace('Blvd', 'Boulevard'),
      phone_number: placeDetails.formatted_phone_number,
    };
  } catch (error) {
    console.error('Error fetching pharmacy info:', error.message);
    return {
      error: 'Unable to retrieve pharmacy information. Please try again later.',
    };
  }
}

/**
 * Sends a user's Lead information to the PLM Verification API based on the specified method.
 * The function performs a sanity check to validate key Lead data fields before sending the data to either
 * the "email" or "sms" endpoint based on the `sendMethod` argument.
 * @param {string} SFLeadID - The Salesforce Lead ID to retrieve the Lead record for processing.
 * @param {string} sendMethod - The method to use for sending data to PLM Verification ("email" or "sms").
 * @returns {object} - Returns a JSON object with success status and an optional failure reason.
 *                     Success format: { "send message success": true, "fail reason": "" }
 *                     Failure format: { "send message success": false, "fail reason": "[Specific reason]" }
 */
async function sendPLMProcess(SFLeadID, sendMethod) {
  console.log('Send PLM Process with SFLeadID: ', SFLeadID, ' and sendMehtod: ', sendMethod);
  try {
    // Step 1: Fetch Lead record from Salesforce
    await conn.login('<EMAIL>', 'Pusati9?8oqas8ldaTr@');
    console.log('Welcome to Salesforce');
    const result = await conn.sobject('Lead').retrieve(SFLeadID);
    // console.log("Salesforce result: ", result);
    if (!result) {
      console.log('Unable find the lead in Salesforce.');
      return {
        'send message success': false,
        'fail reason': 'Lead record not found in Salesforce.',
      };
    }

    // Step 2: Prepare payload for sanity check based on Lead information
    const sanityCheckPayload = new URLSearchParams({
      firm: result.Firm__r?.Name || 'testara',
      group: result.Law_Firm_Group__c || '',
      campaign: result.Campaign_Type__c || '',
      lead_order: result.USE_THIS_LEAD_ORDER__c || '',
      retainer: result.USE_THIS_RETAINER_TEMPLATE__c || '',
      language: result.Notes__c?.toLowerCase().includes('spanish') ? 'es' : 'en',
    });

    // Step 3: Perform sanity check
    const sanityCheckResponse = JSON.parse(
      await performHttpRequest(
        'https://api.onlyclassactions.com/sanityCheck2/',
        sanityCheckPayload.toString()
      )
    );
    console.log('Sanity check response: ', sanityCheckResponse);

    if (!sanityCheckResponse['error'] === false) {
      console.log('Did not pass PLM sanity check.');
      return {
        'send message success': false,
        'fail reason': 'Sanity check failed.',
      };
    }

    // Step 4: Prepare payload for PLM verification based on Lead information
    const pandaDocFields = {
      Lead_DateSentContract: result.Date_Sent_Contract__c,
      Lead_FullName: result.Name,
      Lead_SocialSecurity: result.Social_Security__c,
      Lead_Street: result.Street__c,
      Lead_City: result.City__c,
      Lead_State: result.State__c,
      Lead_ZipCode: result.Zip_Code__c,
      Lead_BirthDate: result.Birth_Date__c,
      Lead_DateOfIncident: result.Date_of_incident__c,
      Lead_NameofClaimant: result.Name_of_Claimant__c,
      Lead_DateDiagnosed: result.Date_Diagnosed_Injury_Date__c,
      Lead_WhenLastExposedUsed: result.When_last_exposed__c,
      Lead_When1stExposedUsed: result.When_1st_exposed_used__c,
      Lead_CPAPDL: result.Driver_s_License_Number__c,
      Lead_DateDeceased: result.Date_Deceased__c,
    };

    const payload = new URLSearchParams({
      salesforceID: result.Id,
      firstName: result.First_Name__c || '',
      lastName: result.Last_Name__c || '',
      phone: result.Phone || '',
      email: result.Email || '',
      claimType: 'b', // hardcoded example, adjust as needed
      firm: result.Firm__r?.Name || 'testara',
      group: result.Law_Firm_Group__c || '',
      campaign: result.Campaign_Type__c || '',
      lead_order: result.USE_THIS_LEAD_ORDER__c || '',
      retainer: result.USE_THIS_RETAINER_TEMPLATE__c || '',
      language: result.Notes__c?.toLowerCase().includes('spanish') ? 'es' : 'en',
      panda_map: JSON.stringify(pandaDocFields),
    });
    // console.log("PLM Payload: ", payload);

    // Step 5: Send data to PLM verification endpoint based on `sendMethod`
    const endpoint =
      sendMethod === 'email' || sendMethod === 'Email'
        ? 'https://api.onlyclassactions.com/genURL2/email/'
        : 'https://api.onlyclassactions.com/genURL2/sms/';
    const verificationResponse = JSON.parse(await performHttpRequest(endpoint, payload.toString()));
    console.log('Verficiation response: ', verificationResponse);
    if (verificationResponse['error'] === false) {
      return {
        'send message success': true,
        'fail reason': '',
      };
    } else {
      return {
        'send message success': false,
        'fail reason': 'Failed to send message to PLM verification.',
      };
    }
  } catch (error) {
    return {
      'send message success': false,
      'fail reason': error.message,
    };
  }
}

async function performHttpRequest(url, body) {
  return new Promise((resolve, reject) => {
    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Authorization: 'testKeyAra2000', // Authorization Key
      },
    };

    const req = https.request(url, options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve(data);
      });
    });

    req.on('error', (e) => {
      reject(new Error(`Request failed: ${e.message}`));
    });

    req.write(body);
    req.end();
  });
}

/**
 * Fetches a Lead's recording using a Salesforce Lead ID.
 * @param {string} SFLeadID - The Salesforce Lead ID to retrieve the Lead record for processing.
 * @param {Array<string>} requestedFields - Array list of fields that the request wants to get.
 * @returns {object} - Returns a JSON object with required fields.
 *                     Success format: { "fetch success": true, "data": { requested fields } }
 *                     Failure format: { "fetch success": false, "fail reason": "[Specific reason]" }
 */

async function fetchSalesforceLeadRecord(SFLeadID, requestedFields) {
  console.log(
    'Fetch Salesforce Lead with SFLeadID: ',
    SFLeadID,
    ' and requestedFields: ',
    requestedFields
  );

  if (!Array.isArray(requestedFields) || requestedFields.length === 0) {
    return {
      'fetch success': false,
      'fail reason': 'Invalid or empty requestedFields array.',
    };
  }

  try {
    // Step 1: Login to Salesforce
    await conn.login('<EMAIL>', 'Pusati9?8oqas8ldaTr@');
    console.log('Connected to Salesforce.');

    // Step 2: Fetch Lead record from Salesforce
    const result = await conn.sobject('Lead').retrieve(SFLeadID, requestedFields);
    // console.log("Salesforce Lead Record: ", result);

    if (!result) {
      console.log('Unable to find the lead in Salesforce.');
      return {
        'fetch success': false,
        'fail reason': 'Lead record not found in Salesforce.',
      };
    }

    // Step 3: Prepare the response object with the requested fields
    const responseData = {};
    responseData['SFLeadID'] = SFLeadID;
    requestedFields.forEach((field) => {
      responseData[field] = result[field] || null; // Include field value or null if not found
    });

    return {
      'fetch success': true,
      data: responseData,
    };
  } catch (error) {
    console.error('Error fetching Salesforce Lead Record: ', error);
    return {
      'send message success': false,
      'fail reason': error.message,
    };
  }
}

async function fetchFullThreadHistory(threadId) {
  let chatHistory = new Map();
  let after = null;
  const limit = 100; // Maximum allowed limit per request
  let count = 1; // Move count outside the loop for global tracking

  while (true) {
    const response = await openai.beta.threads.messages.list(threadId, {
      limit: limit,
      after: after,
      order: 'asc', // Fetch messages in ascending order
    });

    const messages = response.data;
    if (messages.length === 0) {
      break; // Exit loop if no more messages are returned
    }

    for (const message of messages) {
      try {
        chatHistory.set(message.role + count, message.content[0].text.value);
      } catch (err) {
        console.error(err);
      }
      count++; // Increment count globally
    }

    after = messages[messages.length - 1].id; // Set 'after' cursor to the ID of the last message
  }

  console.log(`Total messages retrieved: ${chatHistory.size}`);
  return chatHistory;
}

// function to update salesforce record after the call
async function updateSalesforce(threadId) {
  try {
    console.log('in update salesforce function');
    console.log(`Thread ID: ${threadId}`);

    const threadMessagesFetched = await fetchFullThreadHistory(threadId);
    console.log('message is');
    console.log(threadMessagesFetched);

    // Convert the Map object into a string
    const content = Array.from(threadMessagesFetched.values()).join('\n');
    console.log('Formatted content:');
    console.log(content);

    var salesAssistantID = 'asst_xL8kJpQwOATnaBz4seNx0jMF';
    const thread = await openai.beta.threads.create();

    var salesThreadId = thread.id;
    console.log(`Created new thread with ID: ${salesThreadId}`);

    if (salesThreadId) {
      await openai.beta.threads.messages.create(salesThreadId, {
        role: 'user',
        content: content,
      });

      const run = await openai.beta.threads.runs.create(salesThreadId, {
        assistant_id: salesAssistantID,
      });

      let runStatus = await openai.beta.threads.runs.retrieve(salesThreadId, run.id);
      while (runStatus.status !== 'completed') {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        runStatus = await openai.beta.threads.runs.retrieve(salesThreadId, run.id);
      }

      const messages = await openai.beta.threads.messages.list(salesThreadId);
      const assistantMessage = messages.data.find((m) => m.role === 'assistant');
      console.log('The message from assistant is');
      console.log(assistantMessage.content);

      let responseText = assistantMessage.content;

      // new code below
      const jsonString = responseText[0]?.text?.value || '';
      const match = jsonString.match(/```json\n([\s\S]*?)\n```/);

      let jsonContent = {};

      if (match) {
        // Extract JSON content from the code block
        try {
          jsonContent = JSON.parse(match[1]);
        } catch (error) {
          console.error('Error parsing JSON from code block:', error.message);
          throw new Error('Invalid JSON format inside code block');
        }
      } else {
        // If no code block, try parsing the JSON directly
        try {
          jsonContent = JSON.parse(jsonString);
        } catch (error) {
          console.error('Error parsing JSON directly:', error.message);
          throw new Error('Invalid JSON format');
        }
      }

      const fields = jsonContent.fields || [];
      const values = jsonContent.values || [];

      console.log(`fields is ${fields}`);
      console.log(`values is ${values}`);

      if (fields.length === values.length) {
        console.log('Fields and values lengths match. Proceeding to update Salesforce...');

        try {
          // Step 1: Login to Salesforce
          await conn.login('<EMAIL>', 'Pusati9?8oqas8ldaTr@');
          console.log('Connected to Salesforce.');
          // Step 2: Fetch Lead record from Salesforce
          const lead = await conn.sobject('Lead').retrieve('00QUi00000FbogLMAR');

          if (!lead) {
            console.log('Unable to find the lead in Salesforce.');
            return {
              'fetch success': false,
            };
          }

          const updatePayload = { Id: '00QUi00000FbogLMAR' };

          for (let i = 0; i < fields.length; i++) {
            const field = fields[i].trim();
            const value = values[i].trim();

            console.log(
              "Attempting to update field '",
              field,
              "' with new value '",
              value,
              "' for SFLeadID: ",
              '00QUi00000FbogLMAR'
            );

            updatePayload[field] = value;

            let updateResult = await conn.sobject('Lead').update(updatePayload);

            if (updateResult.success) {
              console.log('Updated Successfully : ' + updateResult.id);
            } else {
              console.error('Update Failed: ', updateResult);
            }
          }

          return {
            'fetch success': true,
          };
        } catch (error) {
          console.error('Error updating Salesforce fields:', error);
          return {
            'fetch success': false,
          };
        }
      } else {
        console.log('Error: Fields and values lengths do not match.');
        return {
          'fetch success': false,
        };
      }
    }
  } catch (error) {
    console.error('Error updating Salesforce the record:', error);
    return {
      'fetch success': false,
    };
  }
}

/**
 * Sends an opt-in SMS message to the specified phone number with the user's name.
 * The function ensures the phone number starts with "+1" before sending the message.
 * Uses the MessageMedia API to send the SMS.
 *
 * @param {string} phoneNumber - The phone number to send the SMS to (must include area code).
 * @param {string} name - The name of the recipient to personalize the message.
 * @returns {object} - A JSON object with success status and an optional failure reason.
 *                     Success format: { "send success": true, "fail reason": "" }
 *                     Failure format: { "send success": false, "fail reason": "[Specific reason]" }
 */
async function sendSMSOptIn(phoneNumber, name) {
  console.log('Received SMS Opt-in request for phone and name:', phoneNumber, name);

  // Validate inputs
  if (!phoneNumber || !name) {
    return {
      'send success': false,
      'fail reason': 'Invalid phone number or name.',
    };
  }

  // Ensure the phone number starts with +1
  if (!phoneNumber.startsWith('+1')) {
    phoneNumber = `+1${phoneNumber}`;
  }

  // Define API endpoint and request body
  const apiUrl = 'https://api.messagemedia.com/v1/messages';
  const requestBody = {
    messages: [
      {
        content: `${name}, Thanks for signing up to receive information about your claim. Respond â€œYESâ€ to receive automated messages and calls from PLM to this number about your claim. Consent not reqâ€™d to purchase. Reply STOP to unsubscribe.`,
        destination_number: phoneNumber,
        format: 'SMS',
        delivery_report: true,
      },
    ],
  };

  // API request headers
  const requestHeaders = {
    'Content-Type': 'application/json',
    Authorization: 'Basic NkZLZmpla1FjTEJUVkpyYWozMDY6Y1lXdFlCbWk2b251ekNyb00wUnRNZTFTeE04Z0xS',
  };

  try {
    // Send POST request to MessageMedia API
    const response = await axios.post(apiUrl, requestBody, {
      headers: requestHeaders,
    });

    // Check response status
    if (response.status >= 200 || response.status < 300) {
      console.log('SMS Opt-in message sent successfully:');
      return {
        'send success': true,
        'fail reason': '',
      };
    } else {
      console.error('Failed to send SMS Opt-in message:', response.data);
      return {
        'send success': false,
        'fail reason': 'Unexpected response from API.',
      };
    }
  } catch (error) {
    console.error('Error sending SMS Opt-in message:', error.message);
    return {
      'send success': false,
      'fail reason': error.message,
    };
  }
}

/**
 * Checks if the User Replied Yes "Yes" to SMS opt-in based on the Salesforce Lead ID.
 *
 * @param {string} SFLeadID - The Salesforce Lead ID to check for SMS opt-in.
 * @returns {object} - A JSON object indicating whether the User Replied Yes "Yes".
 *                     Success format: { "User Replied Yes": true }
 *                     Failure format: { "User Replied Yes": false, "False Reason": "[Error message]", "User's reply": "[Actual reply]" }
 */
async function checkSMSOptedIn(SFLeadID) {
  console.log("Check if the User Replied Yes 'Yes' to SMS opt-in with SFLeadID: ", SFLeadID);

  try {
    // Step 1: Login to Salesforce
    await conn.login('<EMAIL>', 'Pusati9?8oqas8ldaTr@');
    console.log('Connected to Salesforce.');

    // Step 2: Fetch Lead record from Salesforce
    const result = await conn.sobject('Lead').retrieve(SFLeadID, ['SMS_Opt_In__c']);
    // console.log("Salesforce Lead Record: ", result);

    if (!result) {
      console.log('Unable to find the lead in Salesforce.');
      return {
        'User Replied Yes': false,
        'False Reason': 'Lead record not found in Salesforce.',
        "User's reply": null,
      };
    }

    // Step 3: Check the value of 'SMS_Opt_In__c' field
    const SMSresult = result['SMS_Opt_In__c'];
    if (typeof SMSresult === 'string' && SMSresult.toLowerCase() === 'yes') {
      return {
        'User Replied Yes': true,
      };
    }

    // If the reply is not "Yes", return the response with details
    return {
      'User Replied Yes': false,
      'False Reason': "User did not reply with 'Yes'.",
      "User's reply": SMSresult || 'No reply recorded',
    };
  } catch (error) {
    console.error('Error fetching Salesforce Lead Record: ', error);
    return {
      'User Replied Yes': false,
      'False Reason': error.message,
      "User's reply": null,
    };
  }
}

/**
 * Checks if the User Replied Yes "Yes" to SMS opt-in based on the Salesforce Lead ID.
 *
 * @param {string} SFLeadID - The Salesforce Lead ID to check for SMS opt-in.
 * @param {string} fieldToBeUpdate - The Salesforce field need to be updated
 * @param {string} newValue - The new value.
 * @returns {object} - A JSON object indicating whether the User Replied Yes "Yes".
 *                     Success format: { "User Replied Yes": true }
 *                     Failure format: { "User Replied Yes": false, "False Reason": "[Error message]"}
 */
async function updateSingleInfoToSalesforce(SFLeadID, fieldToBeUpdate, newValue) {
  console.log(
    "Attempting to update field '",
    fieldToBeUpdate,
    "' with new value '",
    newValue,
    "' for SFLeadID: ",
    SFLeadID
  );

  try {
    // Step 1: Login to Salesforce
    await conn.login('<EMAIL>', 'Pusati9?8oqas8ldaTr@');
    console.log('Connected to Salesforce.');

    // Step 2: Fetch Lead record from Salesforce
    const lead = await conn.sobject('Lead').retrieve(SFLeadID);

    if (!lead) {
      console.log('Unable to find the lead in Salesforce.');
      return {
        'Update Successful': false,
        'Error Reason': 'Lead record not found in Salesforce.',
      };
    }

    // Step 3: Prepare and execute the update
    const updatePayload = { Id: SFLeadID };
    updatePayload[fieldToBeUpdate] = newValue;

    const updateResult = await conn.sobject('Lead').update(updatePayload);

    if (updateResult.success) {
      console.log('Updated Successfully : ' + updateResult.id);
      return {
        'Update Successful': true,
      };
    } else {
      console.error('Update Failed: ', updateResult);
      return {
        'Update Successful': false,
        'Error Reason': 'Failed to update the lead record.',
      };
    }
  } catch (error) {
    console.error('Error updating Salesforce Lead Record: ', error);

    // Send an error notification email
    await sendErrorNotificationEmail(error.message, SFLeadID, fieldToBeUpdate, newValue);

    return {
      'Update Successful': false,
      'Error Reason': error.message,
    };
  }
}

/**
 * Sends an error notification email.
 *
 * @param {string} errorMessage - The error message to include in the email.
 * @param {string} SFLeadID - The Salesforce Lead ID associated with the error.
 * @param {string} fieldToBeUpdate - The Salesforce field that failed to update.
 * @param {string} newValue - The new value that was attempted to be updated.
 */
async function sendErrorNotificationEmail(errorMessage, SFLeadID, fieldToBeUpdate, newValue) {
  try {
    // Configure the email transport
    const transporter = nodemailer.createTransport({
      service: 'gmail', // Use your email provider (e.g., Gmail, Outlook, etc.)
      auth: {
        user: '<EMAIL>',
        pass: 'qbavvzelalhkczhw',
      },
    });

    // Email content
    const mailOptions = {
      from: '<EMAIL>',
      to: '<EMAIL>, <EMAIL>',
      subject: "[SimZ Error] Can't Update User's Info To Salesforce",
      text: `
      Dear Team,

      An error occurred while attempting to update a user's information in Salesforce.

      Details:
      - Lead ID: ${SFLeadID}
      - Field Attempted to Update: ${fieldToBeUpdate}
      - New Value: ${newValue}

      Error Message:
      ${errorMessage}

      Please investigate and resolve the issue at your earliest convenience.

      Best regards,
      SimZ Error Handler
      `,
    };

    // Send the email
    const info = await transporter.sendMail(mailOptions);
    console.log('Error notification email sent: ' + info.response);
  } catch (error) {
    console.error('Failed to send error notification email: ', error);
  }
}

/**
 * Searches for a client's record in Salesforce using their name and phone number.
 * If a record is found, it fetches specific client details using fetchSalesforceLeadRecord.
 *
 * @param {string} name - The name of the client to search for.
 * @param {string} phoneNumber - The phone number of the client to search for.
 * @returns {object} - A JSON object with the Salesforce ID and client's information if found.
 *                    If no record is found, returns { "found the record": false }.
 */
async function checkAndGetClientInfoFromSalesforce(name, phoneNumber) {
  console.log(`Searching for client record with Name: ${name}, Phone: ${phoneNumber}`);

  const requiredFields = ['Name', 'Phone', 'Email', 'Street__c', 'City__c', 'State__c'];

  try {
    // Step 1: Log in to Salesforce
    await conn.login('<EMAIL>', 'Pusati9?8oqas8ldaTr@');
    console.log('Connected to Salesforce.');

    // Step 2: Search for the Lead record based on name and phone number
    const query = `SELECT Id FROM Lead WHERE Name = '${name}' AND Phone = '${phoneNumber}' LIMIT 1`;
    const result = await conn.query(query);

    if (result.records.length === 0) {
      console.log('No record found matching the criteria.');
      return {
        'found the record': false,
      };
    }

    const SFLeadID = result.records[0].Id;
    console.log(`Found record in Salesforce with ID: ${SFLeadID}`);

    // Step 3: Fetch client details using fetchSalesforceLeadRecord
    const fetchResult = await fetchSalesforceLeadRecord(SFLeadID, requiredFields);

    if (fetchResult['fetch success'] === true) {
      return {
        'found the record': true,
        'Salesforce ID': SFLeadID,
        "User's Information": fetchResult.data,
      };
    } else {
      console.log('Failed to fetch additional client information.');
      return {
        'found the record': false,
      };
    }
  } catch (error) {
    console.error('Error checking and fetching client info: ', error);
    return {
      'found the record': false,
    };
  }
}

/**
 * Sends an appointment confirmation to the user via email or SMS, depending on the selected notification method.
 * For email notifications, an ICS file is generated and attached. For SMS notifications, a message is sent using Twilio.
 *
 * @param {string} state - The state where the user is located. Used to determine the timezone.
 * @param {string} phone_number - The user's phone number in E.164 format (e.g., +1234567890). Required for SMS notifications.
 * @param {string} email - The user's email address. Required for email notifications.
 * @param {string} callback_time - The preferred callback time in ISO 8601 format (e.g., 2025-01-20T10:00:00Z).
 * @param {string} notification_method - The method of notification, either 'email' or 'SMS'.
 * @returns {object} - A JSON object with the success status and a message indicating the result of the operation.
 *                     Success format: { success: true, message: "Appointment confirmation sent via email." }
 *                     Failure format: { success: false, message: "Error message explaining the failure." }
 *
 * @throws {Error} - Throws an error if the state is unsupported, ICS file creation fails, or notification sending fails.
 */
async function sendAppointmentConfirmation(
  state,
  phone_number,
  email,
  callback_time,
  notification_method
) {
  try {
    const timeZone = stateToTimeZone[state];
    if (!timeZone) {
      throw new Error(`Unsupported state: ${state}`);
    }

    const eventStartTime = new Date(callback_time);
    const eventEndTime = new Date(eventStartTime.getTime() + 30 * 60 * 1000);
    const eventStartArray = [
      eventStartTime.getUTCFullYear(),
      eventStartTime.getUTCMonth() + 1,
      eventStartTime.getUTCDate(),
      eventStartTime.getUTCHours(),
      eventStartTime.getUTCMinutes(),
    ];
    const eventEndArray = [
      eventEndTime.getUTCFullYear(),
      eventEndTime.getUTCMonth() + 1,
      eventEndTime.getUTCDate(),
      eventEndTime.getUTCHours(),
      eventEndTime.getUTCMinutes(),
    ];

    if (notification_method === 'email') {
      // ç”Ÿæˆ .ics æ–‡ä»¶å†…å®¹
      const { error, value } = ics.createEvent({
        start: eventStartArray,
        end: eventEndArray,
        title: 'Appointment Confirmation',
        description: 'This is your scheduled appointment.',
        location: 'Phone Call',
        status: 'CONFIRMED',
        busyStatus: 'BUSY',
        organizer: {
          name: 'Express Marketing',
          email: '<EMAIL>',
        },
        attendees: [{ name: 'User', email }],
      });

      if (error) {
        throw new Error(`Failed to create ICS file: ${error.message}`);
      } else {
        console.log('ICS created');
      }

      // å‘é€å¸¦é™„ä»¶çš„é‚®ä»¶
      const transporter = nodemailer.createTransport({
        service: 'Gmail',
        auth: {
          user: '<EMAIL>',
          pass: 'qbavvzelalhkczhw',
        },
      });

      await transporter.sendMail({
        from: '<EMAIL>',
        to: email,
        subject: 'Your Appointment Confirmation',
        text: 'Please find your appointment details attached.',
        attachments: [
          {
            filename: 'appointment.ics',
            content: value,
          },
        ],
      });

      console.log('Email sent successfully.');
    } else if (notification_method === 'SMS') {
      // å‘é€çŸ­ä¿¡
      const accountSid = process.env.TWILIO_ACCOUNT_SID;
      const authToken = process.env.TWILIO_AUTH_TOKEN;
      const client = twilio(accountSid, authToken);

      const message = `Your appointment is scheduled for ${callback_time}. If you have questions, please contact us.`;

      await client.messages.create({
        body: message,
        from: process.env.TWILIO_PHONE_NUMBER,
        to: phone_number,
      });

      console.log('SMS sent successfully.');
    }

    // Schedule a call after sending the notification
    const callTimeInMinutes = 0.3; // Example: Schedule call 30 minutes from now
    await scheduleCall('**********', callTimeInMinutes);

    return {
      success: true,
      message: `Appointment confirmation sent via ${notification_method}.`,
    };
  } catch (error) {
    console.error('Error sending appointment confirmation:', error.message);
    return {
      success: false,
      message: error.message,
    };
  }
}

// Export functions and constants
module.exports = { EventHandler, fetchSalesforceLeadRecord, updateSalesforce };
