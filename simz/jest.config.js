module.exports = {
  // Test environment
  testEnvironment: 'node',

  // Test file matching patterns
  testMatch: [
    '<rootDir>/tests/**/*.test.js',
    '<rootDir>/tests/**/*.spec.js'
  ],

  // Coverage configuration
  collectCoverage: true,
  collectCoverageFrom: [
    'llm-integration/services/**/*.js',
    'salesforce/services/**/*.js',
    'call-management/controllers/**/*.js',
    '!**/node_modules/**',
    '!**/coverage/**',
    '!**/tests/**',
    '!**/vendor/**'
  ],

  // Coverage output directory
  coverageDirectory: 'coverage',

  // Coverage report formats
  coverageReporters: [
    'text',
    'lcov',
    'html'
  ],

  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 75,
      functions: 75,
      lines: 75,
      statements: 75
    }
  },

  // Clear mocks
  clearMocks: true,

  // Set timeout
  testTimeout: 10000,

  // Verbose output
  verbose: true,

  // Module name mapping
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1'
  },

  // Setup files
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js']
}; 