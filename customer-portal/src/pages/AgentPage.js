import React, { useState, useEffect, useRef } from "react";
import { useAuth } from "../hooks/useAuth";
import axiosInstance from "../utils/axiosConfig";
import { mulaw } from "alawmulaw";
import "../styles/pages/AgentPage.css";

const API_URL = process.env.REACT_APP_API_URL || "http://10.0.1.170:30000";
const WS_URL = process.env.REACT_APP_WS_URL || "wss://10.0.1.170:30000";

const AgentPage = () => {
  const { token } = useAuth();
  const [agents, setAgents] = useState([]);
  const [prompts, setPrompts] = useState([]);
  const [versions, setVersions] = useState([]);
  const [twilioNumbers, setTwilioNumbers] = useState([]);
  const [modalOpen, setModalOpen] = useState(false);
  const [callModalOpen, setCallModalOpen] = useState(false);
  const [monitorModalOpen, setMonitorModalOpen] = useState(false);
  const [batchCallModalOpen, setBatchCallModalOpen] = useState(false);
  const [amdResultModalOpen, setAmdResultModalOpen] = useState(false);
  const [editingAgent, setEditingAgent] = useState(null);
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [monitoredAgent, setMonitoredAgent] = useState(null);
  const [csvData, setCsvData] = useState([]);
  const [currentCallIndex, setCurrentCallIndex] = useState(0);
  const [isAutoCalling, setIsAutoCalling] = useState(false);
  const [autoCallProgress, setAutoCallProgress] = useState(0);
  const [callSid, setCallSid] = useState(null);
  const [amdResult, setAmdResult] = useState(null);
  const [pendingAmdCalls, setPendingAmdCalls] = useState(new Set());
  const monitoredAgentRef = useRef(null);
  const amdCheckIntervalRef = useRef(null);
  const [formData, setFormData] = useState({
    name: "",
    twilioNumber: "",
    promptId: "",
    promptVersion: "",
    voice: ""
  });
  const [callFormData, setCallFormData] = useState({
    clientName: "",
    toNumber: "",
    campaignType: "",
    salesforceId: "",
    enableAMD: false,
    amdOptions: {
      machineDetection: "Enable",
      asyncAmd: true,
      machineDetectionTimeout: 30,
      machineDetectionSpeechThreshold: 2400,
      machineDetectionSpeechEndThreshold: 1200,
      machineDetectionSilenceTimeout: 5000
    }
  });
  const [transcripts, setTranscripts] = useState([]);
  const [isListening, setIsListening] = useState(false);
  const isListeningRef = useRef(false);
  const [loading, setLoading] = useState(true);
  const [ws, setWs] = useState(null);
  const transcriptQueueRef = useRef([]);
  const isProcessingTranscriptRef = useRef(false);
  const [volume, setVolume] = useState(1.0);

  const audioContextRef = useRef(null);
  const gainNodeRef = useRef(null);
  const scriptNodeRef = useRef(null);
  const audioBuffersRef = useRef([]);
  const audioSetupCompleteRef = useRef(false);

  const maxBufferCount = 20;

  const transcriptContainerRef = useRef(null);
  const reconnectAttemptsRef = useRef(0);
  const maxReconnectAttempts = 5;
  const reconnectTimeoutRef = useRef(null);

  const [isBarging, setIsBarging] = useState(false);
  const bargeAudioContextRef = useRef(null);
  const bargeProcessorRef = useRef(null);
  const bargeSourceRef = useRef(null);
  const bargeStreamRef = useRef(null);
  const isBargingRef = useRef(false);
  const bargeStartTimeRef = useRef(null);

  const [ongoingCallsModalOpen, setOngoingCallsModalOpen] = useState(false);
  const [ongoingCalls, setOngoingCalls] = useState([]);

  useEffect(() => {
    isListeningRef.current = isListening;
  }, [isListening]);

  useEffect(() => {
    if (token) {
      fetchAgents();
      fetchPrompts();
      fetchTwilioNumbers();
      setupWebSocket();
    }

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }

      if (ws) {
        ws.close();
      }
    };
  }, [token]);

  useEffect(() => {
    if (transcriptContainerRef.current) {
      transcriptContainerRef.current.scrollTop =
        transcriptContainerRef.current.scrollHeight;
    }
  }, [transcripts]);

  const setupWebSocket = () => {
    if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
      console.error(
        "WebSocket maximum reconnection attempts reached. Stopping reconnection."
      );
      return;
    }

    const socket = new WebSocket(`${WS_URL}?x-do-not-start-stt=true`);

    socket.onopen = () => {
      console.log("WebSocket connection established");
      // 重连成功，重置重连计数器
      reconnectAttemptsRef.current = 0;
      socket.send(JSON.stringify({ type: "AUTH", token }));

      if (monitoredAgentRef.current) {
        console.log("starting monitoring...");

        socket.send(
          JSON.stringify({
            type: "MONITOR_CALL",
            agentId: monitoredAgentRef.current.Agent_ID,
            callSid: monitoredAgentRef.current.callSid
          })
        );
      }
    };

    socket.onmessage = (event) => {
      const data = JSON.parse(event.data);

      if (data.type === "AGENT_STATUS") {
        setAgents((prevAgents) =>
          prevAgents.map((agent) => {
            const statusUpdate = data.agents.find(
              (a) => a.id === agent.Agent_ID
            );
            if (statusUpdate) {
              return { ...agent, status: statusUpdate.status };
            }
            return agent;
          })
        );
      } else if (
        data.type === "MONITOR_TRANSCRIPT" &&
        monitoredAgentRef.current &&
        data.callSid === monitoredAgentRef.current.callSid
      ) {
        transcriptQueueRef.current.push({
          speaker: data.speaker,
          text: data.transcript,
          timestamp: data.timestamp
        });
        processTranscriptQueue();
      } else if (
        (data.type === "MONITOR_AUDIO" || data.type === "MONITOR_AUDIO_AI") &&
        isListeningRef.current &&
        data.callSid === monitoredAgentRef.current.callSid
      ) {
        processAudioData(data.audio);
      }
    };

    socket.onerror = (error) => {
      console.error("WebSocket error:", error);
      reconnectAttemptsRef.current++;
    };

    socket.onclose = () => {
      console.log("WebSocket connection closed");

      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }

      if (reconnectAttemptsRef.current < maxReconnectAttempts) {
        const delay = Math.min(
          5000 * Math.pow(2, reconnectAttemptsRef.current),
          30000
        ); // 指数退避，最大30秒
        console.log(
          `Attempting to reconnect in ${delay / 1000} seconds... (Attempt ${
            reconnectAttemptsRef.current + 1
          }/${maxReconnectAttempts})`
        );

        reconnectTimeoutRef.current = setTimeout(() => {
          setupWebSocket();
        }, delay);
      } else {
        console.error(
          "WebSocket maximum reconnection attempts reached. Please refresh the page to retry."
        );
      }
    };

    setWs(socket);
  };

  // Calculate delay based on text length
  const calculateDelay = (text) => {
    const baseDelay = 200;
    const charDelay = text.length * 5;
    const randomDelay = Math.random() * 100 - 50;
    return baseDelay + charDelay + randomDelay;
  };

  const processTranscriptQueue = async () => {
    if (
      isProcessingTranscriptRef.current ||
      transcriptQueueRef.current.length === 0
    )
      return;
    isProcessingTranscriptRef.current = true;

    while (transcriptQueueRef.current.length > 0) {
      const transcript = transcriptQueueRef.current.shift();
      const delay = calculateDelay(transcript.text);

      // Wait for the calculated delay
      await new Promise((resolve) => setTimeout(resolve, delay));

      setTranscripts((prev) => [...prev, transcript]);
    }

    isProcessingTranscriptRef.current = false;
  };

  const setupAudioProcessor = () => {
    if (!audioContextRef.current) {
      try {
        window.AudioContext = window.AudioContext || window.webkitAudioContext;
        audioContextRef.current = new AudioContext({
          sampleRate: 8000
        });

        // Create a gain node for volume control
        gainNodeRef.current = audioContextRef.current.createGain();
        gainNodeRef.current.gain.value = volume * 4.0;
        gainNodeRef.current.connect(audioContextRef.current.destination);
      } catch (e) {
        console.error("Web Audio API is not supported in this browser", e);
        return;
      }
    }

    scriptNodeRef.current = audioContextRef.current.createScriptProcessor(
      2048,
      1,
      1
    );
    // Buffer for PCM samples
    let audioSamplesBuffer = new Float32Array(2048);
    let bufferOffset = 0;

    scriptNodeRef.current.onaudioprocess = (audioProcessingEvent) => {
      const outputBuffer = audioProcessingEvent.outputBuffer;
      const outputData = outputBuffer.getChannelData(0);

      // Fill output buffer with our audio data
      if (audioBuffersRef.current.length > 0 && isListeningRef.current) {
        for (let i = 0; i < outputData.length; i++) {
          if (bufferOffset >= audioSamplesBuffer.length) {
            if (audioBuffersRef.current.length > 0) {
              // Convert the next chunk of received audio to samples
              const nextBuffer = audioBuffersRef.current.shift();
              audioSamplesBuffer = convertBufferToSamples(nextBuffer);
              bufferOffset = 0;
            } else {
              // Silence
              outputData[i] = 0;
              continue;
            }
          }

          // Copy from our samples buffer to the output
          if (bufferOffset < audioSamplesBuffer.length) {
            outputData[i] = audioSamplesBuffer[bufferOffset++];
          } else {
            outputData[i] = 0;
          }
        }
      } else {
        for (let i = 0; i < outputData.length; i++) {
          outputData[i] = 0;
        }
      }
    };

    audioSetupCompleteRef.current = true;
    scriptNodeRef.current.connect(gainNodeRef.current);
  };

  // μ-law to PCM conversion table
  const MULAW_DECODE_TABLE = new Int16Array(256);
  (() => {
    for (let i = 0; i < 256; i++) {
      const mulaw = ~i;
      const sign = mulaw & 0x80 ? -1 : 1;
      const exponent = (mulaw & 0x70) >> 4;
      const mantissa = mulaw & 0x0f;
      const magnitude = ((mantissa << 3) + 0x84) << exponent;
      MULAW_DECODE_TABLE[i] = sign * ((magnitude - 0x84) >> 3);
    }
  })();

  const convertBufferToSamples = (data) => {
    try {
      const samples = new Float32Array(data.data.length);

      // Noise gate threshold
      const noiseGateThreshold = 0.003;

      // Volume boost factor
      const volumeBoost = 12.0;

      // Convert from μ-law to PCM using lookup table
      for (let i = 0; i < data.data.length; i++) {
        // Convert μ-law to PCM using lookup table
        const pcmSample = MULAW_DECODE_TABLE[data.data[i]];

        // Normalize to [-1, 1] range
        const normalizedSample = pcmSample / 32768.0;

        // Apply noise gate and volume boost
        if (Math.abs(normalizedSample) < noiseGateThreshold) {
          samples[i] = 0;
        } else {
          // Apply volume boost with soft clipping to prevent distortion
          const boostedSample = normalizedSample * volumeBoost;
          samples[i] = Math.tanh(boostedSample * 0.5);
        }
      }

      // Apply noise reduction using a moving average filter
      const windowSize = 3;
      const filteredSamples = new Float32Array(samples.length);

      for (let i = 0; i < samples.length; i++) {
        let sum = 0;
        let count = 0;

        // Calculate average of surrounding samples
        for (let j = -windowSize; j <= windowSize; j++) {
          const index = i + j;
          if (index >= 0 && index < samples.length) {
            sum += samples[index];
            count++;
          }
        }

        filteredSamples[i] = sum / count;
      }

      // Apply a simple low-pass filter to reduce high-frequency noise
      const lowPassFiltered = new Float32Array(filteredSamples.length);
      const alpha = 0.2;

      lowPassFiltered[0] = filteredSamples[0];
      for (let i = 1; i < filteredSamples.length; i++) {
        lowPassFiltered[i] =
          alpha * filteredSamples[i] + (1 - alpha) * lowPassFiltered[i - 1];
      }

      return lowPassFiltered;
    } catch (error) {
      console.error("Error converting buffer to samples:", error);
      return new Float32Array(2048);
    }
  };

  const processAudioData = async (data) => {
    try {
      // Resume audio context if it was suspended
      if (
        audioContextRef.current &&
        audioContextRef.current.state === "suspended"
      ) {
        await audioContextRef.current.resume();
      }

      audioBuffersRef.current.push(data);

      while (audioBuffersRef.current.length > maxBufferCount) {
        audioBuffersRef.current.shift();
      }
    } catch (error) {
      console.error("Error processing audio data:", error);
    }
  };

  const handleToggleListen = async () => {
    const newIsListening = !isListening;
    setIsListening(newIsListening);

    if (ws && ws.readyState === WebSocket.OPEN && monitoredAgentRef.current) {
      if (newIsListening) {
        ws.send(
          JSON.stringify({
            type: "START_AUDIO_MONITORING",
            callSid: monitoredAgentRef.current.callSid
          })
        );
      } else {
        ws.send(
          JSON.stringify({
            type: "STOP_AUDIO_MONITORING",
            callSid: monitoredAgentRef.current.callSid
          })
        );
      }
    }

    if (newIsListening) {
      if (!audioContextRef.current) {
        setupAudioProcessor();
      }

      if (audioContextRef.current) {
        if (audioContextRef.current.state === "suspended") {
          await audioContextRef.current.resume();
        }

        if (scriptNodeRef.current && !scriptNodeRef.current.isConnected) {
          scriptNodeRef.current.connect(gainNodeRef.current);
          console.log("Audio processor connected");
        }
      }
    } else {
      if (scriptNodeRef.current && scriptNodeRef.current.isConnected) {
        scriptNodeRef.current.disconnect();
        console.log("Audio processor disconnected");
      }

      audioBuffersRef.current = [];
    }
  };

  const handleVolumeChange = (e) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    if (gainNodeRef.current) {
      gainNodeRef.current.gain.value = Math.pow(newVolume, 1.2) * 4.0;
    }
  };

  const fetchAgents = async () => {
    try {
      const response = await axiosInstance.get("/api/agents");
      console.log("Agents response:", response.data);
      setAgents(response.data);
      setLoading(false);
    } catch (error) {
      console.error("Failed to fetch agents:", error);
      setLoading(false);
    }
  };

  const fetchPrompts = async () => {
    try {
      const response = await axiosInstance.get(`${API_URL}/api/prompts`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      setPrompts(response.data);
    } catch (error) {
      console.error("Failed to fetch prompts:", error);
    }
  };

  const fetchPromptVersions = async (promptId) => {
    try {
      const response = await axiosInstance.get(
        `${API_URL}/api/prompt-versions`,
        {
          headers: { Authorization: `Bearer ${token}` },
          params: { promptId }
        }
      );
      setVersions(response.data);
    } catch (error) {
      console.error("Failed to fetch prompt versions:", error);
    }
  };

  const fetchTwilioNumbers = async () => {
    try {
      const response = await axiosInstance.get(
        `${API_URL}/api/twilio-numbers`,
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );
      setTwilioNumbers(response.data);
    } catch (error) {
      console.error("Failed to fetch Twilio numbers:", error);
    }
  };

  const handleCreateAgent = () => {
    setEditingAgent(null);
    setFormData({
      name: "",
      twilioNumber: "",
      promptId: "",
      promptVersion: ""
    });
    setVersions([]);
    setModalOpen(true);
  };

  const handleEditAgent = (agent) => {
    setEditingAgent(agent);
    setFormData({
      name: agent.Agent_Name,
      twilioNumber: agent.Twilio_Number,
      promptId: agent.Prompt_ID,
      promptVersion: agent.Prompt_Version || ""
    });
    fetchPromptVersions(agent.Prompt_ID);
    setModalOpen(true);
  };

  const handleCallAgent = (agent) => {
    setSelectedAgent(agent);
    setCallFormData({
      clientName: "",
      toNumber: "",
      campaignType: "",
      salesforceId: "",
      enableAMD: true,
      amdOptions: {
        machineDetection: "Enable",
        asyncAmd: true,
        machineDetectionTimeout: 30,
        machineDetectionSpeechThreshold: 2400,
        machineDetectionSpeechEndThreshold: 1200,
        machineDetectionSilenceTimeout: 5000
      }
    });
    setCallModalOpen(true);
  };

  const fetchOngoingCallsForAgent = async (agent) => {
    try {
      const response = await axiosInstance.get(
        `${API_URL}/api/ongoing-calls-by-agent/${agent.Agent_ID}`,
        { headers: { Authorization: `Bearer ${token}` } }
      );
      setOngoingCalls(response.data || []);
      setSelectedAgent(agent);
      setOngoingCallsModalOpen(true);
    } catch (error) {
      console.error("Failed to fetch ongoing calls for agent:", error);
      alert("Failed to fetch ongoing calls for this agent.");
    }
  };

  useEffect(() => {
    let intervalId = null;
    if (ongoingCallsModalOpen && selectedAgent) {
      fetchOngoingCallsForAgent(selectedAgent);
      intervalId = setInterval(() => {
        fetchOngoingCallsForAgent(selectedAgent);
      }, 60000);
    }
    return () => {
      if (intervalId) clearInterval(intervalId);
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [ongoingCallsModalOpen, selectedAgent]);

  const handleMonitorAgent = async (agent) => {
    fetchOngoingCallsForAgent(agent);
  };

  const handleSelectOngoingCall = async (call) => {
    const monitoredAgentData = {
      ...selectedAgent,
      callSid: call.twilioCallSid
    };
    setMonitoredAgent(monitoredAgentData);
    monitoredAgentRef.current = monitoredAgentData;
    setTranscripts([]);
    setIsListening(false);
    setMonitorModalOpen(true);
    setOngoingCallsModalOpen(false);
    setupAudioProcessor();
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(
        JSON.stringify({
          type: "MONITOR_CALL",
          agentId: selectedAgent.Agent_ID,
          callSid: call.twilioCallSid
        })
      );
    } else {
      console.error("WebSocket not open");
      alert("Failed to start monitoring: WebSocket not connected");
    }
  };

  const handleSubmit = async () => {
    try {
      const url = editingAgent
        ? `${API_URL}/api/agents/${editingAgent.Agent_ID}`
        : `${API_URL}/api/agents`;
      const method = editingAgent ? "PUT" : "POST";
      const response = await axiosInstance({
        method,
        url,
        data: formData,
        headers: { Authorization: `Bearer ${token}` }
      });
      if (editingAgent) {
        setAgents(
          agents.map((a) =>
            a.Agent_ID === editingAgent.Agent_ID ? response.data : a
          )
        );
      } else {
        setAgents([...agents, response.data]);
      }
      setModalOpen(false);
    } catch (error) {
      console.error("Failed to save agent:", error);
    }
  };

  const handleCallSubmit = async () => {
    try {
      const callData = {
        agentId: selectedAgent.Agent_ID,
        clientName: callFormData.clientName,
        toNumber: callFormData.toNumber,
        salesforceId: callFormData.salesforceId,
        campaignType: callFormData.campaignType,
        promptId: selectedAgent.Prompt_ID,
        promptVersion: selectedAgent.Prompt_Version
      };

      // 如果启用了 AMD，添加 AMD 相关参数
      if (callFormData.enableAMD) {
        callData.enableAMD = true;
        callData.amdOptions = callFormData.amdOptions;
      }

      const response = await axiosInstance.post(
        `${API_URL}/api/make-call`,
        callData,
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      console.log("Call initiated:", response.data);
      setCallSid(response.data.twilioCallSid);

      // 如果启用了 AMD，开始监控 AMD 状态
      if (callFormData.enableAMD && response.data.amdEnabled) {
        setPendingAmdCalls(
          (prev) => new Set([...prev, response.data.twilioCallSid])
        );
        startAmdStatusCheck(
          response.data.twilioCallSid,
          selectedAgent.Agent_ID
        );
      }

      setCallModalOpen(false);
    } catch (error) {
      console.error("Failed to make call:", error);
      alert("Failed to make call, please try again");
    }
  };

  // AMD 状态检查函数
  const startAmdStatusCheck = (callSid, agentId) => {
    const checkAmdStatus = async () => {
      try {
        const response = await axiosInstance.get(
          `${API_URL}/api/amd-status/${callSid}`,
          {
            headers: { Authorization: `Bearer ${token}` }
          }
        );

        if (response.data && response.data.amdResult) {
          const amdResult = response.data.amdResult;

          // 停止检查
          if (amdCheckIntervalRef.current) {
            clearInterval(amdCheckIntervalRef.current);
            amdCheckIntervalRef.current = null;
          }

          // 从待检查列表中移除
          setPendingAmdCalls((prev) => {
            const newSet = new Set(prev);
            newSet.delete(callSid);
            return newSet;
          });

          // 处理 AMD 结果
          handleAmdResult(amdResult, agentId);
        }
      } catch (error) {
        console.error("Failed to check AMD status:", error);
        // 如果检查失败，继续检查一段时间后停止
      }
    };

    // 立即检查一次
    checkAmdStatus();

    // 每5秒检查一次，最多检查2分钟
    let checkCount = 0;
    const maxChecks = 24; // 2分钟 / 5秒 = 24次

    amdCheckIntervalRef.current = setInterval(() => {
      checkCount++;
      if (checkCount >= maxChecks) {
        clearInterval(amdCheckIntervalRef.current);
        amdCheckIntervalRef.current = null;
        setPendingAmdCalls((prev) => {
          const newSet = new Set(prev);
          newSet.delete(callSid);
          return newSet;
        });
        return;
      }
      checkAmdStatus();
    }, 5000);
  };

  // 处理 AMD 结果
  const handleAmdResult = (amdResult, agentId) => {
    setAmdResult(amdResult);

    // 如果检测到答录机、传真机或其他非人工接听
    if (!amdResult.shouldContinueCall) {
      // 显示弹窗
      setAmdResultModalOpen(true);

      // 将 agent 状态设置为 available
      setAgents((prevAgents) =>
        prevAgents.map((agent) => {
          if (agent.Agent_ID === agentId) {
            return { ...agent, status: "available" };
          }
          return agent;
        })
      );
    } else if (amdResult.type === "human") {
      // 检测到人工接听，显示成功提示
      alert("Human detected, call continues");
    }
  };

  const handleCloseMonitor = async () => {
    stopBarging();
    setMonitorModalOpen(false);
    setMonitoredAgent(null);
    monitoredAgentRef.current = null;
    setTranscripts([]);
    setIsListening(false);
    setIsBarging(false);

    transcriptQueueRef.current = [];
    audioBuffersRef.current = [];

    if (scriptNodeRef.current) {
      scriptNodeRef.current.disconnect();
    }

    if (audioContextRef.current) {
      audioContextRef.current.suspend().catch((err) => {
        console.error("Error suspending audio context:", err);
      });
    }

    if (ws && ws.readyState === WebSocket.OPEN && monitoredAgentRef.current) {
      ws.send(
        JSON.stringify({
          type: "STOP_MONITORING",
          agentId: monitoredAgentRef.current.Agent_ID,
          callSid: monitoredAgentRef.current.callSid
        })
      );
    }

    if (selectedAgent) {
      await fetchOngoingCallsForAgent(selectedAgent);
    }
  };

  const startBarging = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 8000,
          channelCount: 1,
          latency: 0
        }
      });
      bargeStreamRef.current = stream;

      bargeAudioContextRef.current = new (window.AudioContext ||
        window.webkitAudioContext)({
        sampleRate: 8000,
        latencyHint: "interactive"
      });

      bargeSourceRef.current =
        bargeAudioContextRef.current.createMediaStreamSource(stream);

      const processor = bargeAudioContextRef.current.createScriptProcessor(
        2048,
        1,
        1
      );
      bargeProcessorRef.current = processor;

      processor.onaudioprocess = (e) => {
        if (!isBargingRef.current) return;

        const inputData = e.inputBuffer.getChannelData(0);

        // Convert Float32Array to Int16Array
        const pcmData = new Int16Array(inputData.length);
        for (let i = 0; i < inputData.length; i++) {
          // Apply noise gate
          const sample = inputData[i];
          if (Math.abs(sample) < 0.02) {
            pcmData[i] = 0;
          } else {
            // Apply volume boost and convert to PCM
            const boostedSample = Math.tanh(sample * 1.5 * 0.5);
            pcmData[i] = Math.max(
              -32768,
              Math.min(32767, Math.floor(boostedSample * 32768))
            );
          }
        }

        // Convert to mulaw using alawmulaw library
        const mulawData = mulaw.encode(pcmData);

        if (
          ws &&
          ws.readyState === WebSocket.OPEN &&
          monitoredAgentRef.current
        ) {
          const base64Data = btoa(String.fromCharCode.apply(null, mulawData));
          ws.send(
            JSON.stringify({
              type: "BARGE_AUDIO",
              agentId: monitoredAgentRef.current.Agent_ID,
              callSid: monitoredAgentRef.current.callSid,
              data: base64Data,
              sampleRate: 8000
            })
          );
        }
      };

      bargeSourceRef.current.connect(processor);
      processor.connect(bargeAudioContextRef.current.destination);

      if (bargeAudioContextRef.current.state === "suspended") {
        await bargeAudioContextRef.current.resume();
      }

      isBargingRef.current = true;
      setIsBarging(true);

      if (!isListening) {
        handleToggleListen();
      }
    } catch (err) {
      console.error("Error accessing microphone:", err);
      alert("Failed to access microphone for barging");
    }
  };

  const stopBarging = () => {
    if (isBarging) {
      isBargingRef.current = false;
      setIsBarging(false);

      if (bargeProcessorRef.current) {
        bargeProcessorRef.current.disconnect();
      }

      if (bargeSourceRef.current) {
        bargeSourceRef.current.disconnect();
      }

      if (bargeStreamRef.current) {
        bargeStreamRef.current.getTracks().forEach((track) => track.stop());
      }

      if (bargeAudioContextRef.current) {
        bargeAudioContextRef.current.close();
      }

      if (isListening) {
        handleToggleListen();
      }
    }
  };

  const handleBarge = () => {
    if (ws && ws.readyState === WebSocket.OPEN && monitoredAgentRef.current) {
      if (!isBarging) {
        ws.send(
          JSON.stringify({
            type: "BARGE_CALL",
            agentId: monitoredAgentRef.current.Agent_ID,
            callSid: monitoredAgentRef.current.callSid
          })
        );
        startBarging();
        bargeStartTimeRef.current = Date.now();
      } else {
        ws.send(
          JSON.stringify({
            type: "END_BARGE",
            agentId: monitoredAgentRef.current.Agent_ID,
            callSid: monitoredAgentRef.current.callSid
          })
        );
        stopBarging();
      }
    }
  };

  const handleEndCall = () => {
    if (ws && ws.readyState === WebSocket.OPEN && monitoredAgentRef.current) {
      ws.send(
        JSON.stringify({
          type: "END_BARGE",
          agentId: monitoredAgentRef.current.Agent_ID,
          callSid: monitoredAgentRef.current.callSid
        })
      );

      // Update agent status
      setAgents((prevAgents) =>
        prevAgents.map((agent) => {
          agent.status = "available";
          return agent;
        })
      );

      handleCloseMonitor();
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    if (name === "promptId") {
      fetchPromptVersions(value);
    }
  };

  const handleCallChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (name === "enableAMD") {
      setCallFormData({
        ...callFormData,
        [name]: checked
      });
    } else if (name.startsWith("amdOptions.")) {
      const optionName = name.split(".")[1];
      setCallFormData({
        ...callFormData,
        amdOptions: {
          ...callFormData.amdOptions,
          [optionName]: type === "number" ? parseInt(value) : value
        }
      });
    } else {
      setCallFormData({
        ...callFormData,
        [name]: type === "checkbox" ? checked : value
      });
    }
  };

  const handleBatchCall = (agent) => {
    setSelectedAgent(agent);
    setBatchCallModalOpen(true);
    setCsvData([]);
    setCurrentCallIndex(0);
    setAutoCallProgress(0);
  };

  const handleCsvUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const text = e.target.result;
        const rows = text
          .split("\n")
          .map((row) => row.split(",").map((cell) => cell.trim()));
        // Remove empty lines and header rows
        const validRows = rows.filter(
          (row) => row.length >= 3 && row[0] && row[1] && row[2]
        );
        setCsvData(validRows);
      };
      reader.readAsText(file);
    }
  };

  const startAutoCall = async () => {
    if (csvData.length === 0) {
      alert("Please upload a CSV file");
      return;
    }

    setIsAutoCalling(true);
    setCurrentCallIndex(0);
    setAutoCallProgress(0);

    let completed = 0;
    const total = csvData.length;

    const makeCall = async (row, index) => {
      const [phoneNumber, firstName, lastName] = row;

      const callData = {
        agentId: selectedAgent.Agent_ID,
        clientName: `${firstName} ${lastName}`,
        toNumber: phoneNumber,
        salesforceId: "",
        campaignType: "batch",
        promptId: selectedAgent.Prompt_ID,
        promptVersion: selectedAgent.Prompt_Version
      };

      // 批量通话也可以启用 AMD
      if (callFormData.enableAMD) {
        callData.enableAMD = true;
        callData.amdOptions = callFormData.amdOptions;
      }

      try {
        const response = await axiosInstance.post(
          `${API_URL}/api/make-call`,
          callData,
          {
            headers: { Authorization: `Bearer ${token}` }
          }
        );
        // 如果启用了 AMD，开始监控状态
        if (callFormData.enableAMD && response.data.amdEnabled) {
          setPendingAmdCalls(
            (prev) => new Set([...prev, response.data.twilioCallSid])
          );
          startAmdStatusCheck(
            response.data.twilioCallSid,
            selectedAgent.Agent_ID
          );
        }
      } catch (error) {
        console.error("Failed to make call:", error);
      } finally {
        completed++;
        setCurrentCallIndex(completed);
        setAutoCallProgress((completed / total) * 100);
        if (completed === total) {
          setIsAutoCalling(false);
        }
      }
    };

    await Promise.all(csvData.map((row, idx) => makeCall(row, idx)));
  };

  const stopAutoCall = () => {
    setIsAutoCalling(false);
  };

  const handleCloseBatchCall = () => {
    stopAutoCall();
    setBatchCallModalOpen(false);
    setCsvData([]);
    setCurrentCallIndex(0);
    setAutoCallProgress(0);
  };

  const handleEndCallFromList = async (agent) => {
    try {
      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(
          JSON.stringify({
            type: "END_BARGE",
            agentId: agent.Agent_ID,
            callSid: callSid
          })
        );

        // Update agent status
        setAgents((prevAgents) =>
          prevAgents.map((a) => {
            if (a.Agent_ID === agent.Agent_ID) {
              return { ...a, status: "available" };
            }
            return a;
          })
        );
      } else {
        console.error("WebSocket not open");
        alert("Failed to end call: WebSocket not connected");
      }
    } catch (error) {
      console.error("Failed to end call:", error);
      alert("Failed to end call.");
    }
  };

  const toLocalTime = (utcDateString) => {
    const date = new Date(utcDateString);
    const pad = (n) => n.toString().padStart(2, "0");
    const year = date.getFullYear();
    const month = pad(date.getMonth() + 1);
    const day = pad(date.getDate());
    const hours = pad(date.getHours());
    const minutes = pad(date.getMinutes());
    const seconds = pad(date.getSeconds());
    return `${year}.${month}.${day} ${hours}:${minutes}:${seconds}`;
  };

  // 清理函数
  useEffect(() => {
    return () => {
      if (amdCheckIntervalRef.current) {
        clearInterval(amdCheckIntervalRef.current);
      }
    };
  }, []);

  return (
    <div className="agent-page">
      <h2>Agents</h2>
      <button onClick={handleCreateAgent}>Create New Agent</button>
      {loading ? (
        <p>Loading agents...</p>
      ) : (
        <div className="agents-grid">
          {agents.map((agent) => (
            <div key={agent.Agent_ID} className="agent-card">
              <div className="agent-info">
                <h3>{agent.Agent_Name}</h3>
                <p className="agent-number">{agent.Twilio_Number}</p>
                <p className="agent-prompt">
                  Prompt: {agent.Prompt_Name} v{agent.Prompt_Version}
                </p>
                <span
                  className={`status-indicator ${agent.status || "available"}`}
                >
                  {agent.status || "available"}
                </span>
              </div>
              <div className="agent-actions">
                <button
                  className="edit-button"
                  onClick={() => handleEditAgent(agent)}
                >
                  Edit
                </button>
                <button
                  className="call-button"
                  onClick={() => handleCallAgent(agent)}
                  disabled={agent.status === "in-call"}
                >
                  Give a Call
                </button>
                <button
                  className="batch-call-button"
                  onClick={() => handleBatchCall(agent)}
                  disabled={agent.status === "in-call"}
                >
                  Batch Call
                </button>
                <button
                  className="monitor-button"
                  onClick={() => handleMonitorAgent(agent)}
                >
                  Monitor
                </button>
                {agent.status === "in-call" && (
                  <button
                    className="end-call-button"
                    onClick={() => handleEndCallFromList(agent)}
                    style={{
                      backgroundColor: "#f44336",
                      color: "white",
                      border: "none",
                      borderRadius: "4px",
                      cursor: "pointer",
                      padding: "8px 16px"
                    }}
                  >
                    End Call
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {modalOpen && (
        <div className="modal">
          <div className="modal-content">
            <h3>{editingAgent ? "Edit Agent" : "Create Agent"}</h3>
            <input
              type="text"
              name="name"
              placeholder="Agent Name"
              value={formData.name}
              onChange={handleChange}
            />
            <select
              name="twilioNumber"
              value={formData.twilioNumber}
              onChange={handleChange}
            >
              <option value="">Select Twilio Number</option>
              {twilioNumbers.map((num) => (
                <option key={num.phoneNumber} value={num.phoneNumber}>
                  {num.phoneNumber}
                </option>
              ))}
            </select>
            <select
              name="promptId"
              value={formData.promptId}
              onChange={handleChange}
            >
              <option value="">Select Prompt</option>
              {prompts.map((prompt) => (
                <option key={prompt.Prompt_ID} value={prompt.Prompt_ID}>
                  {prompt.Prompt_Name}
                </option>
              ))}
            </select>
            <select
              name="promptVersion"
              value={formData.promptVersion}
              onChange={handleChange}
              disabled={!formData.promptId}
            >
              <option value="">Select Version</option>
              {versions.map((version) => (
                <option key={version.Version_ID} value={version.Version}>
                  {version.Version}
                </option>
              ))}
            </select>
            <button onClick={handleSubmit}>Save</button>
            <button onClick={() => setModalOpen(false)}>Cancel</button>
          </div>
        </div>
      )}

      {callModalOpen && selectedAgent && (
        <div className="modal">
          <div className="modal-content">
            <h3>Make Call - {selectedAgent.Agent_Name}</h3>
            <input
              type="text"
              name="clientName"
              placeholder="Client Name"
              value={callFormData.clientName}
              onChange={handleCallChange}
            />
            <input
              type="text"
              name="toNumber"
              placeholder="Client Phone Number"
              value={callFormData.toNumber}
              onChange={handleCallChange}
            />
            <input
              type="text"
              name="salesforceId"
              placeholder="Salesforce ID"
              value={callFormData.salesforceId}
              onChange={handleCallChange}
            />
            <input
              type="text"
              name="campaignType"
              placeholder="Campaign Type"
              value={callFormData.campaignType}
              onChange={handleCallChange}
            />

            {/* AMD 选项 */}
            <div
              style={{
                marginTop: "15px",
                padding: "10px",
                border: "1px solid #ddd",
                borderRadius: "4px"
              }}
            >
              <label
                style={{
                  display: "flex",
                  alignItems: "center",
                  marginBottom: "10px"
                }}
              >
                <input
                  type="checkbox"
                  name="enableAMD"
                  checked={callFormData.enableAMD}
                  onChange={handleCallChange}
                  style={{ marginRight: "8px" }}
                />
                Enable Answering Machine Detection (AMD)
              </label>

              {callFormData.enableAMD && (
                <div
                  style={{
                    marginLeft: "20px",
                    display: "flex",
                    flexDirection: "column",
                    gap: "8px"
                  }}
                >
                  <label
                    style={{
                      display: "flex",
                      alignItems: "center",
                      fontSize: "0.9em"
                    }}
                  >
                    Detection Mode:
                    <select
                      name="amdOptions.machineDetection"
                      value={callFormData.amdOptions.machineDetection}
                      onChange={handleCallChange}
                      style={{ marginLeft: "8px", padding: "2px" }}
                    >
                      <option value="Enable">Immediate Detection</option>
                      <option value="DetectMessageEnd">
                        Wait for Message End
                      </option>
                    </select>
                  </label>

                  <label
                    style={{
                      display: "flex",
                      alignItems: "center",
                      fontSize: "0.9em"
                    }}
                  >
                    <input
                      type="checkbox"
                      name="amdOptions.asyncAmd"
                      checked={callFormData.amdOptions.asyncAmd}
                      onChange={handleCallChange}
                      style={{ marginRight: "8px" }}
                    />
                    Async Detection (Recommended)
                  </label>

                  <label
                    style={{
                      display: "flex",
                      alignItems: "center",
                      fontSize: "0.9em"
                    }}
                  >
                    Detection Timeout (seconds):
                    <input
                      type="number"
                      name="amdOptions.machineDetectionTimeout"
                      value={callFormData.amdOptions.machineDetectionTimeout}
                      onChange={handleCallChange}
                      min="3"
                      max="59"
                      style={{
                        marginLeft: "8px",
                        width: "60px",
                        padding: "2px"
                      }}
                    />
                  </label>
                </div>
              )}
            </div>

            <div style={{ marginTop: "15px", display: "flex", gap: "10px" }}>
              <button onClick={handleCallSubmit}>Make Call</button>
              <button onClick={() => setCallModalOpen(false)}>Cancel</button>
            </div>
          </div>
        </div>
      )}

      {monitorModalOpen && monitoredAgent && (
        <div className="modal">
          <div className="modal-content">
            <h3>Monitoring - {monitoredAgent.Agent_Name}</h3>
            <div
              ref={transcriptContainerRef}
              className="transcript-container"
              style={{
                maxHeight: "400px",
                overflowY: "auto",
                border: "1px solid #ccc",
                padding: "15px",
                backgroundColor: "#f5f5f5",
                borderRadius: "4px",
                scrollBehavior: "smooth"
              }}
            >
              {transcripts.map((t, index) => (
                <div
                  key={index}
                  style={{
                    marginBottom: "10px",
                    padding: "8px",
                    backgroundColor:
                      t.speaker === "assistant"
                        ? "#e3f2fd"
                        : t.speaker === "user"
                        ? "#f1f8e9"
                        : t.speaker === "admin"
                        ? "#fff3e0"
                        : "#f5f5f5",
                    borderRadius: "4px",
                    borderLeft: `4px solid ${
                      t.speaker === "assistant"
                        ? "#2196f3"
                        : t.speaker === "user"
                        ? "#4caf50"
                        : t.speaker === "admin"
                        ? "#ff9800"
                        : "#9e9e9e"
                    }`
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      marginBottom: "4px",
                      color: "#666",
                      fontSize: "0.9em"
                    }}
                  >
                    <strong
                      style={{
                        color:
                          t.speaker === "assistant"
                            ? "#1976d2"
                            : t.speaker === "user"
                            ? "#2e7d32"
                            : t.speaker === "admin"
                            ? "#e65100"
                            : "#616161",
                        textTransform: "capitalize"
                      }}
                    >
                      {t.speaker}
                    </strong>
                    <span>{new Date(t.timestamp).toLocaleTimeString()}</span>
                  </div>
                  <div style={{ color: "#333" }}>{t.text}</div>
                </div>
              ))}
            </div>
            <div
              style={{
                marginTop: "15px",
                display: "flex",
                flexDirection: "column",
                gap: "10px"
              }}
            >
              <div
                style={{ display: "flex", alignItems: "center", gap: "10px" }}
              >
                <label htmlFor="volume">Volume:</label>
                <input
                  type="range"
                  id="volume"
                  min="0"
                  max="1"
                  step="0.01"
                  value={volume}
                  onChange={handleVolumeChange}
                  style={{ flex: 1 }}
                />
              </div>
              <div style={{ display: "flex", gap: "10px" }}>
                <button
                  onClick={handleToggleListen}
                  style={{
                    padding: "8px 16px",
                    backgroundColor: isListening ? "#f44336" : "#4caf50",
                    color: "white",
                    border: "none",
                    borderRadius: "4px",
                    cursor: "pointer"
                  }}
                >
                  {isListening ? "Stop Listening" : "Listen to Call"}
                </button>
                <button
                  onClick={handleBarge}
                  style={{
                    padding: "8px 16px",
                    backgroundColor: isBarging ? "#f44336" : "#ff9800",
                    color: "white",
                    border: "none",
                    borderRadius: "4px",
                    cursor: "pointer"
                  }}
                >
                  {isBarging ? "End Call" : "Barge In"}
                </button>
                <button
                  onClick={handleEndCall}
                  style={{
                    padding: "8px 16px",
                    backgroundColor: "#f44336",
                    color: "white",
                    border: "none",
                    borderRadius: "4px",
                    cursor: "pointer"
                  }}
                >
                  End Call
                </button>
                <button
                  onClick={handleCloseMonitor}
                  style={{
                    padding: "8px 16px",
                    backgroundColor: "#9e9e9e",
                    color: "white",
                    border: "none",
                    borderRadius: "4px",
                    cursor: "pointer"
                  }}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {batchCallModalOpen && selectedAgent && (
        <div className="modal">
          <div className="modal-content">
            <h3>Batch Call - {selectedAgent.Agent_Name}</h3>
            <div style={{ marginBottom: "20px" }}>
              <p>Please upload a CSV file with the following format:</p>
              <ul>
                <li>First column: Phone Number</li>
                <li>Second column: First Name</li>
                <li>Third column: Last Name</li>
              </ul>
              <input
                type="file"
                accept=".csv"
                onChange={handleCsvUpload}
                disabled={isAutoCalling}
              />
            </div>

            {/* 批量通话的 AMD 选项 */}
            <div
              style={{
                marginBottom: "20px",
                padding: "10px",
                border: "1px solid #ddd",
                borderRadius: "4px"
              }}
            >
              <label
                style={{
                  display: "flex",
                  alignItems: "center",
                  marginBottom: "10px"
                }}
              >
                <input
                  type="checkbox"
                  name="enableAMD"
                  checked={callFormData.enableAMD}
                  onChange={handleCallChange}
                  style={{ marginRight: "8px" }}
                />
                Enable Answering Machine Detection (AMD) for Batch Calls
              </label>

              {callFormData.enableAMD && (
                <div
                  style={{
                    marginLeft: "20px",
                    fontSize: "0.9em",
                    color: "#666"
                  }}
                >
                  <p>
                    Note: Enabling AMD will increase call costs by $0.0075 per
                    detection
                  </p>
                </div>
              )}
            </div>

            {csvData.length > 0 && (
              <div style={{ marginBottom: "20px" }}>
                <p>Loaded {csvData.length} records</p>
                {isAutoCalling && (
                  <div>
                    <p>
                      Current progress: {currentCallIndex + 1} /{" "}
                      {csvData.length}
                    </p>
                    <div
                      style={{
                        width: "100%",
                        height: "20px",
                        backgroundColor: "#f0f0f0",
                        borderRadius: "10px",
                        overflow: "hidden"
                      }}
                    >
                      <div
                        style={{
                          width: `${autoCallProgress}%`,
                          height: "100%",
                          backgroundColor: "#4caf50",
                          transition: "width 0.3s ease"
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>
            )}

            <div style={{ display: "flex", gap: "10px" }}>
              {!isAutoCalling ? (
                <button
                  onClick={startAutoCall}
                  disabled={csvData.length === 0}
                  style={{
                    padding: "8px 16px",
                    backgroundColor: "#4caf50",
                    color: "white",
                    border: "none",
                    borderRadius: "4px",
                    cursor: "pointer"
                  }}
                >
                  Start Auto Dialing
                </button>
              ) : (
                <button
                  onClick={stopAutoCall}
                  style={{
                    padding: "8px 16px",
                    backgroundColor: "#f44336",
                    color: "white",
                    border: "none",
                    borderRadius: "4px",
                    cursor: "pointer"
                  }}
                >
                  Stop Auto Dialing
                </button>
              )}
              <button
                onClick={handleCloseBatchCall}
                style={{
                  padding: "8px 16px",
                  backgroundColor: "#9e9e9e",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  cursor: "pointer"
                }}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* AMD 结果弹窗 */}
      {amdResultModalOpen && amdResult && (
        <div className="modal">
          <div className="modal-content">
            <h3>Answering Machine Detection Result</h3>
            <div style={{ marginBottom: "20px" }}>
              <p>
                <strong>Detection Result:</strong> {amdResult.description}
              </p>
              <p>
                <strong>Detection Type:</strong>{" "}
                {amdResult.type === "machine"
                  ? "Answering Machine"
                  : amdResult.type === "fax"
                  ? "Fax Machine"
                  : amdResult.type === "human"
                  ? "Human"
                  : "Unknown"}
              </p>
              <p>
                <strong>Detection Duration:</strong>{" "}
                {amdResult.detectionDuration} milliseconds
              </p>
              <p>
                <strong>Call Status:</strong>{" "}
                {amdResult.shouldContinueCall
                  ? "Continuing"
                  : "Automatically Ended"}
              </p>
            </div>

            {!amdResult.shouldContinueCall && (
              <div
                style={{
                  padding: "10px",
                  backgroundColor: "#fff3cd",
                  border: "1px solid #ffeaa7",
                  borderRadius: "4px",
                  marginBottom: "15px"
                }}
              >
                <p style={{ margin: 0, color: "#856404" }}>
                  ⚠️ Non-human answering detected. Call has been automatically
                  ended and agent status set to available.
                </p>
              </div>
            )}

            <button
              onClick={() => {
                setAmdResultModalOpen(false);
                setAmdResult(null);
              }}
              style={{
                padding: "8px 16px",
                backgroundColor: "#007bff",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer"
              }}
            >
              OK
            </button>
          </div>
        </div>
      )}

      {/* Ongoing Calls Modal */}
      {ongoingCallsModalOpen && selectedAgent && (
        <div className="modal">
          <div className="modal-content">
            <h3>Monitor Calls for {selectedAgent.Agent_Name}</h3>
            {ongoingCalls.length === 0 ? (
              <p>No ongoing calls found for this agent.</p>
            ) : (
              <ul style={{ listStyle: "none", padding: 0 }}>
                {ongoingCalls.map((call) => {
                  let customerNumber = "-";
                  if (selectedAgent && selectedAgent.Twilio_Number) {
                    if (call.callerNumber === selectedAgent.Twilio_Number) {
                      customerNumber = call.toNumber || call.to || "-";
                    } else {
                      customerNumber = call.callerNumber || call.from || "-";
                    }
                  }
                  let directionIcon = null;
                  if (call.callDirection === "Inbound") {
                    directionIcon = (
                      <span
                        title="Inbound"
                        style={{
                          marginRight: 4
                        }}
                      >
                        ⬅️
                      </span>
                    );
                  } else if (call.callDirection === "Outbound") {
                    directionIcon = (
                      <span
                        title="Outbound"
                        style={{
                          marginRight: 4
                        }}
                      >
                        ➡️
                      </span>
                    );
                  } else {
                    directionIcon = <span style={{ marginRight: 4 }}>-</span>;
                  }
                  return (
                    <li
                      key={call.twilioCallSid}
                      style={{
                        border: "1px solid #ccc",
                        borderRadius: "4px",
                        marginBottom: "10px",
                        padding: "10px",
                        background: "#f9f9f9",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        gap: "10px"
                      }}
                    >
                      <span>{customerNumber}</span>
                      <span>{directionIcon}</span>
                      <span>
                        {call.createdAt ? toLocalTime(call.createdAt) : "-"}
                      </span>
                      <button
                        onClick={() => handleSelectOngoingCall(call)}
                        style={{
                          padding: "6px 14px",
                          backgroundColor: "#4caf50",
                          color: "white",
                          border: "none",
                          borderRadius: "4px",
                          cursor: "pointer"
                        }}
                      >
                        Monitor
                      </button>
                    </li>
                  );
                })}
              </ul>
            )}
            <button
              onClick={() => setOngoingCallsModalOpen(false)}
              style={{
                padding: "8px 16px",
                backgroundColor: "#9e9e9e",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer",
                marginTop: "10px"
              }}
            >
              Close
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default AgentPage;
