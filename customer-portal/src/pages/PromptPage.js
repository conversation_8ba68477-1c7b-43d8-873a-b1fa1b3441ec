import React, { useState } from 'react';
import PromptSidebar from '../components/PromptSidebar';
import PromptChat from '../components/PromptChat';
import '../styles/pages/PromptPage.css';

const PromptPage = () => {
  const [selectedPrompt, setSelectedPrompt] = useState(null);

  return (
    <div className="prompt-page-container">
      <PromptSidebar onPromptSelect={setSelectedPrompt} />
      <PromptChat selectedPrompt={selectedPrompt} />
    </div>
  );
};

export default PromptPage;