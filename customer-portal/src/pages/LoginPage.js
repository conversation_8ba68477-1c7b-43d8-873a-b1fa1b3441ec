import React, { useState } from 'react';
import LoginForm from '../components/LoginForm';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { login } from '../services/authService';

const LoginPage = () => {
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { login: setToken } = useAuth();

  const handleLogin = async (username, password) => {
    try {
      const token = await login(username, password);
      setToken(token);
      navigate('/home');
    } catch (err) {
      setError('Login failed. Please check your credentials.');
    }
  };

  return (
    <div style={{ border: '2px solid red', padding: '10px' }}>
      <h1 style={{ color: 'green' }}>Customer Portal Login </h1>
      <LoginForm onLogin={handleLogin} />
      {error && <p style={{ color: 'red' }}>{error}</p>}
      <p>
        Don't have an account? <a href="/register">Register here</a>
      </p>
    </div>
  );
};

export default LoginPage;