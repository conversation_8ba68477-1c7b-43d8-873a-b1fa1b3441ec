import React from 'react';
import { useFetchCalls } from '../hooks/useFetchCalls';
import CallHistory from '../components/CallHistory';
import '../styles/pages/CallHistoryPage.css';

const CallHistoryPage = () => {
  const { calls, loading, error } = useFetchCalls();

  if (loading) return <div className="page-content"><p>Loading...</p></div>;
  if (error) return <div className="page-content"><p>Error: {error}</p></div>;

  return (
    <div className="page-content call-history-page">
      <h1>Call History</h1>
      <CallHistory calls={calls} />
    </div>
  );
};

export default CallHistoryPage;