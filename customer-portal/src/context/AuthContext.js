import React, { createContext, useState, useContext, useEffect } from 'react';
import { isTokenExpired, handleTokenExpiration } from '../utils/tokenUtils';

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [token, setToken] = useState(localStorage.getItem('token') || '');

  // 在组件挂载时检查token是否过期
  useEffect(() => {
    if (token && isTokenExpired(token)) {
      handleTokenExpiration();
    }
  }, [token]);

  const login = (newToken) => {
    setToken(newToken);
    localStorage.setItem('token', newToken);
  };

  const logout = () => {
    setToken('');
    localStorage.removeItem('token');
  };

  // 提供检查token过期的方法，供组件使用
  const checkTokenExpiration = () => {
    if (token && isTokenExpired(token)) {
      handleTokenExpiration();
      return true;
    }
    return false;
  };

  return (
    <AuthContext.Provider value={{ token, login, logout, checkTokenExpiration }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
export { AuthContext }; 