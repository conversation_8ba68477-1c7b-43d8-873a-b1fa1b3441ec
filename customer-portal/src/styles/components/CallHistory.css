/* src/styles/components/CallHistory.css */
.call-list {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.search-section {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 25px;
}

.search-section h3 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 1.2em;
  font-weight: 600;
}

.search-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: end;
  margin-bottom: 15px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  min-width: 200px;
}

.filter-group label {
  margin-bottom: 5px;
  font-weight: 500;
  color: #495057;
  font-size: 0.9em;
}

.search-input,
.date-input {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  background-color: white;
}

.search-input:focus,
.date-input:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.search-input {
  width: 200px;
}

.date-input {
  width: 150px;
}

.filter-actions {
  display: flex;
  align-items: end;
}

.clear-button {
  padding: 8px 16px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
  height: fit-content;
}

.clear-button:hover {
  background-color: #5a6268;
}

.search-results-info {
  color: #6c757d;
  font-size: 14px;
  font-style: italic;
  padding: 10px 0;
  border-top: 1px solid #dee2e6;
  margin-top: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-group {
    min-width: auto;
  }
  
  .search-input,
  .date-input {
    width: 100%;
  }
}

.controls {
  margin-bottom: 20px;
}

.call-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.call-table th,
.call-table td {
  padding: 10px;
  border: 1px solid #ddd;
  text-align: left;
}

.call-table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.call-item:hover {
  background-color: #f9f9f9;
  cursor: pointer;
}

.transcript-details {
  padding: 20px;
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
}

.transcript-content {
  margin-bottom: 20px;
}

.audio-player-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
}

.transcript-entry {
  margin: 5px 0;
}

.transcript-entry.user {
  color: #2c3e50;
}

.transcript-entry.agent {
  color: #2980b9;
}

.transcript-entry.interrupted {
  background-color: #ffe6e6;
}

.timestamp {
  font-size: 0.8em;
  color: #777;
}

.interrupted-icon {
  margin-left: 5px;
  color: #e74c3c;
}

.toggle-icon {
  cursor: pointer;
  margin-left: 10px;
}

.pagination {
  display: flex;
  justify-content: center;
  gap: 5px;
  align-items: center;
}

.pagination button {
  padding: 5px 10px;
  border: 1px solid #ddd;
  background-color: #fff;
  cursor: pointer;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.pagination button:hover:not(:disabled) {
  background-color: #e0e0e0;
}

.pagination button.active {
  background-color: #2980b9;
  color: #fff;
  border-color: #2980b9;
}

.pagination button:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.pagination-nav {
  font-weight: bold;
}

.pagination-ellipsis {
  padding: 5px 10px;
  color: #777;
}

.audio-player {
  display: flex;
  align-items: center;
  width: 60%;
  /* max-width: 800px; */
  margin: 0 auto;
  background-color: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.audio-controls {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  flex-wrap: wrap;
}

.play-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 10px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  min-width: 40px;
  min-height: 40px;
  background-color: #f0f0f0;
}

.play-button:hover {
  background-color: #e0e0e0;
}

.play-button:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

.play-button.loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.audio-progress {
  flex: 1;
  height: 6px;
  background-color: #e0e0e0;
  border-radius: 3px;
  position: relative;
  cursor: pointer;
  overflow: hidden;
  transition: height 0.2s ease;
}

.audio-progress:hover {
  height: 8px;
}

.progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: #2196f3;
  transition: width 0.1s linear;
  border-radius: 3px;
}

.audio-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #666;
  min-width: 100px;
  font-family: monospace;
}

.current-time {
  color: #2196f3;
  font-weight: 500;
}

.time-separator {
  color: #999;
}

.total-time {
  color: #666;
}

.loading-transcript {
  text-align: center;
  color: #666;
  padding: 20px;
}

.loading-spinner {
  display: inline-block;
  animation: spin 1s linear infinite;
}

.control-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  min-width: 36px;
  min-height: 36px;
  background-color: #f0f0f0;
  font-size: 16px;
}

.control-button:hover {
  background-color: #e0e0e0;
}

.control-button:active {
  background-color: #d0d0d0;
}

.speed-control {
  display: flex;
  align-items: center;
}

.speed-select {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  font-size: 12px;
  min-width: 60px;
}

.speed-select:hover {
  border-color: #2196f3;
}

.speed-select:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.download-button {
  background-color: #4caf50 !important;
  color: white;
}

.download-button:hover {
  background-color: #45a049 !important;
}