/* src/styles/pages/agentPage.css */
.agent-page {
    padding: 20px;
  }
  
  .agent-page ul {
    list-style: none;
    padding: 0;
  }
  
  .agent-page li {
    cursor: pointer;
    padding: 10px;
    border-bottom: 1px solid #ccc;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .agent-page li:hover {
    background: #f0f0f0;
  }
  
  .status-indicator {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8em;
    margin-top: 10px;
    color: white;
  }
  
  .status-indicator.available {
    background-color: #4CAF50;
  }
  
  .status-indicator.in-call {
    background-color: #f44336;
  }
  
  .status-indicator.unknown {
    background-color: #9e9e9e;
  }
  
  .modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .modal-content {
    background: white;
    padding: 20px;
    border-radius: 5px;
    width: 500px;
  }
  
  .modal-content input,
  .modal-content select {
    width: 100%;
    margin: 10px 0;
    padding: 5px;
  }
  
  .modal-content button {
    margin: 5px;
  }
  
  button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
  }

  .agent-actions {
    display: flex;
    flex-direction: row;
    gap: 10px;
    margin-top: 15px;
  }

  .agent-actions button {
    flex: 1;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s ease;
    white-space: nowrap;
  }

  .edit-button {
    background-color: #f5f5f5;
    color: #333;
  }

  .edit-button:hover {
    background-color: #e0e0e0;
  }

  .call-button {
    background-color: #03a9f4;
    color: white;
  }

  .call-button:hover {
    background-color: #0288d1;
  }

  .call-button:disabled {
    background-color: #b3e5fc;
    cursor: not-allowed;
  }

  .monitor-button {
    background-color: #2196F3;
    color: white;
  }

  .monitor-button:hover {
    background-color: #1976D2;
  }

  .monitor-modal {
    width: 800px;
    max-width: 90vw;
    height: 80vh;
    display: flex;
    flex-direction: column;
  }

  .transcript-container {
    flex: 1;
    overflow-y: auto;
    background: #f5f5f5;
    padding: 15px;
    margin: 10px 0;
    border-radius: 4px;
    font-family: monospace;
  }

  .transcript-container pre {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  .monitor-controls {
    display: flex;
    justify-content: flex-end;
    padding-top: 10px;
    border-top: 1px solid #eee;
  }

  .agents-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    padding: 20px 0;
  }

  .agent-card {
    background: white;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 20px;
    transition: transform 0.2s ease;
  }

  .agent-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border-color: #bdbdbd;
  }

  .agent-info {
    margin-bottom: 15px;
  }

  .agent-info h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 1.2em;
  }

  .agent-number {
    color: #666;
    margin: 5px 0;
    font-size: 0.9em;
  }

  .agent-prompt {
    color: #666;
    margin: 5px 0;
    font-size: 0.9em;
  }