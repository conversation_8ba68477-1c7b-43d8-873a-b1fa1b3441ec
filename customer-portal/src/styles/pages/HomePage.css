.home-page {
    margin-left: 220px; /* 留出侧边栏空间 */
    padding: 40px;
    text-align: center;
  }
  
  .home-page h1 {
    font-size: 2.5em;
    color: #2c3e50;
    margin-bottom: 10px;
  }
  
  .home-page p {
    font-size: 1.2em;
    color: #7f8c8d;
    margin-bottom: 30px;
  }
  
  .button-container {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
  }
  
  .home-button {
    display: inline-block;
    padding: 15px 30px;
    font-size: 1.1em;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    transition: transform 0.2s, box-shadow 0.2s;
  }
  
  .home-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  .home-button.agent {
    background-color: #3498db;
  }
  
  .home-button.prompt {
    background-color: #e74c3c;
  }
  
  .home-button.history {
    background-color: #2ecc71;
  }
  
  .home-button.account {
    background-color: #f1c40f;
  }