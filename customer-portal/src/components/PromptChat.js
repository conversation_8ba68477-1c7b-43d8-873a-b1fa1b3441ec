import React, { useState } from 'react';
import { chatWithPrompt, clearChatHistory } from '../services/promptService';
import { useAuth } from '../hooks/useAuth';
import '../styles/components/promptChat.css';

const PromptChat = ({ selectedPrompt }) => {
  const { token, checkTokenExpiration } = useAuth();
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [selectedProvider, setSelectedProvider] = useState(selectedPrompt?.provider || 'Grok');

  const handleSend = () => {
    if (checkTokenExpiration()) return;
    
    if (input.trim() && selectedPrompt) {
      const newMessage = { text: input, sender: 'user' };
      setMessages([...messages, newMessage]);
      chatWithPrompt(
        selectedPrompt.Prompt_ID,
        input,
        selectedProvider,
        selectedPrompt.content
      )
        .then((response) => {
          setMessages((prev) => [...prev, { text: response.response, sender: 'prompt' }]);
        })
        .catch((error) => console.error('Failed to chat:', error));
      setInput('');
    }
  };

  const handleClearChat = () => {
    if (checkTokenExpiration()) return;
    
    clearChatHistory()
      .then(() => {
        setMessages([]);
      })
      .catch((error) => console.error('Failed to clear chat:', error));
  };

  const handleProviderChange = (e) => {
    setSelectedProvider(e.target.value);
  };

  return (
    <div className="prompt-chat">
      <div className="chat-header">
        <h3>
          {selectedPrompt ? `Chat with ${selectedPrompt.name || selectedPrompt.Prompt_ID} (v${selectedPrompt.latestVersion || selectedPrompt.Version})` : 'Select a Prompt'}
        </h3>
        {selectedPrompt && (
          <div className="prompt-name">
            Prompt: {selectedPrompt.name || selectedPrompt.Prompt_ID}
          </div>
        )}
      </div>
      <div className="chat-controls">
        <select
          value={selectedProvider}
          onChange={handleProviderChange}
          disabled={!selectedPrompt}
        >
          <option value="Grok">Grok</option>
          <option value="OpenAI">OpenAI</option>
          <option value="Meta">Meta</option>
        </select>
        <button onClick={handleClearChat} disabled={!selectedPrompt || messages.length === 0}>
          Clear Chat
        </button>
      </div>
      <div className="chat-body">
        {messages.map((msg, index) => (
          <div key={index} className={`message ${msg.sender}`}>
            {msg.sender === 'user' ? 'You' : selectedProvider}: {msg.text}
          </div>
        ))}
      </div>
      <div className="chat-footer">
        <input
          type="text"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="Type your message..."
          disabled={!selectedPrompt}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              handleSend();
            }
          }}
        />
        <button onClick={handleSend} disabled={!selectedPrompt}>
          Send
        </button>
      </div>
    </div>
  );
};

export default PromptChat;