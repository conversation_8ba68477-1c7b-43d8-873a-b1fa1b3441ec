// src/components/CallHistory.js
import React, { useState } from 'react';
import { fetchTranscript } from '../services/callService';
import { useAuth } from '../hooks/useAuth';
import '../styles/components/CallHistory.css';

const CallHistory = ({ calls }) => {
  const { checkTokenExpiration } = useAuth();
  const [expandedCallId, setExpandedCallId] = useState(null);
  const [transcripts, setTranscripts] = useState({});
  const [currentPage, setCurrentPage] = useState(1);
  const [sortOrder, setSortOrder] = useState('desc');
  const [currentlyPlaying, setCurrentlyPlaying] = useState(null);
  const [loadingAudio, setLoadingAudio] = useState(null);
  const [audioProgress, setAudioProgress] = useState({});
  const [audioDuration, setAudioDuration] = useState({});
  const [currentTime, setCurrentTime] = useState({});
  const [playbackSpeed, setPlaybackSpeed] = useState({}); // Play Speed
  
  // Search Status
  const [searchFilters, setSearchFilters] = useState({
    clientName: '',
    clientNumber: '',
    simzCallId: '',
    startDate: '',
    endDate: ''
  });
  
  const callsPerPage = 7;
  const maxVisiblePages = 5; 

  // Filter function
  const getFilteredCalls = () => {
    return calls.filter(call => {
      // User Name 
      const clientNameMatch = !searchFilters.clientName || 
        (call.clientName && call.clientName.toLowerCase().includes(searchFilters.clientName.toLowerCase()));
      
      // Client Number
      const clientNumberMatch = !searchFilters.clientNumber || 
        (call.toNumber && call.toNumber.includes(searchFilters.clientNumber));
      
      // SimZ Call ID
      const simzCallIdMatch = !searchFilters.simzCallId || 
        (call.callId && call.callId.toLowerCase().includes(searchFilters.simzCallId.toLowerCase()));
      
      // Date
      const callDate = new Date(call.callDateTime);
      const startDateMatch = !searchFilters.startDate || 
        callDate >= new Date(searchFilters.startDate);
      const endDateMatch = !searchFilters.endDate || 
        callDate <= new Date(new Date(searchFilters.endDate).setHours(23, 59, 59, 999));
      
      return clientNameMatch && clientNumberMatch && simzCallIdMatch && startDateMatch && endDateMatch;
    });
  };


  const handleSearchChange = (field, value) => {
    setSearchFilters(prev => ({
      ...prev,
      [field]: value
    }));
    setCurrentPage(1);
    setExpandedCallId(null); 
  };

  const handleClearSearch = () => {
    setSearchFilters({
      clientName: '',
      clientNumber: '',
      simzCallId: '',
      startDate: '',
      endDate: ''
    });
    setCurrentPage(1);
    setExpandedCallId(null);
  };

  const handleToggleTranscript = async (callId) => {
    // 检查token是否过期
    if (checkTokenExpiration()) return;
    
    if (expandedCallId === callId) {
      setExpandedCallId(null);
    } else {
      if (!transcripts[callId]) {
        try {
          const transcriptData = await fetchTranscript(callId);
          setTranscripts((prev) => ({ ...prev, [callId]: transcriptData }));
          // console.log('[CallHistory] Transcript:', transcriptData);
        } catch (err) {
          console.error('Failed to fetch transcript:', err);
        }
      }
      setExpandedCallId(callId);
    }
  };

  const handlePlayRecording = async (callId, recordingUrl) => {
    if (currentlyPlaying === callId) {
      // If already playing, stop it
      const audio = document.getElementById(`audio-${callId}`);
      if (audio) {
        audio.pause();
        audio.currentTime = 0;
        setCurrentTime(prev => ({ ...prev, [callId]: 0 }));
      }
      setCurrentlyPlaying(null);
      setLoadingAudio(null);
    } else {
      // Stop any currently playing audio
      if (currentlyPlaying) {
        const prevAudio = document.getElementById(`audio-${currentlyPlaying}`);
        if (prevAudio) {
          prevAudio.pause();
          prevAudio.currentTime = 0;
          setCurrentTime(prev => ({ ...prev, [currentlyPlaying]: 0 }));
        }
      }

      // Create new audio element
      const audio = document.getElementById(`audio-${callId}`);
      if (audio) {
        try {
          setLoadingAudio(callId);
          const audioUrl = `${process.env.REACT_APP_API_URL}/api/call-recordings/${recordingUrl}`;
          try {
            const response = await fetch(audioUrl, {
              headers: {
                'ngrok-skip-browser-warning': 'true'
              }
            });
            
            if (response.ok) {
              const blob = await response.blob();
              const blobUrl = URL.createObjectURL(blob);
              audio.src = blobUrl;
              
              // Clean Up Blob
              const cleanup = () => URL.revokeObjectURL(blobUrl);
              audio.addEventListener('ended', cleanup, { once: true });
              audio.addEventListener('error', cleanup, { once: true });
            } else {
              audio.src = audioUrl;
            }
          } catch (fetchError) {
            console.log('Fetch failed, using direct src:', fetchError);
            audio.src = audioUrl;
          }
          
          // Play the audio
          const playPromise = audio.play();
          
          if (playPromise !== undefined) {
            playPromise
              .then(() => {
                setCurrentlyPlaying(callId);
                setLoadingAudio(null);
              })
              .catch(error => {
                console.error('Error playing audio:', error);
                setCurrentlyPlaying(null);
                setLoadingAudio(null);
              });
          }
        } catch (error) {
          console.error('Error playing audio:', error);
          setCurrentlyPlaying(null);
          setLoadingAudio(null);
        }
      }
    }
  };

  const handleTimeUpdate = (callId, e) => {
    const audio = e.target;
    const progress = (audio.currentTime / audio.duration) * 100;
    setAudioProgress(prev => ({ ...prev, [callId]: progress }));
    setCurrentTime(prev => ({ ...prev, [callId]: audio.currentTime }));
  };

  const handleLoadedMetadata = (callId, e) => {
    const audio = e.target;
    setAudioDuration(prev => ({ ...prev, [callId]: audio.duration }));
  };

  const formatTime = (seconds) => {
    if (!seconds && seconds !== 0) return '00:00';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Time Bar
  const handleProgressClick = (callId, e) => {
    const audio = document.getElementById(`audio-${callId}`);
    if (audio && audioDuration[callId]) {
      const progressBar = e.currentTarget;
      const rect = progressBar.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const clickPercent = clickX / rect.width;
      const newTime = clickPercent * audioDuration[callId];
      
      audio.currentTime = newTime;
      setCurrentTime(prev => ({ ...prev, [callId]: newTime }));
      setAudioProgress(prev => ({ ...prev, [callId]: clickPercent * 100 }));
    }
  };

  // Forward audio for 10 s
  const handleFastForward = (callId) => {
    const audio = document.getElementById(`audio-${callId}`);
    if (audio && audioDuration[callId]) {
      const newTime = Math.min(audio.currentTime + 10, audioDuration[callId]);
      audio.currentTime = newTime;
      setCurrentTime(prev => ({ ...prev, [callId]: newTime }));
      setAudioProgress(prev => ({ ...prev, [callId]: (newTime / audioDuration[callId]) * 100 }));
    }
  };

  // Backward audio for 10 s
  const handleRewind = (callId) => {
    const audio = document.getElementById(`audio-${callId}`);
    if (audio) {
      const newTime = Math.max(audio.currentTime - 10, 0);
      audio.currentTime = newTime;
      setCurrentTime(prev => ({ ...prev, [callId]: newTime }));
      setAudioProgress(prev => ({ ...prev, [callId]: (newTime / audioDuration[callId]) * 100 }));
    }
  };

  // 改变播放速度
  const handleSpeedChange = (callId, speed) => {
    const audio = document.getElementById(`audio-${callId}`);
    if (audio) {
      audio.playbackRate = speed;
      setPlaybackSpeed(prev => ({ ...prev, [callId]: speed }));
    }
  };

  // Download Recording
  const handleDownload = async (callId, recordingUrl, clientName, callDateTime) => {
    try {
      const audioUrl = `${process.env.REACT_APP_API_URL}/api/call-recordings/${recordingUrl}`;
      const response = await fetch(audioUrl, {
        headers: {
          'ngrok-skip-browser-warning': 'true'
        }
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${clientName}_${callDateTime}_recording.wav`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      } else {
        console.error('Download failed:', response.status);
        alert('Download failed, please try again later');
      }
    } catch (error) {
      console.error('Download error:', error);
      alert('Download failed, please try again later');
    }
  };

  // Filter
  const filteredCalls = getFilteredCalls();
  const sortedCalls = [...filteredCalls].sort((a, b) => {
    const dateA = new Date(a.callDateTime);
    const dateB = new Date(b.callDateTime);
    return sortOrder === 'desc' ? dateB - dateA : dateA - dateB;
  });

  const indexOfLastCall = currentPage * callsPerPage;
  const indexOfFirstCall = indexOfLastCall - callsPerPage;
  const currentCalls = sortedCalls.slice(indexOfFirstCall, indexOfLastCall);
  const totalPages = Math.ceil(sortedCalls.length / callsPerPage);

  const handlePageChange = (page) => {
    setCurrentPage(page);
    setExpandedCallId(null);
  };

  const handleSortToggle = () => {
    setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc');
    setExpandedCallId(null);
  };

  // convert utc time to local time
  const toLocalTime = (utcDateString) => {
    const date = new Date(utcDateString);
    return new Intl.DateTimeFormat('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
      timeZoneName: 'short'
    }).format(date);
  };

  // generate pagination buttons
  const getPaginationItems = () => {
    const items = [];
    const startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    // adjust startPage if endPage is close to totalPages
    const adjustedStartPage = Math.max(1, endPage - maxVisiblePages + 1);

    // previous page button
    items.push(
      <button
        key="prev"
        onClick={() => handlePageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className="pagination-nav"
      >
        &lt; Prev
      </button>
    );

    // first page
    items.push(
      <button
        key={1}
        onClick={() => handlePageChange(1)}
        className={currentPage === 1 ? 'active' : ''}
      >
        1
      </button>
    );

    // ellipsis (if startPage > 2)
    if (adjustedStartPage > 2) {
      items.push(<span key="ellipsis-start" className="pagination-ellipsis">...</span>);
    }

    // middle page numbers
    for (let page = adjustedStartPage; page <= endPage; page++) {
      if (page !== 1 && page !== totalPages) {
        items.push(
          <button
            key={page}
            onClick={() => handlePageChange(page)}
            className={currentPage === page ? 'active' : ''}
          >
            {page}
          </button>
        );
      }
    }

    // ellipsis (if endPage < totalPages - 1)
    if (endPage < totalPages - 1) {
      items.push(<span key="ellipsis-end" className="pagination-ellipsis">...</span>);
    }

    // last page (if totalPages > 1)
    if (totalPages > 1) {
      items.push(
        <button
          key={totalPages}
          onClick={() => handlePageChange(totalPages)}
          className={currentPage === totalPages ? 'active' : ''}
        >
          {totalPages}
        </button>
      );
    }

    // next page button
    items.push(
      <button
        key="next"
        onClick={() => handlePageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className="pagination-nav"
      >
        Next &gt;
      </button>
    );

    return items;
  };

  return (
    <div className="call-list">
      {/* Search Bar */}
      <div className="search-section">
        <h3>Search Call History</h3>
        <div className="search-filters">
          <div className="filter-group">
            <label htmlFor="clientName">Client Name:</label>
            <input
              type="text"
              id="clientName"
              value={searchFilters.clientName}
              onChange={(e) => handleSearchChange('clientName', e.target.value)}
              placeholder="Enter Client Name..."
              className="search-input"
            />
          </div>
          
          <div className="filter-group">
            <label htmlFor="clientNumber">Client Number:</label>
            <input
              type="text"
              id="clientNumber"
              value={searchFilters.clientNumber}
              onChange={(e) => handleSearchChange('clientNumber', e.target.value)}
              placeholder="Enter Client Number..."
              className="search-input"
            />
          </div>
          
          <div className="filter-group">
            <label htmlFor="simzCallId">SimZ Call ID:</label>
            <input
              type="text"
              id="simzCallId"
              value={searchFilters.simzCallId}
              onChange={(e) => handleSearchChange('simzCallId', e.target.value)}
              placeholder="Enter SimZ Call ID..."
              className="search-input"
            />
          </div>
          
          <div className="filter-group">
            <label htmlFor="startDate">Start Date:</label>
            <input
              type="date"
              id="startDate"
              value={searchFilters.startDate}
              onChange={(e) => handleSearchChange('startDate', e.target.value)}
              className="date-input"
            />
          </div>
          
          <div className="filter-group">
            <label htmlFor="endDate">End Date:</label>
            <input
              type="date"
              id="endDate"
              value={searchFilters.endDate}
              onChange={(e) => handleSearchChange('endDate', e.target.value)}
              className="date-input"
            />
          </div>
          
          <div className="filter-actions">
            <button onClick={handleClearSearch} className="clear-button">
              Clear Filters
            </button>
          </div>
        </div>
        
        <div className="search-results-info">
          Showing {currentCalls.length} records, {sortedCalls.length} total matches
        </div>
      </div>

      <div className="controls">
        <button onClick={handleSortToggle}>
          Sort by Date ({sortOrder === 'desc' ? 'Newest' : 'Oldest'})
        </button>
      </div>
      <table className="call-table">
        <thead>
          <tr>
            <th>Date & Time</th>
            <th>Client Name</th>
            <th>Client Number</th>
            <th>Duration</th>
            <th>SimZ Call ID</th>
            <th>Agent Name</th>
            <th>Prompt Name</th>
            <th>Human Detected</th>
            <th>Recording</th>
            <th>Transcript</th>
          </tr>
        </thead>
        <tbody>
          {currentCalls.map((call) => (
            <React.Fragment key={call.callId}>
              <tr className="call-item">
                <td>{toLocalTime(call.callDateTime)}</td>
                <td>{call.clientName}</td>
                <td>{call.toNumber || 'N/A'}</td>
                <td>{call.callDuration}s</td>
                <td>{call.callId || 'N/A'}</td>
                <td>{call.agentName || 'N/A'}</td>
                <td>{call.promptName || 'N/A'}</td>
                <td style={{ textAlign: 'center', fontSize: '18px' }}>
                  {call.amdResult === 'machine_start' ? '❌' : '✅'}
                </td>
                <td>
                  {call.recordingUrl ? (
                    <button
                      className="play-button"
                      onClick={() => handlePlayRecording(call.callId, call.recordingUrl)}
                    >
                      {currentlyPlaying === call.callId ? '⏸️' : '▶️'}
                    </button>
                  ) : (
                    'N/A'
                  )}
                </td>
                <td>
                  <span
                    className="toggle-icon"
                    onClick={() => handleToggleTranscript(call.callId)}
                  >
                    {expandedCallId === call.callId ? '▼' : '▶'}
                  </span>
                </td>
              </tr>
              {expandedCallId === call.callId && (
                <tr>
                  <td colSpan="10">
                    <div className="transcript-details">
                      {transcripts[call.callId] ? (
                        <>
                          <div className="transcript-content">
                            {transcripts[call.callId].transcript.map((entry, index) => (
                              <div
                                key={index}
                                className={`transcript-entry ${entry.speaker} ${
                                  entry.Status === 'Interrupted' ? 'interrupted' : ''
                                }`}
                              >
                                <strong style={{ 
                                  color: entry.speaker === 'assistant' ? '#1976d2' : 
                                         entry.speaker === 'user' ? '#2e7d32' : 
                                         entry.speaker === 'admin' ? '#e65100' : '#616161',
                                  textTransform: 'capitalize'
                                }}>{entry.speaker}:</strong> {entry.text}
                                <span className="timestamp">
                                  {' '}
                                  ({new Date(entry.timestamp).toLocaleTimeString()})
                                </span>
                                {entry.Status === 'Interrupted' && (
                                  <span className="interrupted-icon">⚠️</span>
                                )}
                              </div>
                            ))}
                          </div>
                          {call.recordingUrl && (
                            <div className="audio-player-section">
                              <div className="audio-player">
                                <div className="audio-controls">
                                  <button
                                    className="control-button rewind-button"
                                    onClick={() => handleRewind(call.callId)}
                                    title="Backward 10 seconds"
                                  >
                                    ⏪
                                  </button>
                                  <button
                                    className={`play-button ${loadingAudio === call.callId ? 'loading' : ''}`}
                                    onClick={() => handlePlayRecording(call.callId, call.recordingUrl)}
                                    disabled={loadingAudio === call.callId}
                                  >
                                    {loadingAudio === call.callId ? (
                                      <span className="loading-spinner">⏳</span>
                                    ) : currentlyPlaying === call.callId ? (
                                      '⏸️'
                                    ) : (
                                      '▶️'
                                    )}
                                  </button>
                                  <button
                                    className="control-button forward-button"
                                    onClick={() => handleFastForward(call.callId)}
                                    title="Forward 10 seconds"
                                  >
                                    ⏩
                                  </button>
                                  <div className="audio-progress" onClick={(e) => handleProgressClick(call.callId, e)}>
                                    <div 
                                      className="progress-bar"
                                      style={{ width: `${audioProgress[call.callId] || 0}%` }}
                                    />
                                  </div>
                                  <div className="audio-time">
                                    <span className="current-time">
                                      {formatTime(currentTime[call.callId])}
                                    </span>
                                    <span className="time-separator">/</span>
                                    <span className="total-time">
                                      {formatTime(audioDuration[call.callId])}
                                    </span>
                                  </div>
                                  <div className="speed-control">
                                    <select
                                      value={playbackSpeed[call.callId] || 1}
                                      onChange={(e) => handleSpeedChange(call.callId, parseFloat(e.target.value))}
                                      className="speed-select"
                                      title="Play Speed"
                                    >
                                      <option value={0.5}>0.5x</option>
                                      <option value={0.75}>0.75x</option>
                                      <option value={1}>1x</option>
                                      <option value={1.25}>1.25x</option>
                                      <option value={1.5}>1.5x</option>
                                      <option value={2}>2x</option>
                                    </select>
                                  </div>
                                  <button
                                    className="control-button download-button"
                                    onClick={() => handleDownload(call.callId, call.recordingUrl, call.clientName, toLocalTime(call.callDateTime))}
                                    title="Download Recording"
                                  >
                                    📥
                                  </button>
                                </div>
                                <audio
                                  id={`audio-${call.callId}`}
                                  onTimeUpdate={(e) => handleTimeUpdate(call.callId, e)}
                                  onLoadedMetadata={(e) => handleLoadedMetadata(call.callId, e)}
                                  onEnded={() => {
                                    setCurrentlyPlaying(null);
                                    setAudioProgress(prev => ({ ...prev, [call.callId]: 0 }));
                                    setCurrentTime(prev => ({ ...prev, [call.callId]: 0 }));
                                  }}
                                  onError={(e) => {
                                    console.error('Audio playback error:', e);
                                    setCurrentlyPlaying(null);
                                    setLoadingAudio(null);
                                  }}
                                />
                              </div>
                            </div>
                          )}
                        </>
                      ) : (
                        <div className="loading-transcript">Loading transcript...</div>
                      )}
                    </div>
                  </td>
                </tr>
              )}
            </React.Fragment>
          ))}
        </tbody>
      </table>
      <div className="pagination">
        {getPaginationItems()}
      </div>
    </div>
  );
};

export default CallHistory;