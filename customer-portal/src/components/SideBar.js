import React, { useState } from 'react';
import { NavLink } from 'react-router-dom';
import '../styles/components/Sidebar.css';

const Sidebar = () => {
  const [isSidebarVisible, setIsSidebarVisible] = useState(true);

  const toggleSidebar = () => {
    setIsSidebarVisible(!isSidebarVisible);
  };

  return (
    <div>
      {/* Toggle Button */}
      <button className="toggle-sidebar-btn" onClick={toggleSidebar}>
        {isSidebarVisible ? '✖' : '☰'}
      </button>

      {/* Sidebar */}
      {isSidebarVisible && (
        <div className="sidebar">
          <h2>Customer Portal</h2>
          <nav>
            <ul>
              <li>
                <NavLink to="/home" activeClassName="active">Home</NavLink>
              </li>
              <li>
                <NavLink to="/agent" activeClassName="active">Agent</NavLink>
              </li>
              <li>
                <NavLink to="/prompt" activeClassName="active">Prompt</NavLink>
              </li>
              <li>
                <NavLink to="/call-history" activeClassName="active">Call History</NavLink>
              </li>
              <li>
                <NavLink to="/account-setting" activeClassName="active">Account Setting</NavLink>
              </li>
            </ul>
          </nav>
        </div>
      )}
    </div>
  );
};

export default Sidebar;