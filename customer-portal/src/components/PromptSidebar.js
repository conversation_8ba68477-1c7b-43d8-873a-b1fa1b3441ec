import React, { useState, useEffect } from 'react';
import { fetchPrompts, fetchPromptContent, savePrompt, fetchPromptVersions, fetchPromptContentByMongoId } from '../services/promptService';
import { useAuth } from '../hooks/useAuth';
import '../styles/components/promptSidebar.css';

const PromptSidebar = ({ onPromptSelect }) => {
  const { token, checkTokenExpiration } = useAuth();
  const [prompts, setPrompts] = useState([]);
  const [newPromptName, setNewPromptName] = useState('');
  const [newPromptContent, setNewPromptContent] = useState('');
  const [selectedProvider, setSelectedProvider] = useState('Grok');
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedPrompt, setSelectedPrompt] = useState(null);
  const [versions, setVersions] = useState([]);
  const [selectedVersion, setSelectedVersion] = useState('');
  const [editingContent, setEditingContent] = useState('');
  const [loading, setLoading] = useState(true);
  const [saveAsNewVersion, setSaveAsNewVersion] = useState(false);

  useEffect(() => {
    if (checkTokenExpiration()) {
      return;
    }
    
    if (token) {
      setLoading(true);
      fetchPrompts()
        .then((data) => {
          setPrompts(data);
          setLoading(false);
        })
        .catch((error) => {
          console.error('Failed to fetch prompts:', error);
          setLoading(false);
        });
    } else {
      setLoading(false);
    }
  }, [token, checkTokenExpiration]);

  const handlePromptClick = async (prompt) => {
    if (checkTokenExpiration()) return;
    
    setSelectedPrompt(prompt);
    setModalOpen(true);
    try {
      const versionData = await fetchPromptVersions(prompt.Prompt_ID);
      setVersions(versionData);
      setSelectedVersion(versionData[0]?.Version || '');
      const content = await fetchPromptContent(prompt.Prompt_ID);
      setEditingContent(content);
    } catch (error) {
      console.error('Failed to fetch prompt versions:', error);
    }
  };

  const handleVersionChange = async (e) => {
    if (checkTokenExpiration()) return;
    
    const version = e.target.value;
    setSelectedVersion(version);
    const versionData = versions.find(v => v.Version === version);
    if (versionData) {
      const mongoPrompt = await fetchPromptContentByMongoId(versionData.mongo_prompt_id);
      setEditingContent(mongoPrompt.content);
    }
  };

  const handleSavePrompt = () => {
    if (checkTokenExpiration()) return;
    
    if (!newPromptName || !newPromptContent) {
      alert('Please provide a name and content for the new prompt.');
      return;
    }
    const newPrompt = {
      name: newPromptName,
      provider: selectedProvider,
      content: newPromptContent,
      version: '1',
    };
    savePrompt(newPrompt)
      .then((savedPrompt) => {
        setPrompts([...prompts, { 
          Prompt_ID: savedPrompt.promptId, 
          Prompt_Name: savedPrompt.name, 
          Versions: [savedPrompt.versionId] 
        }]);
        setNewPromptName('');
        setNewPromptContent('');
      })
      .catch((error) => console.error('Failed to save prompt:', error));
  };

  const handleSaveEdit = () => {
    if (checkTokenExpiration()) return;
    
    if (!editingContent) {
      alert('Prompt content cannot be empty.');
      return;
    }
    const updatedPrompt = {
      promptId: selectedPrompt.Prompt_ID,
      name: selectedPrompt.Prompt_Name,
      content: editingContent,
      provider: selectedProvider,
      version: saveAsNewVersion ? (parseInt(versions[0]?.Version || 0) + 1).toString() : selectedVersion,
    };
    savePrompt(updatedPrompt)
      .then((savedPrompt) => {
        setPrompts(prompts.map((p) =>
          p.Prompt_ID === savedPrompt.promptId
            ? { ...p, Prompt_Name: savedPrompt.name }
            : p
        ));
        setVersions(saveAsNewVersion ? [...versions, { Version_ID: savedPrompt.versionId, Version: savedPrompt.version, mongo_prompt_id: savedPrompt.mongoPromptId }] : versions);
        setModalOpen(false);
      })
      .catch((error) => console.error('Failed to save prompt:', error));
  };

  const handleTestPrompt = () => {
    onPromptSelect({ 
      Prompt_ID: selectedPrompt.Prompt_ID, 
      name: selectedPrompt.Prompt_Name, 
      content: editingContent, // 传递用户编辑的内容
      Version: selectedVersion,
      provider: selectedProvider // 传递选择的 provider
    });
    setModalOpen(false);
  };

  return (
    <div className="prompt-sidebar">
      <h3>Prompts</h3>
      <input
        type="text"
        placeholder="New Prompt Name"
        value={newPromptName}
        onChange={(e) => setNewPromptName(e.target.value)}
      />
      <textarea
        placeholder="New Prompt Content"
        value={newPromptContent}
        onChange={(e) => setNewPromptContent(e.target.value)}
      />
      <select
        value={selectedProvider}
        onChange={(e) => setSelectedProvider(e.target.value)}
      >
        <option value="Grok">Grok</option>
        <option value="OpenAI">OpenAI</option>
        <option value="Meta">Meta</option>
      </select>
      <button onClick={handleSavePrompt}>Create New Prompt</button>

      {loading ? (
        <p>Loading prompts...</p>
      ) : (
        <ul className="prompt-list">
          {prompts.map((prompt) => (
            <li
              key={prompt.Prompt_ID}
              onClick={() => handlePromptClick(prompt)}
            >
              {prompt.Prompt_Name}
            </li>
          ))}
        </ul>
      )}

      {modalOpen && selectedPrompt && (
        <div className="modal">
          <div className="modal-content">
            <h4>{selectedPrompt.Prompt_Name}</h4>
            <select
              value={selectedVersion}
              onChange={handleVersionChange}
            >
              {versions.map((v) => (
                <option key={v.Version_ID} value={v.Version}>{v.Version}</option>
              ))}
            </select>
            <textarea
              value={editingContent}
              onChange={(e) => setEditingContent(e.target.value)}
            />
            <label>
              <input
                type="checkbox"
                checked={saveAsNewVersion}
                onChange={(e) => setSaveAsNewVersion(e.target.checked)}
              />
              Save as new version
            </label>
            <button onClick={handleSaveEdit}>Save</button>
            <button onClick={handleTestPrompt}>Test this Prompt</button>
            <button onClick={() => setModalOpen(false)}>Close</button>
          </div>
        </div>
      )}
    </div>
  );
};

export default PromptSidebar;