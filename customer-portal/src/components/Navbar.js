import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth'; // 使用 useAuth 获取 logout
import './Navbar.css';

const NavBar = () => {
  const { token, logout } = useAuth(); // 从 AuthContext 获取 token 和 logout

  const handleLogout = () => {
    logout(); // 调用 AuthContext 的 logout
    window.location.href = '/login'; // 确保重定向（可选，视需求）
  };

  return (
    <nav>
      <Link to="/">Home</Link>
      {token && ( // 如果有 token，显示受保护链接和 Logout 按钮
        <>
          <Link to="/prompt">Prompt</Link>
          <Link to="/call-history">Call History</Link>
          <button onClick={handleLogout}>Logout</button>
        </>
      )}
      {!token && <Link to="/login">Login</Link>} {/* 未登录时显示 Login */}
    </nav>
  );
};

export default NavBar;