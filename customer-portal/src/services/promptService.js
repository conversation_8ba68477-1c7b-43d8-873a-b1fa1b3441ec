import axiosInstance from '../utils/axiosConfig';

export const fetchPrompts = async () => {
  const response = await axiosInstance.get(`/api/prompts`);
  return response.data;
};

export const fetchPromptContent = async (promptId) => {
  const response = await axiosInstance.get(`/api/get-prompt`, {
    params: { promptId },
  });
  return response.data;
};

export const savePrompt = async (promptData) => {
  const response = await axiosInstance.post(`/api/save-prompt`, promptData, {
    headers: {
      'Content-Type': 'application/json',
    },
  });
  return response.data;
};

export const chatWithPrompt = async (promptId, message, provider, content) => {
  const response = await axiosInstance.post(
    `/api/grok-chat`,
    { promptId, message, provider, content },
    {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
  return response.data;
};

export const clearChatHistory = async () => {
  const response = await axiosInstance.post(
    `/api/clear-chat`,
    {},
    {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
  return response.data;
};

export const fetchPromptVersions = async (promptId) => {
  const response = await axiosInstance.get(`/api/prompt-versions`, {
    params: { promptId },
  });
  return response.data;
};

export const fetchPromptContentByMongoId = async (mongoPromptId) => {
  const response = await axiosInstance.get(`/api/prompt-by-mongo`, {
    params: { mongoPromptId },
  });
  return response.data;
};

export default {
  fetchPrompts,
  fetchPromptContent,
  savePrompt,
  chatWithPrompt,
  clearChatHistory,
  fetchPromptVersions,
  fetchPromptContentByMongoId,
};