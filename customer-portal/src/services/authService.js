import axiosInstance from '../utils/axiosConfig';

const API_URL = process.env.REACT_APP_API_URL || 'http://10.0.1.170:30000';
const PORTAL_URL = process.env.REACT_APP_PORTAL_URL || 'http://10.0.1.170:30000';

export const login = async (username, password) => {
  console.log(API_URL);
  // 登录请求不使用拦截器，因为此时还没有token
  const response = await axiosInstance.post(`/api/login`, { username, password });
  return response.data.token;
};

export const logout = () => {
  localStorage.removeItem('token');
  // 登出后重定向到指定登录页面
  window.location.href = PORTAL_URL;
};

export const register = async (username, password, email) => {
  const response = await axiosInstance.post(`/api/register`, { username, password, email });
  return response.data;
};