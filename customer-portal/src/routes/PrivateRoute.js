import React, { useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { isTokenExpired, handleTokenExpiration } from '../utils/tokenUtils';

const PrivateRoute = ({ children }) => {
  const { token } = useAuth();
  
  // 在路由导航时检查token是否过期
  useEffect(() => {
    if (token && isTokenExpired(token)) {
      handleTokenExpiration();
    }
  }, [token]);
  
  // 如果没有token，重定向到登录页
  if (!token) {
    return <Navigate to="/" />;
  }
  
  // 如果token过期，重定向到外部登录页
  if (isTokenExpired(token)) {
    handleTokenExpiration();
    return null; // 重定向已经在handleTokenExpiration中处理
  }
  
  return children;
};

export default PrivateRoute;