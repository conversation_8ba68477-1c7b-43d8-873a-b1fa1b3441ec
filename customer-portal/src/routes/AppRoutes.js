import React from 'react';
import { Routes, Route } from 'react-router-dom';
import LoginPage from '../pages/LoginPage';
import RegisterPage from '../pages/RegisterPage';
import HomePage from '../pages/HomePage';
import AgentPage from '../pages/AgentPage';
import PromptPage from '../pages/PromptPage';
import CallHistoryPage from '../pages/CallHistoryPage';
import AccountSettingPage from '../pages/AccountSettingPage';
import PrivateRoute from './PrivateRoute';
import ProtectedLayout from '../components/ProtectedLayout';

const AppRoutes = () => (
  <Routes>
    <Route path="/" element={<LoginPage />} />
    <Route path="/register" element={<RegisterPage />} />
    <Route
      path="/"
      element={<PrivateRoute><ProtectedLayout /></PrivateRoute>}
    >
      <Route path="home" element={<HomePage />} />
      <Route path="agent" element={<AgentPage />} />
      <Route path="prompt" element={<PromptPage />} />
      <Route path="call-history" element={<CallHistoryPage />} />
      <Route path="account-setting" element={<AccountSettingPage />} />
    </Route>
  </Routes>
);

export default AppRoutes;