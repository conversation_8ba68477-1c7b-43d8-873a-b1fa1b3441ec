import { useState, useEffect } from 'react';
import { fetchCallHistory } from '../services/callService';
import { useAuth } from './useAuth';

export const useFetchCalls = () => {
  const { token, checkTokenExpiration } = useAuth();
  const [calls, setCalls] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const getCalls = async () => {
      if (!token) return;
      
      // Check token expireation
      if (checkTokenExpiration()) return;
      
      setLoading(true);
      try {
        const data = await fetchCallHistory();
        setCalls(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };
    getCalls();
  }, [token, checkTokenExpiration]);

  return { calls, loading, error };
};