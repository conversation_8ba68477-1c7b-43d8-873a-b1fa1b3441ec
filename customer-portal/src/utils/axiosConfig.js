import axios from 'axios';
import { isTokenExpired, handleTokenExpiration } from './tokenUtils';

const API_URL = process.env.REACT_APP_API_URL || 'http://**********:30000';

// 创建axios实例
const axiosInstance = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'ngrok-skip-browser-warning': 'true'
  }
});

// 请求拦截器
axiosInstance.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('token');
    
    // 检查token是否过期
    if (token && isTokenExpired(token)) {
      // 如果过期，处理过期逻辑
      handleTokenExpiration();
      // 中断请求
      return Promise.reject(new Error('Token已过期，请重新登录'));
    }
    
    // 如果token有效，添加到请求头
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
axiosInstance.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    // 检查是否是401错误(未授权)
    if (error.response && error.response.status === 401) {
      // 处理token过期逻辑
      handleTokenExpiration();
    }
    return Promise.reject(error);
  }
);

export default axiosInstance; 