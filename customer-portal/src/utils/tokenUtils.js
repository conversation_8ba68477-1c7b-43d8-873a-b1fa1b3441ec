/**
 * Token工具函数 
 */

const PORTAL_URL = process.env.REACT_APP_PORTAL_URL || 'http://10.0.1.170:30000';

/**
 * 解析JWT token
 * @param {string} token - JWT token
 * @returns {object|null} - 解析后的payload或null
 */
export const parseJwt = (token) => {
  try {
    // JWT格式为：header.payload.signature
    // 我们需要提取payload部分并解码
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    return null;
  }
};

/**
 * 检查JWT token是否过期
 * @param {string} token - JWT token
 * @returns {boolean} - 是否过期
 */
export const isTokenExpired = (token) => {
  if (!token) return true;
  
  const decodedToken = parseJwt(token);
  if (!decodedToken) return true;
  
  // 检查exp (expiration time)字段
  // JWT中的exp是Unix时间戳(秒)
  const currentTime = Math.floor(Date.now() / 1000);
  return decodedToken.exp && decodedToken.exp < currentTime;
};

/**
 * 处理token过期
 * @returns {void}
 */
export const handleTokenExpiration = () => {
  localStorage.removeItem('token');
  // 重定向到外部登录页面
  window.location.href = PORTAL_URL;
}; 