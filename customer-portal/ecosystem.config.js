module.exports = {
  apps: [{
    name: 'customer-portal',
    script: 'npm',
    args: 'start',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    error_file: process.env.PM2_ERROR_LOG || './logs/customer-portal-err.log',
    out_file: process.env.PM2_OUTPUT_LOG || './logs/customer-portal-out.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss',
    merge_logs: true,
    log_type: 'json',
    time: true,
    log_rotate: true,
    max_size: '10M',
    max_files: '10'
  }]
}; 