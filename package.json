{"name": "simz-platform", "version": "1.0.0", "description": "SimZ Platform - Monorepo for call processing and customer portal services", "private": true, "workspaces": ["services/*", "shared/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "test": "turbo run test", "test:unit": "turbo run test:unit", "test:integration": "turbo run test:integration", "test:coverage": "turbo run test:coverage", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "format": "turbo run format", "format:check": "turbo run format:check", "clean": "turbo run clean", "start": "turbo run start", "start:core": "turbo run start --filter=core-call-service", "start:portal": "turbo run start --filter=customer-portal", "build:core": "turbo run build --filter=core-call-service", "build:portal": "turbo run build --filter=customer-portal", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f"}, "devDependencies": {"turbo": "^1.13.4", "eslint": "^8.56.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "prettier": "^3.5.3", "typescript": "^5.3.3", "@types/node": "^20.10.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "author": "<PERSON>", "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/your-org/simz-platform.git"}, "keywords": ["monorepo", "microservices", "call-processing", "customer-portal", "turbo"]}