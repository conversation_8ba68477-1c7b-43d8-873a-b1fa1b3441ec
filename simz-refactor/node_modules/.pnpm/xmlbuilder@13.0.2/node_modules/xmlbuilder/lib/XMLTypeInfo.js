// Generated by CoffeeScript 2.4.1
(function() {
  var Derivation, XMLTypeInfo;

  Derivation = require('./Derivation');

  // Represents a type referenced from element or attribute nodes.
  module.exports = XMLTypeInfo = class XMLTypeInfo {
    // Initializes a new instance of `XMLTypeInfo`

    constructor(typeName, typeNamespace) {
      this.typeName = typeName;
      this.typeNamespace = typeNamespace;
    }

    // DOM level 3 functions to be implemented later
    isDerivedFrom(typeNamespaceArg, typeNameArg, derivationMethod) {
      throw new Error("This DOM method is not implemented.");
    }

  };

}).call(this);
