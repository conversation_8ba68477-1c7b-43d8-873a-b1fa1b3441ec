#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/bin/node_modules:/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/node_modules:/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/js-yaml@4.1.0/node_modules:/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/bin/node_modules:/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/node_modules:/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/js-yaml@4.1.0/node_modules:/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../js-yaml/bin/js-yaml.js" "$@"
else
  exec node  "$basedir/../js-yaml/bin/js-yaml.js" "$@"
fi
