#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/protobufjs@6.11.4/node_modules/protobufjs/bin/node_modules:/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/protobufjs@6.11.4/node_modules/protobufjs/node_modules:/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/protobufjs@6.11.4/node_modules:/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/protobufjs@6.11.4/node_modules/protobufjs/bin/node_modules:/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/protobufjs@6.11.4/node_modules/protobufjs/node_modules:/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/protobufjs@6.11.4/node_modules:/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../protobufjs/bin/pbjs" "$@"
else
  exec node  "$basedir/../protobufjs/bin/pbjs" "$@"
fi
