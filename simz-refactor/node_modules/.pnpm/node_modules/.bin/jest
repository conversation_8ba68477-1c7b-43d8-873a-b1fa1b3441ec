#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/jest-cli@29.7.0_@types+node@20.19.9/node_modules/jest-cli/bin/node_modules:/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/jest-cli@29.7.0_@types+node@20.19.9/node_modules/jest-cli/node_modules:/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/jest-cli@29.7.0_@types+node@20.19.9/node_modules:/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/jest-cli@29.7.0_@types+node@20.19.9/node_modules/jest-cli/bin/node_modules:/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/jest-cli@29.7.0_@types+node@20.19.9/node_modules/jest-cli/node_modules:/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/jest-cli@29.7.0_@types+node@20.19.9/node_modules:/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../jest-cli/bin/jest.js" "$@"
else
  exec node  "$basedir/../jest-cli/bin/jest.js" "$@"
fi
