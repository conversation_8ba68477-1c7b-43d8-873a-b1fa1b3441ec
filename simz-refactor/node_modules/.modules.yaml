hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@azure/abort-controller@1.1.0':
    '@azure/abort-controller': private
  '@azure/ai-text-analytics@5.1.0':
    '@azure/ai-text-analytics': private
  '@azure/core-asynciterator-polyfill@1.0.2':
    '@azure/core-asynciterator-polyfill': private
  '@azure/core-auth@1.10.0':
    '@azure/core-auth': private
  '@azure/core-client@1.10.0':
    '@azure/core-client': private
  '@azure/core-lro@2.7.2':
    '@azure/core-lro': private
  '@azure/core-paging@1.6.2':
    '@azure/core-paging': private
  '@azure/core-rest-pipeline@1.22.0':
    '@azure/core-rest-pipeline': private
  '@azure/core-tracing@1.0.0-preview.12':
    '@azure/core-tracing': private
  '@azure/core-util@1.13.0':
    '@azure/core-util': private
  '@azure/identity@4.10.2':
    '@azure/identity': private
  '@azure/logger@1.3.0':
    '@azure/logger': private
  '@azure/msal-browser@4.16.0':
    '@azure/msal-browser': private
  '@azure/msal-common@15.9.0':
    '@azure/msal-common': private
  '@azure/msal-node@3.6.4':
    '@azure/msal-node': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.0':
    '@babel/core': private
  '@babel/generator@7.28.0':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.28.2':
    '@babel/helpers': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-async-generators': private
  '@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-bigint': private
  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.28.0)':
    '@babel/plugin-syntax-class-properties': private
  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.28.0)':
    '@babel/plugin-syntax-class-static-block': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-meta': private
  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-json-strings': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-logical-assignment-operators': private
  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-numeric-separator': private
  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-object-rest-spread': private
  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-optional-catch-binding': private
  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-optional-chaining': private
  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.28.0)':
    '@babel/plugin-syntax-private-property-in-object': private
  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.28.0)':
    '@babel/plugin-syntax-top-level-await': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-typescript': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.0':
    '@babel/traverse': private
  '@babel/types@7.28.2':
    '@babel/types': private
  '@bcoe/v8-coverage@0.2.3':
    '@bcoe/v8-coverage': private
  '@deepgram/captions@1.2.0':
    '@deepgram/captions': private
  '@deepgram/sdk@3.13.0(bufferutil@4.0.9)(utf-8-validate@5.0.10)':
    '@deepgram/sdk': private
  '@derhuerst/http-basic@8.2.4':
    '@derhuerst/http-basic': private
  '@eslint-community/eslint-utils@4.7.0(eslint@8.57.1)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': private
  '@eslint/js@8.57.1':
    '@eslint/js': private
  '@ffmpeg-installer/ffmpeg@1.1.0':
    '@ffmpeg-installer/ffmpeg': private
  '@ffmpeg-installer/linux-x64@4.1.0':
    '@ffmpeg-installer/linux-x64': private
  '@google-cloud/common@2.4.0':
    '@google-cloud/common': private
  '@google-cloud/projectify@1.0.4':
    '@google-cloud/projectify': private
  '@google-cloud/promisify@1.0.4':
    '@google-cloud/promisify': private
  '@google-cloud/speech@3.6.0':
    '@google-cloud/speech': private
  '@google/generative-ai@0.17.2':
    '@google/generative-ai': private
  '@grpc/grpc-js@1.3.8':
    '@grpc/grpc-js': private
  '@grpc/proto-loader@0.5.6':
    '@grpc/proto-loader': private
  '@humanwhocodes/config-array@0.13.0':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@ioredis/commands@1.2.0':
    '@ioredis/commands': private
  '@istanbuljs/load-nyc-config@1.1.0':
    '@istanbuljs/load-nyc-config': private
  '@istanbuljs/schema@0.1.3':
    '@istanbuljs/schema': private
  '@jest/console@29.7.0':
    '@jest/console': private
  '@jest/core@29.7.0':
    '@jest/core': private
  '@jest/environment@29.7.0':
    '@jest/environment': private
  '@jest/expect-utils@29.7.0':
    '@jest/expect-utils': private
  '@jest/expect@29.7.0':
    '@jest/expect': private
  '@jest/fake-timers@29.7.0':
    '@jest/fake-timers': private
  '@jest/globals@29.7.0':
    '@jest/globals': private
  '@jest/reporters@29.7.0':
    '@jest/reporters': private
  '@jest/schemas@29.6.3':
    '@jest/schemas': private
  '@jest/source-map@29.6.3':
    '@jest/source-map': private
  '@jest/test-result@29.7.0':
    '@jest/test-result': private
  '@jest/test-sequencer@29.7.0':
    '@jest/test-sequencer': private
  '@jest/transform@29.7.0':
    '@jest/transform': private
  '@jest/types@29.6.3':
    '@jest/types': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@mapbox/node-pre-gyp@1.0.11':
    '@mapbox/node-pre-gyp': private
  '@mongodb-js/saslprep@1.3.0':
    '@mongodb-js/saslprep': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@opentelemetry/api@1.9.0':
    '@opentelemetry/api': private
  '@pkgr/core@0.2.9':
    '@pkgr/core': private
  '@pm2/agent@2.0.4(bufferutil@4.0.9)(utf-8-validate@5.0.10)':
    '@pm2/agent': private
  '@pm2/io@6.0.1':
    '@pm2/io': private
  '@pm2/js-api@0.8.0(bufferutil@4.0.9)(utf-8-validate@5.0.10)':
    '@pm2/js-api': private
  '@pm2/pm2-version-check@1.0.4':
    '@pm2/pm2-version-check': private
  '@protobufjs/aspromise@1.1.2':
    '@protobufjs/aspromise': private
  '@protobufjs/base64@1.1.2':
    '@protobufjs/base64': private
  '@protobufjs/codegen@2.0.4':
    '@protobufjs/codegen': private
  '@protobufjs/eventemitter@1.1.0':
    '@protobufjs/eventemitter': private
  '@protobufjs/fetch@1.1.0':
    '@protobufjs/fetch': private
  '@protobufjs/float@1.0.2':
    '@protobufjs/float': private
  '@protobufjs/inquire@1.1.0':
    '@protobufjs/inquire': private
  '@protobufjs/path@1.1.2':
    '@protobufjs/path': private
  '@protobufjs/pool@1.1.0':
    '@protobufjs/pool': private
  '@protobufjs/utf8@1.1.0':
    '@protobufjs/utf8': private
  '@sendgrid/client@8.1.5':
    '@sendgrid/client': private
  '@sendgrid/helpers@8.0.0':
    '@sendgrid/helpers': private
  '@sendgrid/mail@8.1.5':
    '@sendgrid/mail': private
  '@sinclair/typebox@0.27.8':
    '@sinclair/typebox': private
  '@sinonjs/commons@3.0.1':
    '@sinonjs/commons': private
  '@sinonjs/fake-timers@10.3.0':
    '@sinonjs/fake-timers': private
  '@socket.io/component-emitter@3.1.2':
    '@socket.io/component-emitter': private
  '@tootallnate/once@1.1.2':
    '@tootallnate/once': private
  '@tootallnate/quickjs-emscripten@0.23.0':
    '@tootallnate/quickjs-emscripten': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.20.7':
    '@types/babel__traverse': private
  '@types/body-parser@1.19.6':
    '@types/body-parser': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/cors@2.8.19':
    '@types/cors': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/duplexify@3.6.4':
    '@types/duplexify': private
  '@types/express-serve-static-core@4.19.6':
    '@types/express-serve-static-core': private
  '@types/express@4.17.23':
    '@types/express': private
  '@types/fs-extra@8.1.5':
    '@types/fs-extra': private
  '@types/graceful-fs@4.1.9':
    '@types/graceful-fs': private
  '@types/http-errors@2.0.5':
    '@types/http-errors': private
  '@types/istanbul-lib-coverage@2.0.6':
    '@types/istanbul-lib-coverage': private
  '@types/istanbul-lib-report@3.0.3':
    '@types/istanbul-lib-report': private
  '@types/istanbul-reports@3.0.4':
    '@types/istanbul-reports': private
  '@types/lodash@4.17.20':
    '@types/lodash': private
  '@types/long@4.0.2':
    '@types/long': private
  '@types/mime@1.3.5':
    '@types/mime': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/node-fetch@2.6.12':
    '@types/node-fetch': private
  '@types/pumpify@1.4.4':
    '@types/pumpify': private
  '@types/qs@6.14.0':
    '@types/qs': private
  '@types/range-parser@1.2.7':
    '@types/range-parser': private
  '@types/send@0.17.5':
    '@types/send': private
  '@types/serve-static@1.15.8':
    '@types/serve-static': private
  '@types/stack-utils@2.0.3':
    '@types/stack-utils': private
  '@types/validator@13.15.2':
    '@types/validator': private
  '@types/webidl-conversions@7.0.3':
    '@types/webidl-conversions': private
  '@types/webrtc@0.0.37':
    '@types/webrtc': private
  '@types/whatwg-url@11.0.5':
    '@types/whatwg-url': private
  '@types/yargs-parser@21.0.3':
    '@types/yargs-parser': private
  '@types/yargs@17.0.33':
    '@types/yargs': private
  '@typespec/ts-http-runtime@0.3.0':
    '@typespec/ts-http-runtime': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  abbrev@1.1.1:
    abbrev: private
  abort-controller@3.0.0:
    abort-controller: private
  accepts@1.3.8:
    accepts: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  agent-base@6.0.2:
    agent-base: private
  agentkeepalive@4.6.0:
    agentkeepalive: private
  ajv@6.12.6:
    ajv: private
  amp-message@0.1.2:
    amp-message: private
  amp@0.3.1:
    amp: private
  ansi-colors@4.1.3:
    ansi-colors: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  aproba@2.1.0:
    aproba: private
  are-we-there-yet@2.0.0:
    are-we-there-yet: private
  argparse@2.0.1:
    argparse: private
  array-flatten@1.1.1:
    array-flatten: private
  arrify@2.0.1:
    arrify: private
  asap@2.0.6:
    asap: private
  asn1@0.2.6:
    asn1: private
  assert-plus@1.0.0:
    assert-plus: private
  ast-types@0.13.4:
    ast-types: private
  async@0.2.10:
    async: private
  asynckit@0.4.0:
    asynckit: private
  aws-sign2@0.7.0:
    aws-sign2: private
  aws-ssl-profiles@1.1.2:
    aws-ssl-profiles: private
  aws4@1.13.2:
    aws4: private
  axios@1.11.0:
    axios: private
  babel-jest@29.7.0(@babel/core@7.28.0):
    babel-jest: private
  babel-plugin-istanbul@6.1.1:
    babel-plugin-istanbul: private
  babel-plugin-jest-hoist@29.6.3:
    babel-plugin-jest-hoist: private
  babel-preset-current-node-syntax@1.1.0(@babel/core@7.28.0):
    babel-preset-current-node-syntax: private
  babel-preset-jest@29.6.3(@babel/core@7.28.0):
    babel-preset-jest: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  base64-url@2.3.3:
    base64-url: private
  base64id@2.0.0:
    base64id: private
  basic-ftp@5.0.5:
    basic-ftp: private
  bcrypt-pbkdf@1.0.2:
    bcrypt-pbkdf: private
  bcrypt@5.1.1:
    bcrypt: private
  bent@7.3.12:
    bent: private
  bignumber.js@9.3.1:
    bignumber.js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  blessed@0.1.81:
    blessed: private
  bodec@0.1.0:
    bodec: private
  body-parser@1.20.3:
    body-parser: private
  body-parser@2.2.0:
    body-parser: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.1:
    browserslist: private
  bser@2.1.1:
    bser: private
  bson@6.10.4:
    bson: private
  buffer-equal-constant-time@1.0.1:
    buffer-equal-constant-time: private
  buffer-from@1.1.2:
    buffer-from: private
  bufferutil@4.0.9:
    bufferutil: private
  bundle-name@4.1.0:
    bundle-name: private
  bytes@3.1.2:
    bytes: private
  bytesish@0.4.4:
    bytesish: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camelcase@6.3.0:
    camelcase: private
  caniuse-lite@1.0.30001727:
    caniuse-lite: private
  caseless@0.12.0:
    caseless: private
  chalk@4.1.2:
    chalk: private
  char-regex@1.0.2:
    char-regex: private
  charm@0.1.2:
    charm: private
  chokidar@3.6.0:
    chokidar: private
  chownr@2.0.0:
    chownr: private
  ci-info@3.9.0:
    ci-info: private
  cjs-module-lexer@1.4.3:
    cjs-module-lexer: private
  cli-tableau@2.0.1:
    cli-tableau: private
  cliui@8.0.1:
    cliui: private
  cluster-key-slot@1.1.2:
    cluster-key-slot: private
  co-prompt@1.0.0:
    co-prompt: private
  co@4.6.0:
    co: private
  coffeescript@1.12.7:
    coffeescript: private
  collect-v8-coverage@1.0.2:
    collect-v8-coverage: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-support@1.1.3:
    color-support: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@2.20.3:
    commander: private
  compute-cosine-similarity@1.1.0:
    compute-cosine-similarity: private
  compute-dot@1.1.0:
    compute-dot: private
  compute-l2norm@1.1.0:
    compute-l2norm: private
  concat-map@0.0.1:
    concat-map: private
  concat-stream@2.0.0:
    concat-stream: private
  console-control-strings@1.1.0:
    console-control-strings: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie-signature@1.0.6:
    cookie-signature: private
  cookie@0.7.1:
    cookie: private
  core-util-is@1.0.3:
    core-util-is: private
  cors@2.8.5:
    cors: private
  create-jest@29.7.0(@types/node@20.19.9):
    create-jest: private
  cron-parser@4.9.0:
    cron-parser: private
  croner@4.1.97:
    croner: private
  cross-fetch@3.2.0:
    cross-fetch: private
  cross-spawn@7.0.6:
    cross-spawn: private
  csprng@0.1.2:
    csprng: private
  csv-parse@4.16.3:
    csv-parse: private
  csv-stringify@1.1.2:
    csv-stringify: private
  culvert@0.1.2:
    culvert: private
  d@1.0.2:
    d: private
  dashdash@1.14.1:
    dashdash: private
  data-uri-to-buffer@6.0.2:
    data-uri-to-buffer: private
  dayjs@1.11.13:
    dayjs: private
  debug@4.4.1(supports-color@5.5.0):
    debug: private
  dedent@1.6.0:
    dedent: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  default-browser-id@5.0.0:
    default-browser-id: private
  default-browser@5.2.1:
    default-browser: private
  define-lazy-prop@3.0.0:
    define-lazy-prop: private
  degenerator@5.0.1:
    degenerator: private
  delayed-stream@1.0.0:
    delayed-stream: private
  delegates@1.0.0:
    delegates: private
  denque@2.1.0:
    denque: private
  depd@2.0.0:
    depd: private
  destroy@1.2.0:
    destroy: private
  detect-libc@2.0.4:
    detect-libc: private
  detect-newline@3.1.0:
    detect-newline: private
  diff-sequences@29.6.3:
    diff-sequences: private
  doctrine@3.0.0:
    doctrine: private
  dotenv@8.6.0:
    dotenv: private
  dottie@2.0.6:
    dottie: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexify@3.7.1:
    duplexify: private
  ecc-jsbn@0.1.2:
    ecc-jsbn: private
  ecdsa-sig-formatter@1.0.11:
    ecdsa-sig-formatter: private
  ee-first@1.1.1:
    ee-first: private
  electron-to-chromium@1.5.190:
    electron-to-chromium: private
  emittery@0.13.1:
    emittery: private
  emoji-regex@8.0.0:
    emoji-regex: private
  encodeurl@2.0.0:
    encodeurl: private
  end-of-stream@1.4.5:
    end-of-stream: private
  engine.io-parser@5.2.3:
    engine.io-parser: private
  engine.io@6.6.4(bufferutil@4.0.9)(utf-8-validate@5.0.10):
    engine.io: private
  enquirer@2.3.6:
    enquirer: private
  ent@2.2.2:
    ent: private
  env-paths@2.2.1:
    env-paths: private
  error-ex@1.3.2:
    error-ex: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es5-ext@0.10.64:
    es5-ext: private
  es6-iterator@2.0.3:
    es6-iterator: private
  es6-symbol@3.1.4:
    es6-symbol: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  escodegen@2.1.0:
    escodegen: private
  eslint-scope@7.2.2:
    eslint-scope: private
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: private
  esniff@2.0.1:
    esniff: private
  espree@9.6.1:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  event-emitter@0.3.5:
    event-emitter: private
  event-target-shim@5.0.1:
    event-target-shim: private
  eventemitter2@5.0.1:
    eventemitter2: private
  events@3.3.0:
    events: private
  execa@5.1.1:
    execa: private
  exit@0.1.2:
    exit: private
  expect@29.7.0:
    expect: private
  express@4.21.2:
    express: private
  ext@1.7.0:
    ext: private
  extend@3.0.2:
    extend: private
  extrareqp2@1.0.0(debug@4.3.7):
    extrareqp2: private
  extsprintf@1.3.0:
    extsprintf: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-diff@1.3.0:
    fast-diff: private
  fast-json-patch@3.1.1:
    fast-json-patch: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-text-encoding@1.0.6:
    fast-text-encoding: private
  fastq@1.19.1:
    fastq: private
  faye-websocket@0.11.4:
    faye-websocket: private
  faye@1.4.1:
    faye: private
  fb-watchman@2.0.2:
    fb-watchman: private
  fclone@1.0.11:
    fclone: private
  ffmpeg-static@5.2.0:
    ffmpeg-static: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@1.3.1:
    finalhandler: private
  find-up@5.0.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  fluent-ffmpeg@2.1.3:
    fluent-ffmpeg: private
  follow-redirects@1.15.9(debug@4.3.7):
    follow-redirects: private
  forever-agent@0.6.1:
    forever-agent: private
  form-data-encoder@1.7.2:
    form-data-encoder: private
  form-data@4.0.4:
    form-data: private
  formdata-node@4.4.1:
    formdata-node: private
  forwarded@0.2.0:
    forwarded: private
  fresh@0.5.2:
    fresh: private
  fs-minipass@2.1.0:
    fs-minipass: private
  fs.realpath@1.0.0:
    fs.realpath: private
  function-bind@1.1.2:
    function-bind: private
  gauge@3.0.2:
    gauge: private
  gaxios@2.3.4:
    gaxios: private
  gcp-metadata@3.5.0:
    gcp-metadata: private
  generate-function@2.3.1:
    generate-function: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-package-type@0.1.0:
    get-package-type: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@6.0.1:
    get-stream: private
  get-uri@6.0.5:
    get-uri: private
  getpass@0.1.7:
    getpass: private
  git-node-fs@1.0.0(js-git@0.7.8):
    git-node-fs: private
  git-sha1@0.1.2:
    git-sha1: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@7.2.3:
    glob: private
  globals@13.24.0:
    globals: private
  google-auth-library@5.10.1:
    google-auth-library: private
  google-gax@1.15.4:
    google-gax: private
  google-p12-pem@2.0.5:
    google-p12-pem: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  gtoken@4.1.4:
    gtoken: private
  har-schema@2.0.0:
    har-schema: private
  har-validator@5.1.5:
    har-validator: private
  has-flag@3.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  has-unicode@2.0.1:
    has-unicode: private
  hasown@2.0.2:
    hasown: private
  html-escaper@2.0.2:
    html-escaper: private
  http-errors@2.0.0:
    http-errors: private
  http-parser-js@0.5.10:
    http-parser-js: private
  http-proxy-agent@7.0.2:
    http-proxy-agent: private
  http-response-object@3.0.2:
    http-response-object: private
  http-signature@1.2.0:
    http-signature: private
  https-proxy-agent@5.0.1:
    https-proxy-agent: private
  human-signals@2.1.0:
    human-signals: private
  humanize-ms@1.2.1:
    humanize-ms: private
  iconv-lite@0.6.3:
    iconv-lite: private
  ics@3.8.1:
    ics: private
  ignore-by-default@1.0.1:
    ignore-by-default: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  import-local@3.2.0:
    import-local: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflection@1.13.4:
    inflection: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ini@1.3.8:
    ini: private
  ioredis@5.6.1:
    ioredis: private
  ip-address@9.0.5:
    ip-address: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-core-module@2.16.1:
    is-core-module: private
  is-docker@3.0.0:
    is-docker: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-fn@2.1.0:
    is-generator-fn: private
  is-glob@4.0.3:
    is-glob: private
  is-inside-container@1.0.0:
    is-inside-container: private
  is-number@7.0.0:
    is-number: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-property@1.0.2:
    is-property: private
  is-regex@1.2.1:
    is-regex: private
  is-stream-ended@0.1.4:
    is-stream-ended: private
  is-stream@2.0.1:
    is-stream: private
  is-typedarray@1.0.0:
    is-typedarray: private
  is-wsl@1.1.0:
    is-wsl: private
  isarray@1.0.0:
    isarray: private
  isexe@2.0.0:
    isexe: private
  isstream@0.1.2:
    isstream: private
  istanbul-lib-coverage@3.2.2:
    istanbul-lib-coverage: private
  istanbul-lib-instrument@6.0.3:
    istanbul-lib-instrument: private
  istanbul-lib-report@3.0.1:
    istanbul-lib-report: private
  istanbul-lib-source-maps@4.0.1:
    istanbul-lib-source-maps: private
  istanbul-reports@3.1.7:
    istanbul-reports: private
  jest-changed-files@29.7.0:
    jest-changed-files: private
  jest-circus@29.7.0:
    jest-circus: private
  jest-cli@29.7.0(@types/node@20.19.9):
    jest-cli: private
  jest-config@29.7.0(@types/node@20.19.9):
    jest-config: private
  jest-diff@29.7.0:
    jest-diff: private
  jest-docblock@29.7.0:
    jest-docblock: private
  jest-each@29.7.0:
    jest-each: private
  jest-environment-node@29.7.0:
    jest-environment-node: private
  jest-get-type@29.6.3:
    jest-get-type: private
  jest-haste-map@29.7.0:
    jest-haste-map: private
  jest-leak-detector@29.7.0:
    jest-leak-detector: private
  jest-matcher-utils@29.7.0:
    jest-matcher-utils: private
  jest-message-util@29.7.0:
    jest-message-util: private
  jest-mock@29.7.0:
    jest-mock: private
  jest-pnp-resolver@1.2.3(jest-resolve@29.7.0):
    jest-pnp-resolver: private
  jest-regex-util@29.6.3:
    jest-regex-util: private
  jest-resolve-dependencies@29.7.0:
    jest-resolve-dependencies: private
  jest-resolve@29.7.0:
    jest-resolve: private
  jest-runner@29.7.0:
    jest-runner: private
  jest-runtime@29.7.0:
    jest-runtime: private
  jest-snapshot@29.7.0:
    jest-snapshot: private
  jest-util@29.7.0:
    jest-util: private
  jest-validate@29.7.0:
    jest-validate: private
  jest-watcher@29.7.0:
    jest-watcher: private
  jest-worker@29.7.0:
    jest-worker: private
  js-git@0.7.8:
    js-git: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsbn@1.1.0:
    jsbn: private
  jsesc@3.1.0:
    jsesc: private
  jsforce@1.11.1:
    jsforce: private
  json-bigint@0.3.1:
    json-bigint: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-schema@0.4.0:
    json-schema: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json-stringify-safe@5.0.1:
    json-stringify-safe: private
  json5@2.2.3:
    json5: private
  jsonwebtoken@9.0.2:
    jsonwebtoken: private
  jsprim@1.4.2:
    jsprim: private
  jwa@1.4.2:
    jwa: private
  jws@3.2.2:
    jws: private
  kareem@2.6.3:
    kareem: private
  keypress@0.2.1:
    keypress: private
  keyv@4.5.4:
    keyv: private
  kleur@3.0.3:
    kleur: private
  lazy@1.0.11:
    lazy: private
  leven@3.1.0:
    leven: private
  levn@0.4.1:
    levn: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  locate-path@6.0.0:
    locate-path: private
  lodash.at@4.6.0:
    lodash.at: private
  lodash.camelcase@4.3.0:
    lodash.camelcase: private
  lodash.defaults@4.2.0:
    lodash.defaults: private
  lodash.get@4.4.2:
    lodash.get: private
  lodash.has@4.5.2:
    lodash.has: private
  lodash.includes@4.3.0:
    lodash.includes: private
  lodash.isarguments@3.1.0:
    lodash.isarguments: private
  lodash.isboolean@3.0.3:
    lodash.isboolean: private
  lodash.isinteger@4.0.4:
    lodash.isinteger: private
  lodash.isnumber@3.0.3:
    lodash.isnumber: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.isstring@4.0.1:
    lodash.isstring: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.once@4.1.1:
    lodash.once: private
  lodash@4.17.21:
    lodash: private
  long-timeout@0.1.1:
    long-timeout: private
  long@5.3.2:
    long: private
  lru-cache@5.1.1:
    lru-cache: private
  lru.min@1.1.2:
    lru.min: private
  luxon@3.7.1:
    luxon: private
  make-dir@3.1.0:
    make-dir: private
  makeerror@1.0.12:
    makeerror: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  media-typer@0.3.0:
    media-typer: private
  media-typer@1.1.0:
    media-typer: private
  memory-pager@1.5.0:
    memory-pager: private
  merge-descriptors@1.0.3:
    merge-descriptors: private
  merge-stream@2.0.0:
    merge-stream: private
  methods@1.1.2:
    methods: private
  micromatch@4.0.8:
    micromatch: private
  microsoft-cognitiveservices-speech-sdk@1.45.0(bufferutil@4.0.9)(utf-8-validate@5.0.10):
    microsoft-cognitiveservices-speech-sdk: private
  mime-db@1.52.0:
    mime-db: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime-types@3.0.1:
    mime-types: private
  mime@1.6.0:
    mime: private
  mimic-fn@2.1.0:
    mimic-fn: private
  minimatch@3.1.2:
    minimatch: private
  minipass@5.0.0:
    minipass: private
  minizlib@2.1.2:
    minizlib: private
  mkdirp@1.0.4:
    mkdirp: private
  module-details-from-path@1.0.4:
    module-details-from-path: private
  moment-timezone@0.5.48:
    moment-timezone: private
  moment@2.30.1:
    moment: private
  mongodb-connection-string-url@3.0.2:
    mongodb-connection-string-url: private
  mongodb@6.18.0(socks@2.8.6):
    mongodb: private
  mongoose@8.16.4(socks@2.8.6):
    mongoose: private
  mpath@0.9.0:
    mpath: private
  mquery@5.0.0:
    mquery: private
  ms@2.1.3:
    ms: private
  mulaw-js@1.0.1:
    mulaw-js: private
  multistream@2.1.1:
    multistream: private
  mute-stream@0.0.8:
    mute-stream: private
  mysql2@3.14.2:
    mysql2: private
  named-placeholders@1.1.3:
    named-placeholders: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  needle@2.4.0:
    needle: private
  negotiator@0.6.3:
    negotiator: private
  netmask@2.0.2:
    netmask: private
  next-tick@1.1.0:
    next-tick: private
  node-addon-api@5.1.0:
    node-addon-api: private
  node-domexception@1.0.0:
    node-domexception: private
  node-fetch@2.7.0:
    node-fetch: private
  node-forge@0.10.0:
    node-forge: private
  node-gyp-build@4.8.4:
    node-gyp-build: private
  node-int64@0.4.0:
    node-int64: private
  node-releases@2.0.19:
    node-releases: private
  node-schedule@2.1.1:
    node-schedule: private
  nodemailer@6.10.1:
    nodemailer: private
  nodemon@3.1.10:
    nodemon: private
  nopt@5.0.0:
    nopt: private
  normalize-path@3.0.0:
    normalize-path: private
  npm-run-path@4.0.1:
    npm-run-path: private
  npmlog@5.0.1:
    npmlog: private
  nssocket@0.6.0:
    nssocket: private
  oauth-sign@0.9.0:
    oauth-sign: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  open@10.2.0:
    open: private
  openai@4.104.0(ws@8.18.3(bufferutil@4.0.9)(utf-8-validate@5.0.10)):
    openai: private
  opn@5.5.0:
    opn: private
  optionator@0.9.4:
    optionator: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-try@2.2.0:
    p-try: private
  pac-proxy-agent@7.2.0:
    pac-proxy-agent: private
  pac-resolver@7.0.1:
    pac-resolver: private
  pako@0.2.9:
    pako: private
  parent-module@1.0.1:
    parent-module: private
  parse-cache-control@1.0.1:
    parse-cache-control: private
  parse-json@5.2.0:
    parse-json: private
  parseurl@1.3.3:
    parseurl: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-to-regexp@0.1.12:
    path-to-regexp: private
  performance-now@2.1.0:
    performance-now: private
  pg-connection-string@2.9.1:
    pg-connection-string: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pidusage@3.0.2:
    pidusage: private
  pirates@4.0.7:
    pirates: private
  pkg-dir@4.2.0:
    pkg-dir: private
  pm2-axon-rpc@0.7.1:
    pm2-axon-rpc: private
  pm2-axon@4.0.1:
    pm2-axon: private
  pm2-deploy@1.0.2:
    pm2-deploy: private
  pm2-multimeter@0.1.2:
    pm2-multimeter: private
  pm2-sysmonit@1.2.8:
    pm2-sysmonit: private
  pm2@5.4.3(bufferutil@4.0.9)(utf-8-validate@5.0.10):
    pm2: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prettier-linter-helpers@1.0.0:
    prettier-linter-helpers: private
  pretty-format@29.7.0:
    pretty-format: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  progress@2.0.3:
    progress: private
  promise@7.3.1:
    promise: private
  promptly@2.2.0:
    promptly: private
  prompts@2.4.2:
    prompts: private
  property-expr@2.0.6:
    property-expr: private
  protobufjs@6.11.4:
    protobufjs: private
  proxy-addr@2.0.7:
    proxy-addr: private
  proxy-agent@6.3.1:
    proxy-agent: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  psl@1.15.0:
    psl: private
  pstree.remy@1.1.8:
    pstree.remy: private
  pump@3.0.3:
    pump: private
  pumpify@2.0.1:
    pumpify: private
  punycode@1.4.1:
    punycode: private
  pure-rand@6.1.0:
    pure-rand: private
  qs@6.13.0:
    qs: private
  queue-microtask@1.2.3:
    queue-microtask: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@2.5.2:
    raw-body: private
  raw-body@3.0.0:
    raw-body: private
  react-is@18.3.1:
    react-is: private
  read@1.0.7:
    read: private
  readable-stream@2.3.8:
    readable-stream: private
  readdirp@3.6.0:
    readdirp: private
  redis-commands@1.7.0:
    redis-commands: private
  redis-errors@1.2.0:
    redis-errors: private
  redis-parser@3.0.0:
    redis-parser: private
  redis@3.1.2:
    redis: private
  request@2.88.2:
    request: private
  require-directory@2.1.1:
    require-directory: private
  require-in-the-middle@5.2.0:
    require-in-the-middle: private
  resolve-cwd@3.0.0:
    resolve-cwd: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve.exports@2.0.3:
    resolve.exports: private
  resolve@1.22.10:
    resolve: private
  retry-as-promised@7.1.1:
    retry-as-promised: private
  retry-request@4.2.2:
    retry-request: private
  reusify@1.1.0:
    reusify: private
  rimraf@3.0.2:
    rimraf: private
  run-applescript@7.0.0:
    run-applescript: private
  run-parallel@1.2.0:
    run-parallel: private
  run-series@1.1.9:
    run-series: private
  runes2@1.1.4:
    runes2: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sax@1.4.1:
    sax: private
  scmp@2.1.0:
    scmp: private
  semver@6.3.1:
    semver: private
  send@0.19.0:
    send: private
  seq-queue@0.0.5:
    seq-queue: private
  sequelize-pool@7.1.0:
    sequelize-pool: private
  sequelize@6.37.7(mysql2@3.14.2):
    sequelize: private
  sequin@0.1.1:
    sequin: private
  serve-static@1.16.2:
    serve-static: private
  set-blocking@2.0.0:
    set-blocking: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shimmer@1.2.1:
    shimmer: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  sift@17.1.3:
    sift: private
  signal-exit@3.0.7:
    signal-exit: private
  simple-update-notifier@2.0.0:
    simple-update-notifier: private
  sisteransi@1.0.5:
    sisteransi: private
  slash@3.0.0:
    slash: private
  smart-buffer@4.2.0:
    smart-buffer: private
  socket.io-adapter@2.5.5(bufferutil@4.0.9)(utf-8-validate@5.0.10):
    socket.io-adapter: private
  socket.io-parser@4.2.4:
    socket.io-parser: private
  socket.io@4.8.1(bufferutil@4.0.9)(utf-8-validate@5.0.10):
    socket.io: private
  socks-proxy-agent@8.0.5:
    socks-proxy-agent: private
  socks@2.8.6:
    socks: private
  sorted-array-functions@1.3.0:
    sorted-array-functions: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  sparse-bitfield@3.0.3:
    sparse-bitfield: private
  sprintf-js@1.1.2:
    sprintf-js: private
  sqlstring@2.3.3:
    sqlstring: private
  sshpk@1.18.0:
    sshpk: private
  stack-utils@2.0.6:
    stack-utils: private
  standard-as-callback@2.1.0:
    standard-as-callback: private
  statuses@2.0.1:
    statuses: private
  stream-events@1.0.5:
    stream-events: private
  stream-shift@1.0.3:
    stream-shift: private
  string-length@4.0.2:
    string-length: private
  string-width@4.2.3:
    string-width: private
  string_decoder@1.1.1:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
  strip-bom@4.0.0:
    strip-bom: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  stubs@3.0.0:
    stubs: private
  supports-color@5.5.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  synckit@0.11.11:
    synckit: private
  systeminformation@5.27.7:
    systeminformation: private
  tar@6.2.1:
    tar: private
  teeny-request@6.0.3:
    teeny-request: private
  test-exclude@6.0.0:
    test-exclude: private
  text-table@0.2.0:
    text-table: private
  tiny-case@1.0.3:
    tiny-case: private
  tldts-core@6.1.86:
    tldts-core: private
  tldts@6.1.86:
    tldts: private
  tmpl@1.0.5:
    tmpl: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  toposort-class@1.0.1:
    toposort-class: private
  toposort@2.0.2:
    toposort: private
  touch@3.1.1:
    touch: private
  tough-cookie@5.1.2:
    tough-cookie: private
  tr46@5.1.1:
    tr46: private
  tslib@2.8.1:
    tslib: private
  tunnel-agent@0.6.0:
    tunnel-agent: private
  turbo-linux-64@1.13.4:
    turbo-linux-64: private
  tv4@1.3.0:
    tv4: private
  tweetnacl@0.14.5:
    tweetnacl: private
  twilio@5.8.0:
    twilio: private
  tx2@1.0.5:
    tx2: private
  type-check@0.4.0:
    type-check: private
  type-detect@4.0.8:
    type-detect: private
  type-fest@0.20.2:
    type-fest: private
  type-is@1.6.18:
    type-is: private
  type-is@2.0.1:
    type-is: private
  type@2.7.3:
    type: private
  typedarray-to-buffer@3.1.5:
    typedarray-to-buffer: private
  typedarray@0.0.6:
    typedarray: private
  undefsafe@2.0.5:
    undefsafe: private
  undici-types@6.21.0:
    undici-types: private
  unpipe@1.0.0:
    unpipe: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  utf-8-validate@5.0.10:
    utf-8-validate: private
  util-deprecate@1.0.2:
    util-deprecate: private
  utils-merge@1.0.1:
    utils-merge: private
  uuid@9.0.1:
    uuid: private
  v8-to-istanbul@9.3.0:
    v8-to-istanbul: private
  validate.io-array@1.0.6:
    validate.io-array: private
  validate.io-function@1.0.2:
    validate.io-function: private
  validator@13.15.15:
    validator: private
  vary@1.1.2:
    vary: private
  verror@1.10.0:
    verror: private
  vizion@2.2.1:
    vizion: private
  walkdir@0.4.1:
    walkdir: private
  walker@1.0.8:
    walker: private
  web-streams-polyfill@4.0.0-beta.3:
    web-streams-polyfill: private
  webidl-conversions@7.0.0:
    webidl-conversions: private
  websocket-driver@0.7.4:
    websocket-driver: private
  websocket-extensions@0.1.4:
    websocket-extensions: private
  websocket@1.0.35:
    websocket: private
  whatwg-url@14.2.0:
    whatwg-url: private
  which@1.3.1:
    which: private
  wide-align@1.1.5:
    wide-align: private
  wkx@0.5.0:
    wkx: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@4.0.2:
    write-file-atomic: private
  ws@8.18.3(bufferutil@4.0.9)(utf-8-validate@5.0.10):
    ws: private
  wsl-utils@0.1.0:
    wsl-utils: private
  xml2js@0.5.0:
    xml2js: private
  xmlbuilder@13.0.2:
    xmlbuilder: private
  y18n@5.0.8:
    y18n: private
  yaeti@0.0.6:
    yaeti: private
  yallist@4.0.0:
    yallist: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yocto-queue@0.1.0:
    yocto-queue: private
  yup@1.6.1:
    yup: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.13.1
pendingBuilds: []
prunedAt: Thu, 24 Jul 2025 10:51:08 GMT
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@ffmpeg-installer/darwin-arm64@4.1.5'
  - '@ffmpeg-installer/darwin-x64@4.1.0'
  - '@ffmpeg-installer/linux-arm64@4.1.4'
  - '@ffmpeg-installer/linux-arm@4.1.3'
  - '@ffmpeg-installer/linux-ia32@4.1.0'
  - '@ffmpeg-installer/win32-ia32@4.1.0'
  - '@ffmpeg-installer/win32-x64@4.1.0'
  - fsevents@2.3.3
  - turbo-darwin-64@1.13.4
  - turbo-darwin-arm64@1.13.4
  - turbo-linux-arm64@1.13.4
  - turbo-windows-64@1.13.4
  - turbo-windows-arm64@1.13.4
storeDir: /home/<USER>/.local/share/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
