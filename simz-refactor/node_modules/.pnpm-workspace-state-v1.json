{"lastValidatedTimestamp": 1753382262108, "projects": {"/home/<USER>/work/simz-platform/simz-refactor": {"name": "simz-platform", "version": "1.0.0"}, "/home/<USER>/work/simz-platform/simz-refactor/services/core-call-service": {"name": "core-call-service", "version": "1.0.0"}, "/home/<USER>/work/simz-platform/simz-refactor/services/salesforce-service": {"name": "salesforce-service", "version": "1.0.0"}, "/home/<USER>/work/simz-platform/simz-refactor/shared/config": {"name": "@simz-platform/config", "version": "1.0.0"}, "/home/<USER>/work/simz-platform/simz-refactor/shared/middleware": {"name": "@simz-platform/middleware", "version": "1.0.0"}, "/home/<USER>/work/simz-platform/simz-refactor/shared/types": {"name": "@simz-platform/types", "version": "1.0.0"}, "/home/<USER>/work/simz-platform/simz-refactor/shared/utils": {"name": "@simz-platform/utils", "version": "1.0.0"}}, "pnpmfiles": [], "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": true, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "workspacePackagePatterns": ["services/*", "shared/*"]}, "filteredInstall": true}