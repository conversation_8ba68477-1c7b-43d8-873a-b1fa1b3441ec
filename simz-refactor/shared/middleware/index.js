// Reusable middleware for SimZ platform
const cors = require('cors');
const express = require('express');

/**
 * CORS middleware with default configuration
 */
const corsMiddleware = (origins = ['http://localhost:3000']) => {
  return cors({
    origin: origins,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
  });
};

/**
 * Request logging middleware
 */
const requestLogger = (req, res, next) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${req.method} ${req.path} - ${req.ip}`);
  next();
};

/**
 * Error handling middleware
 */
const errorHandler = (err, req, res, next) => {
  console.error('Error:', err);
  
  const status = err.status || 500;
  const message = err.message || 'Internal Server Error';
  
  res.status(status).json({
    error: {
      message,
      status,
      timestamp: new Date().toISOString()
    }
  });
};

/**
 * JSON body parser with size limit
 */
const jsonParser = express.json({ limit: '10mb' });

/**
 * URL encoded parser
 */
const urlEncodedParser = express.urlencoded({ extended: true, limit: '10mb' });

module.exports = {
  corsMiddleware,
  requestLogger,
  errorHandler,
  jsonParser,
  urlEncodedParser
};
