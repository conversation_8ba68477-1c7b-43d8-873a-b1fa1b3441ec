{"name": "@simz-platform/types", "version": "1.0.0", "description": "Shared TypeScript type definitions for SimZ platform", "main": "index.js", "types": "index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "clean": "rm -rf dist"}, "devDependencies": {"typescript": "^5.0.0", "@types/node": "^20.0.0"}, "author": "<PERSON>", "license": "ISC"}