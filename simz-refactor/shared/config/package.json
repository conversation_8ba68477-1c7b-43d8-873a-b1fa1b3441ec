{"name": "@simz-platform/config", "version": "1.0.0", "description": "Shared configuration for SimZ platform", "main": "index.js", "scripts": {"build": "echo 'No build step required for configuration'", "dev": "echo 'No dev mode for config'", "test": "jest", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "clean": "rm -rf coverage"}, "dependencies": {"dotenv": "^16.0.0"}, "author": "<PERSON>", "license": "ISC"}