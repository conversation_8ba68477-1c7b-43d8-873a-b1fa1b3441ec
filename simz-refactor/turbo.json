{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", "build/**", ".next/**", "!.next/cache/**"]}, "dev": {"cache": false, "persistent": true}, "test": {"dependsOn": ["build"], "outputs": ["coverage/**"], "inputs": ["src/**/*.ts", "src/**/*.js", "tests/**/*.ts", "tests/**/*.js", "jest.config.js"]}, "test:unit": {"dependsOn": ["build"], "outputs": ["coverage/**"], "inputs": ["src/**/*.ts", "src/**/*.js", "tests/unit/**/*.ts", "tests/unit/**/*.js", "jest.config.js"]}, "test:integration": {"dependsOn": ["build"], "outputs": ["coverage/**"], "inputs": ["src/**/*.ts", "src/**/*.js", "tests/integration/**/*.ts", "tests/integration/**/*.js", "jest.config.js"]}, "test:coverage": {"dependsOn": ["build"], "outputs": ["coverage/**"], "inputs": ["src/**/*.ts", "src/**/*.js", "tests/**/*.ts", "tests/**/*.js", "jest.config.js"]}, "lint": {"outputs": []}, "lint:fix": {"outputs": []}, "format": {"outputs": []}, "format:check": {"outputs": []}, "clean": {"cache": false}, "start": {"dependsOn": ["build"], "cache": false, "persistent": true}}}