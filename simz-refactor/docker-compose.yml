version: '3.8'

services:
  # Core Call Service
  core-call-service:
    build:
      context: ./services/core-call-service
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - MONGODB_URI=mongodb://mongodb:27017/simz
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./services/core-call-service:/app
      - /app/node_modules
    networks:
      - simz-network
    restart: unless-stopped

  # Salesforce Service
  salesforce-service:
    build:
      context: ./services/salesforce-service
      dockerfile: Dockerfile
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=production
      - SALESFORCE_SERVICE_PORT=3002
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - redis
    volumes:
      - ./services/salesforce-service:/app
      - /app/node_modules
    networks:
      - simz-network
    restart: unless-stopped

  # MongoDB Database
  mongodb:
    image: mongo:6.0
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
      - MONGO_INITDB_DATABASE=simz
    volumes:
      - mongodb_data:/data/db
      - ./tools/docker/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - simz-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - simz-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # MySQL Database (if needed)
  mysql:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=simz
      - MYSQL_USER=simz_user
      - MYSQL_PASSWORD=simz_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./tools/docker/mysql-init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - simz-network
    restart: unless-stopped



volumes:
  mongodb_data:
  redis_data:
  mysql_data:

networks:
  simz-network:
    driver: bridge
