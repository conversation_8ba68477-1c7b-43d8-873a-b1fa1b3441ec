#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/pm2@5.4.3_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/pm2/bin/node_modules:/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/pm2@5.4.3_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/pm2/node_modules:/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/pm2@5.4.3_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules:/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/pm2@5.4.3_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/pm2/bin/node_modules:/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/pm2@5.4.3_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/pm2/node_modules:/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/pm2@5.4.3_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules:/home/<USER>/work/simz-platform/simz-refactor/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../pm2/bin/pm2-runtime" "$@"
else
  exec node  "$basedir/../pm2/bin/pm2-runtime" "$@"
fi
