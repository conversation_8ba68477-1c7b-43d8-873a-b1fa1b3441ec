// agents/services/AgentStatusService.js
class AgentStatusService {
  constructor() {
    if (AgentStatusService.instance) {
      return AgentStatusService.instance;
    }
    AgentStatusService.instance = this;

    this.agents = new Map(); // Map of agentId to agent status
    this.wsClients = new Set(); // Set of connected WebSocket clients
  }

  initializeAgent(agentId, agentData) {
    this.agents.set(agentId, {
      ...agentData,
      status: 'available',
      lastStatusUpdate: Date.now(),
    });
    this.broadcastStatus();
  }

  initializeAgents(agentsList) {
    // Clear existing agents
    this.agents.clear();

    // Initialize all agents
    agentsList.forEach((agent) => {
      this.agents.set(agent.Agent_ID, {
        name: agent.Agent_Name,
        twilioNumber: agent.Twilio_Number,
        status: 'available',
        lastStatusUpdate: Date.now(),
      });
    });

    this.broadcastStatus();
  }

  updateAgentStatus(agentId, status) {
    const agent = this.agents.get(agentId);
    if (agent) {
      agent.status = status;
      agent.lastStatusUpdate = Date.now();
      this.broadcastStatus();
    }
  }

  updateAgentStatusByPhoneNumber(phoneNumber, status) {
    // Find agent with matching Twilio number
    for (const [agentId, agentData] of this.agents.entries()) {
      if (agentData.twilioNumber === phoneNumber) {
        // Update status for matching agent
        agentData.status = status;
        agentData.lastStatusUpdate = Date.now();

        console.log('broadcast');
        this.broadcastStatus();
        return;
      }
    }
  }

  getAgentStatus(agentId) {
    return this.agents.get(agentId)?.status || 'unknown';
  }

  getAllAgentsStatus() {
    return Array.from(this.agents.entries()).map(([id, data]) => ({
      id,
      ...data,
    }));
  }

  addWebSocketClient(ws) {
    this.wsClients.add(ws);

    // Send initial status to new client
    ws.send(
      JSON.stringify({
        type: 'AGENT_STATUS',
        agents: this.getAllAgentsStatus(),
      })
    );
  }

  removeWebSocketClient(ws) {
    this.wsClients.delete(ws);
  }

  broadcastStatus() {
    const data = JSON.stringify({
      type: 'AGENT_STATUS',
      agents: this.getAllAgentsStatus(),
    });

    this.wsClients.forEach((client) => {
      if (client.readyState === 1) {
        // WebSocket.OPEN
        client.send(data);
      }
    });
  }
}

// Create a single instance
const agentStatusService = new AgentStatusService();

module.exports = { AgentStatusService, agentStatusService };
