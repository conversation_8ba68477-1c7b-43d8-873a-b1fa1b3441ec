{"name": "core-call-service", "version": "1.0.0", "description": "Core call processing service for SimZ platform", "main": "index.js", "dependencies": {"@azure/ai-text-analytics": "^5.1.0", "@azure/identity": "^4.4.1", "@deepgram/sdk": "^3.11.1", "@ffmpeg-installer/ffmpeg": "^1.1.0", "@google-cloud/speech": "^3.6.0", "@google/generative-ai": "^0.17.0", "@sendgrid/mail": "^8.1.5", "axios": "^1.7.7", "bcrypt": "^5.1.1", "compute-cosine-similarity": "^1.1.0", "cors": "^2.8.5", "dotenv": "^8.6.0", "express": "^4.19.2", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.3", "ics": "^3.8.1", "ioredis": "^5.5.0", "jsforce": "^1.11.1", "jsonwebtoken": "^9.0.2", "microsoft-cognitiveservices-speech-sdk": "^1.42.0", "mongodb": "^6.15.0", "mongoose": "^8.13.0", "mulaw-js": "^1.0.1", "mysql2": "^3.12.0", "node-schedule": "^2.1.1", "nodemailer": "^6.9.16", "nodemon": "^3.1.0", "openai": "^4.85.4", "pm2": "^5.4.3", "redis": "^3.1.2", "sequelize": "^6.37.5", "socket.io": "^4.8.1", "twilio": "^5.4.0", "websocket": "^1.0.29", "ws": "^8.18.1"}, "scripts": {"build": "echo 'No build step required for Node.js service'", "dev": "nodemon index.js", "start": "node index.js", "test": "jest", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "clean": "rm -rf dist coverage", "pm2:start": "pm2 start index.js --name 'core-call-service'", "pm2:restart": "pm2 restart core-call-service", "pm2:stop": "pm2 stop core-call-service", "pm2:delete": "pm2 delete core-call-service", "pm2:logs": "pm2 logs core-call-service", "pm2:status": "pm2 status"}, "author": "<PERSON>", "license": "ISC", "devDependencies": {"eslint": "^8.56.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "jest": "^29.7.0", "prettier": "^3.5.3"}}