# SimZ - AI-Powered Call Center Platform

SimZ is a comprehensive AI-powered call center platform that integrates Twilio for call management, various LLM providers for natural language processing, and Salesforce for CRM integration.

## Features

- **Call Management**: Handle inbound and outbound calls using Twilio
- **Real-time Speech Processing**:
  - Speech-to-Text using Deepgram
  - Text-to-Speech using ElevenLabs
- **LLM Integration**: Support for multiple LLM providers (OpenAI, Google Gemini, etc.)
- **Intelligent Agents**: Automated customer service and call handling
- **CRM Integration**: Seamless connection with Salesforce
- **User Authentication**: JWT-based authentication system
- **Real-time Communication**: WebSocket support for live interactions
- **Call Recording**: Automatic call recording and transcription

## Tech Stack

- **Backend**: Node.js/Express.js
- **Databases**:
  - MySQL: Call metadata storage
  - MongoDB: Transcript storage
  - Redis: Caching
- **Real-time Communication**: WebSocket
- **Process Management**: PM2
- **External APIs**:
  - <PERSON><PERSON><PERSON> (Call Management)
  - Deepgram (Speech Recognition)
  - ElevenLabs (Text-to-Speech)
  - Various LLM Providers

## Project Structure

```
simz/
├── agents/                 # Agent management
├── auth/                   # Authentication
├── call-management/        # Call handling
├── cache-management/       # Caching system
├── config/                 # Configuration files
├── llm-integration/        # LLM integration
├── public/                 # Static files
├── salesforce/            # Salesforce integration
├── speech-to-text/        # Speech recognition
├── text-to-speech/        # Text-to-speech
└── utils/                 # Utility functions
```

## API Endpoints

### Authentication

- `POST /api/login` - User login
- `POST /api/register` - User registration

### Call Management

- `POST /twiml/in-bound` - Handle incoming calls
- `POST /twiml/out-bound` - Handle outbound calls
- `GET /api/call-history` - Get call history
- `GET /api/transcript/:callId` - Get call transcript

### LLM Integration

- `GET /api/prompts` - Get all prompts
- `GET /api/get-prompt` - Get specific prompt
- `POST /api/save-prompt` - Save new prompt
- `POST /api/grok-chat` - Chat with Grok
- `POST /api/clear-chat` - Clear chat history
- `GET /api/prompt-versions` - Get prompt versions
- `GET /api/prompt-by-mongo` - Get prompt by MongoDB ID

### Agent Management

- `POST /api/agents` - Create new agent
- `PUT /api/agents/:agentId` - Update agent
- `GET /api/agents` - Get all agents
- `GET /api/twilio-numbers` - Get Twilio numbers
- `POST /api/make-call` - Initiate a call

### System

- `GET /status` - Check system status
- `POST /ping` - Health check endpoint

## Application Management

### Prerequisites

- Node.js (v14 or higher)
- MySQL
- MongoDB
- Redis
- PM2 (for production)

### Installation

1. Clone the repository:

```bash
git clone [repository-url]
cd simz
```

2. Install dependencies:

```bash
npm install
```

3. Configure environment variables:

```bash
cp .env.example .env
# Edit .env with your configuration
```

### Running the Application

#### Development Mode

```bash
npm start
```

#### Production Mode with PM2

```bash
# Install PM2 globally (if not installed)
npm install pm2 -g

# Start the application
npm run pm2:start

# Or use PM2 directly with config
pm2 start ecosystem.config.js
```

### PM2 Management Commands

```bash
# View application status
npm run pm2:status

# View logs
npm run pm2:logs

# Restart application
npm run pm2:restart

# Stop application
npm run pm2:stop

# Delete application from PM2
npm run pm2:delete
```

### PM2 Configuration

The application uses PM2 for process management with the following features:

- Auto-restart on crashes
- Memory limit monitoring
- Log management
- Health checks
- Production environment settings

## Environment Variables

Key environment variables required:

- `MYSQL_HOST`: MySQL database host
- `MYSQL_USER`: MySQL database user
- `MYSQL_PASSWORD`: MySQL database password
- `MYSQL_DATABASE`: MySQL database name
- `MONGO_URI`: MongoDB connection string
- `REDIS_HOST`: Redis host
- `REDIS_PORT`: Redis port
- `REDIS_PASSWORD`: Redis password
- `JWT_SECRET`: JWT secret key
- `TWILIO_ACCOUNT_SID`: Twilio account SID
- `TWILIO_AUTH_TOKEN`: Twilio auth token

## Security

- All API endpoints (except login/register) require JWT authentication
- API key validation for Twilio webhooks
- Secure WebSocket connections
- Environment variable protection

## Monitoring and Logging

- PM2 provides process monitoring
- Application logs are stored in:
  - `logs/pm2-error.log`
  - `logs/pm2-out.log`
  - `logs/combined.log`

## Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a new Pull Request

## License

ISC License
