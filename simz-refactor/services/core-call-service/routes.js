// routes.js
module.exports = (callService) => {
  const express = require('express');
  const router = express.Router();
  const { TwilioController } = require('./call-management/controllers/TwilioController');
  const { WebLLMController } = require('./llm-integration/controllers/WebLLMController');
  const { AuthController } = require('./auth/controllers/AuthController');
  const { CallController } = require('./call-management/controllers/CallController');
  const { AgentController } = require('./agents/controllers/AgentController');
  const { SalesforceController } = require('./salesforce/controllers/SalesforceController');
  const { SalesforceService } = require('./salesforce/services/SalesforceService');
  const jwt = require('jsonwebtoken');
  const apiKeys = require('./config/api-keys');
  const path = require('path');
  const twilio = require('twilio');
  const client = twilio(apiKeys.twilio.accountSid, apiKeys.twilio.authToken);
  const env = require('./config/env');

  const twilioController = new TwilioController(router, apiKeys, callService);
  const webLLMController = new WebLLMController();
  const authController = new AuthController();
  const callController = new CallController(apiKeys, callService);
  const agentController = new AgentController(callService);
  const salesforceController = new SalesforceController();

  // get the correct WebSocket URL - compatible with Nginx proxy
  const getWsUrl = (req) => {
    // simulate ngrok mode, return a fixed domain name
    const wsUrl = `wss://${env.server.url.replace('http://', '').replace('https://', '')}/`;
    console.log('[routes.js > getWsUrl] Using WebSocket URL:', wsUrl);
    return wsUrl;
  };

  const authenticateToken = (req, res, next) => {
    const token = req.headers.authorization?.split(' ')[1];
    if (!token) {
      console.log('[authenticateToken] No token provided');
      return res.status(401).json({ message: 'No token provided' });
    }
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      req.user = decoded;
      console.log('[routes.js > authenticateToken] Set req.user:', req.user);
      next();
    } catch (error) {
      console.error('[routes.js > authenticateToken] Token verification failed:', error.message);
      return res.status(401).json({ message: 'Invalid token' });
    }
  };

  // Existing routes
  router.post('/ping', (req, res) => res.send('pong'));

  // add twiml entry point - for Nginx proxy configuration (supports GET requests for validation)
  router.get('/twiml/in-bound', (req, res) => {
    console.log('[routes.js] GET request to twiml in-bound for validation');
    res.set('Content-Type', 'text/xml');
    res.send(`
      <Response>
        <Say>Webhook validation successful</Say>
      </Response>
    `);
  });

  router.post('/twiml/in-bound', async (req, res) => {
    console.log('[routes.js] Incoming call via twiml endpoint');
    console.log('[routes.js] Request body:', req.body);
    // console.log('[routes.js] Request headers:', req.headers);

    const callSid = req.body.CallSid;
    const twilioPhoneNumber = req.body.To;
    const fromNumber = req.body.From;
    const callerNumber = req.body.Caller;

    if (twilioPhoneNumber) {
      agentController.agentStatusService.updateAgentStatusByPhoneNumber(
        twilioPhoneNumber,
        'in-call'
      );
    }

    try {
      // Initialize call record in database
      await callController.handleInboundCall(callSid, fromNumber, twilioPhoneNumber);
    } catch (error) {
      console.error('[routes.js] Error initializing call record:', error);
      throw error;
    }

    if (callSid) {
      client
        .calls(callSid)
        .recordings.create({
          recordingStatusCallback: `${env.server.url}/api/call-recordings/callback`,
          recordingStatusCallbackEvent: ['completed'],
          recordingStatusCallbackMethod: 'POST',
        })
        .then((recording) => console.log('[routes.js] Recording started:', recording.sid))
        .catch((err) => console.error('[routes.js] Recording failed:', err));

      const leadData = callService.getStagedLeadData(callSid);
      if (leadData) {
        leadData.customerPhone = fromNumber;
        callService.stageLeadData(callSid, leadData);
        console.log('[routes.js] Updated staged lead data with customerPhone.');
      } else {
        console.warn(
          `[routes.js] No pre-staged lead data found for callSid ${callSid}. Creating new.`
        );
        callService.stageLeadData(callSid, { customerPhone: fromNumber });
      }

      const promptData = callService.getStagedPromptData(callSid);
      if (promptData) {
        promptData.callerNumber = callerNumber;
        promptData.promptType = 'simz-hotel';
        callService.stagePromptData(callSid, promptData);
        console.log('[routes.js] Updated staged prompt data with promptType.');
      } else {
        console.warn(
          `[routes.js] No pre-staged prompt data found for callSid ${callSid}. Creating new.`
        );
        const newPromptData = {
          callerNumber: callerNumber,
          twilioCallSid: callSid,
          internalCallID: callSid,
          promptType: 'simz-hotel',
        };
        callService.stagePromptData(callSid, newPromptData);
      }
    } else {
      console.warn('[routes.js] No CallSid provided in the request');
    }

    // use similar original ngrok mode response format
    res.set('Content-Type', 'text/xml');
    res.send(`
      <Response>
        <Connect>
          <Stream url="${getWsUrl(req)}"/>
        </Connect>
        <Say>Not able to connect</Say>
        <Pause length="60" />
      </Response>
    `);
  });

  router.post('/twiml/in-bound/transfer-thelegalleads', async (req, res) => {
    console.log('[routes.js] Incoming call via twiml endpoint, transfer to in-house agent');
    // console.log('[routes.js] Request headers:', req.headers);

    const callSid = req.body.CallSid;
    const twilioPhoneNumber = req.body.To;
    const fromNumber = req.body.From;

    // Create twiml
    const twiml = new twilio.twiml.VoiceResponse();

    // transfer
    twiml.say('Transferring you to the next available intake specialist, please wait.');
    twiml.dial('+13233804546', {
      callerId: twilioPhoneNumber, // Original phone number
    });

    // Failed
    twiml.say('Sorry, the intake specialist is busy, please try again later.');

    // Send twiml
    res.type('text/xml');
    res.send(twiml.toString());
  });

  // add support for GET requests for validation
  router.get('/twiml/out-bound', (req, res) => {
    console.log('[routes.js] GET request to twiml out-bound for validation');
    res.set('Content-Type', 'text/xml');
    res.send(`
      <Response>
        <Say>Webhook validation successful</Say>
      </Response>
    `);
  });

  // this is the original route, keep it for compatibility
  router.post('/in-bound', (req, res) => {
    console.log('[routes.js] Incoming call via original endpoint');
    console.log('[routes.js] Request body:', req.body);

    const callSid = req.body.CallSid;
    if (callSid) {
      client
        .calls(callSid)
        .recordings.create()
        .then((recording) => console.log('[routes.js] Recording started:', recording.sid))
        .catch((err) => console.error('[routes.js] Recording failed:', err));
    } else {
      console.warn('[routes.js] No CallSid provided in the request');
    }

    // use Nginx's WebSocket URL
    const wsUrl = getWsUrl(req);
    console.log('[routes.js] Using WebSocket URL:', wsUrl);

    res.set('Content-Type', 'text/xml');
    res.send(`
      <Response>
        <Connect>
          <Stream url="${wsUrl}"/>
        </Connect>
        <Say>Not able to connect</Say>
        <Pause length="60" />
      </Response>
    `);
  });

  // add twiml outbound call
  router.post('/twiml/out-bound', (req, res) => {
    console.log('[routes.js] Outbound call via twiml endpoint:', req.body);
    const callSid = req.body.CallSid;
    client
      .calls(callSid)
      .recordings.create()
      .then((recording) => console.log('[routes.js] Recording started:', recording.sid))
      .catch((err) => console.error('[routes.js] Recording failed:', err));
    res.set('Content-Type', 'text/xml');
    res.send(`
      <Response>
        <Connect>
          <Stream url="${getWsUrl(req)}/twiml"/>
        </Connect>
        <Say>We are sorry, an error occurred, bye</Say>
        <Pause length="60" />
      </Response>
    `);
  });

  // Add route for outbound calls
  router.post('/out-bound', async (req, res) => {
    console.log('[routes.js] Outbound call received:', req.body);
    const callSid = req.body.CallSid;

    // const salesforceService = new SalesforceService();
    // const lead = await salesforceService.findLeadByPhoneNumber(req.body.Caller);

    // // Update the current client info to the global.callData.leadData
    // global.callData = {
    //   leadData: {
    //     name: lead.Name,
    //     phone: lead.Phone,
    //     email: lead.Email,
    //     company: lead.Company,
    //     campaignType: lead.Campaign_Type__c,
    //   },
    //   salesforceId: lead.Id,
    // };

    // console.log('[routes.js] Global callData:', global.callData);

    // client
    //   .calls(callSid)
    //   .recordings.create()
    //   .then((recording) => console.log('[routes.js] Recording started:', recording.sid))
    //   .catch((err) => console.error('[routes.js] Recording failed:', err));
    res.set('Content-Type', 'text/xml');
    res.send(`
      <Response>
        <Pause length="1" />
        <Say language="en-US" voice="Polly.Joanna"> 
          <prosody rate="medium">This call may be monitored and recorded by an A eye soundboard for quality and training purposes.</prosody>
        </Say>
        <Connect>
          <Stream url="${getWsUrl(req)}/twiml"/>
        </Connect>
        <Say>We are sorry, an error occurred, good bye for now</Say>
        <Pause length="60" />
      </Response>
    `);
  });

  router.get('/', (req, res) => res.sendFile(path.join(__dirname, './public/index.html')));
  router.get('/chat-grok', (req, res) =>
    res.sendFile(path.join(__dirname, './public/GrokInterface.html'))
  );
  router.get('/status', (req, res) =>
    res.json({ status: 'Server is running', uptime: process.uptime() })
  );

  // For inbound prompts
  router.get('/api/get-inbound-prompt', webLLMController.getInboundPrompt.bind(webLLMController));
  router.post(
    '/api/save-inbound-prompt',
    webLLMController.saveInboundPrompt.bind(webLLMController)
  );
  router.post(
    '/api/chat-with-inbound-prompt',
    webLLMController.chatWithInboundPrompt.bind(webLLMController)
  );
  router.post('/api/clear-inbound-chat', webLLMController.clearInboundChat.bind(webLLMController));

  router.get(
    '/api/prompts',
    authenticateToken,
    webLLMController.getCompanyPrompts.bind(webLLMController)
  );
  router.get(
    '/api/get-prompt',
    authenticateToken,
    webLLMController.getPrompt.bind(webLLMController)
  );
  router.post(
    '/api/save-prompt',
    authenticateToken,
    webLLMController.savePrompt.bind(webLLMController)
  );
  router.post(
    '/api/grok-chat',
    authenticateToken,
    webLLMController.chatWithGrok.bind(webLLMController)
  );
  router.post(
    '/api/clear-chat',
    authenticateToken,
    webLLMController.clearChat.bind(webLLMController)
  );
  router.get(
    '/api/prompt-versions',
    authenticateToken,
    webLLMController.getPromptVersions.bind(webLLMController)
  );
  router.get(
    '/api/prompt-by-mongo',
    authenticateToken,
    webLLMController.getPromptByMongoId.bind(webLLMController)
  );

  router.post('/api/login', authController.login.bind(authController));
  router.post('/api/register', authController.register.bind(authController));
  router.get(
    '/api/call-history',
    authenticateToken,
    callController.getCallHistory.bind(callController)
  );
  router.get(
    '/api/transcript/:callId',
    authenticateToken,
    callController.getTranscript.bind(callController)
  );
  router.get('/api/call-recordings', callController.getCallRecordings.bind(callController));
  router.get(
    '/api/call-recordings/:recordingSid',
    callController.getRecordingFile.bind(callController)
  );
  router.post(
    '/api/call-recordings/callback',
    callController.handleRecordingCallback.bind(callController)
  );
  router.post('/api/live-calls', callController.getLiveCalls.bind(callController));
  router.get(
    '/api/ongoing-calls-by-agent/:agentId',
    callController.getOngoingCallsByAgent.bind(callController)
  );

  // Agents routes
  router.post('/api/agents', authenticateToken, agentController.createAgent.bind(agentController));
  router.get('/api/agents', authenticateToken, agentController.getCompanyAgents.bind(agentController));
  router.put(
    '/api/agents/:agentId',
    authenticateToken,
    agentController.updateAgent.bind(agentController)
  );
  router.get(
    '/api/twilio-numbers',
    authenticateToken,
    agentController.getTwilioNumbers.bind(agentController)
  );
  router.post('/api/make-call', authenticateToken, agentController.makeCall.bind(agentController));

  // AMD 相关路由
  router.post(
    '/api/make-call-with-amd',
    authenticateToken,
    agentController.makeCallWithAMD.bind(agentController)
  );
  router.post('/api/amd-callback', agentController.handleAMDCallback.bind(agentController));
  router.get(
    '/api/amd-status/:callSid',
    authenticateToken,
    agentController.getAMDCallStatus.bind(agentController)
  );

  // Add route for agent call status
  router.post('/agent-call-status', callController.handleCallStatus.bind(callController));

  // add back the original agent-confirm endpoint
  router.post('/agent-confirm', (req, res) => {
    console.log('[routes.js] Agent confirmation endpoint hit');
    console.log('[routes.js] Request headers:', req.headers);
    console.log('[routes.js] Request body:', req.body);
    console.log('[routes.js] Request query:', req.query);

    const twiml = new twilio.twiml.VoiceResponse();

    // Get conferenceName from query parameters
    const conferenceName = req.query.conferenceName;
    console.log('[routes.js] Conference name:', conferenceName);

    // If agent pressed 1, join the conference
    if (req.body.Digits === '1') {
      console.log('[routes.js > agent-confirm] Agent pressed 1, connecting to conference');
      twiml.say(
        { voice: 'Google.en-US-Chirp3-HD-Aoede', language: 'en-US' },
        'Connecting you to the customer.'
      );
      twiml.dial().conference(
        {
          endConferenceOnExit: true,
          startConferenceOnEnter: true,
          beep: false,
        },
        conferenceName
      );
    } else if (req.body.Digits === '2') {
      console.log('[routes.js > agent-confirm] Agent pressed 2, replaying customer information');
      // 重新收集按键输入
      const actionUrl = `${env.server.url}/agent-confirm?conferenceName=${encodeURIComponent(conferenceName)}&customerName=${encodeURIComponent(req.query.customerName)}&customerPhone=${encodeURIComponent(req.query.customerPhone)}&qualificationInfo=${encodeURIComponent(req.query.qualificationInfo)}`;

      twiml
        .gather({
          numDigits: 1,
          action: actionUrl,
          method: 'POST',
          timeout: 300,
        })
        .say(
          { voice: 'Google.en-US-Chirp3-HD-Aoede', language: 'en-US' },
          `Customer information:
        Name: ${req.query.customerName || 'Unknown'},
        Again, the customer name is: ${req.query.customerName || 'Unknown'},
        Customer Phone: ${req.query.customerPhone || 'Not provided'},
        Again, the customer phone is: ${req.query.customerPhone || 'Not provided'},
        Campaign Type: ${req.query.campaignType || 'Not provided'},
        And Qualification Info: ${req.query.qualificationInfo || 'Not provided'},
        Press 1 to join the call.,
        Press 2 to listen to the customer information again.`
        );
    } else {
      console.log('[routes.js] Invalid input received:', req.body.Digits);
      // If wrong input, redirect back to the initial prompt
      twiml.redirect('/agent-call');
    }

    const response = twiml.toString();
    console.log('[routes.js > agent-confirm] Generated TwiML:', response);
    res.type('text/xml');
    res.send(response);
  });

  // add test endpoint, display request information
  router.get('/nginx-test', (req, res) => {
    console.log('[routes.js] Nginx test endpoint hit');
    console.log('[routes.js] Request headers:', req.headers);
    // return HTML page, display request information
    res.send(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Nginx Configuration Test</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { color: #333; }
            pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
          </style>
        </head>
        <body>
          <h1>Nginx Configuration Test</h1>
          <p>Application is running correctly and Nginx is forwarding requests.</p>
          
          <h2>Request Headers:</h2>
          <pre>${JSON.stringify(req.headers, null, 2)}</pre>
          
          <h2>WebSocket Test</h2>
          <div id="status">Connecting...</div>
          <pre id="log"></pre>
          
          <script>
            const log = document.getElementById('log');
            const status = document.getElementById('status');
            
            function appendLog(message) {
              log.textContent += message + '\\n';
            }
            
            try {
              const ws = new WebSocket('wss://' + window.location.host + '/');
              
              ws.onopen = function() {
                status.textContent = 'Connected to WebSocket';
                status.style.color = 'green';
                appendLog('WebSocket connection established');
              };
              
              ws.onmessage = function(event) {
                appendLog('Received: ' + event.data);
              };
              
              ws.onerror = function(error) {
                status.textContent = 'WebSocket Error';
                status.style.color = 'red';
                appendLog('WebSocket error: ' + JSON.stringify(error));
              };
              
              ws.onclose = function(event) {
                status.textContent = 'WebSocket Closed: ' + event.code;
                status.style.color = 'orange';
                appendLog('WebSocket closed with code: ' + event.code);
              };
            } catch(e) {
              status.textContent = 'WebSocket Error: ' + e.message;
              status.style.color = 'red';
              appendLog('Error creating WebSocket: ' + e.message);
            }
          </script>
        </body>
      </html>
    `);
  });

  // Salesforce routes
  router.get('/api/salesforce/test-connection', authenticateToken, (req, res) =>
    salesforceController.testConnection(req, res)
  );
  router.post('/api/salesforce/disconnect', authenticateToken, (req, res) =>
    salesforceController.disconnect(req, res)
  );
  router.post('/api/salesforce/create', authenticateToken, (req, res) =>
    salesforceController.createRecord(req, res)
  );
  router.post('/api/salesforce/query', authenticateToken, (req, res) =>
    salesforceController.query(req, res)
  );
  router.post('/api/salesforce/update', authenticateToken, (req, res) =>
    salesforceController.updateRecord(req, res)
  );
  router.post('/api/salesforce/delete', authenticateToken, (req, res) =>
    salesforceController.deleteRecord(req, res)
  );
  router.get('/api/salesforce/describe/:objectType', authenticateToken, (req, res) =>
    salesforceController.describeObject(req, res)
  );

  // Add new route for Simz Steak House interface
  router.get('/chat-simz-steak', (req, res) =>
    res.sendFile(path.join(__dirname, './public/SimzSteakInterface.html'))
  );

  // For Simz Steak House prompts
  router.get(
    '/api/get-simz-steak-prompt',
    webLLMController.getSimzSteakHousePrompt.bind(webLLMController)
  );
  router.post(
    '/api/save-simz-steak-prompt',
    webLLMController.saveSimzSteakHousePrompt.bind(webLLMController)
  );
  router.post(
    '/api/chat-with-simz-steak-prompt',
    webLLMController.chatWithSimzSteakHousePrompt.bind(webLLMController)
  );
  router.post('/api/clear-simz-steak-chat', webLLMController.clearChat.bind(webLLMController));

  // add twiml entry point for Simz Steak House
  router.get('/twiml/in-bound/simz-steakhouse', (req, res) => {
    console.log('[routes.js] GET request to twiml in-bound for Simz Steak House validation');
    res.set('Content-Type', 'text/xml');
    res.send(`
      <Response>
        <Say>Webhook validation successful for Simz Steak House</Say>
      </Response>
    `);
  });

  router.post('/twiml/in-bound/simz-steakhouse', async (req, res) => {
    console.log('[routes.js] Incoming call via Simz Steak House twiml endpoint');
    console.log('[routes.js] Request body:', req.body);

    const callSid = req.body.CallSid;
    const callerNumber = req.body.Caller;

    const salesforceService = new SalesforceService();
    const lead = await salesforceService.findLeadByPhoneNumber(req.body.Caller);

    callService.stageLeadData(callSid, {
      name: lead.Name,
      phone: lead.Phone,
      email: lead.Email,
      company: lead.Company,
      campaignType: lead.Campaign_Type__c,
      salesforceId: lead.Id,
    });

    if (callSid) {
      client
        .calls(callSid)
        .recordings.create()
        .then((recording) => console.log('[routes.js] Recording started:', recording.sid))
        .catch((err) => console.error('[routes.js] Recording failed:', err));

      const promptData = {
        callerNumber: callerNumber,
        twilioCallSid: callSid,
        internalCallID: callSid,
        promptType: 'simz-steakhouse',
      };
      callService.stagePromptData(callSid, promptData);
    } else {
      console.warn('[routes.js] No CallSid provided in the request');
    }
    res.set('Content-Type', 'text/xml');
    res.send(`
      <Response>
        <Connect>
          <Stream url="${getWsUrl(req)}"/>
        </Connect>
        <Say>Not able to connect</Say>
        <Pause length="60" />
      </Response>
    `);
  });

  return router;
};
