{"name": "simz-platform", "version": "1.0.0", "description": "SimZ Platform - Monorepo with microservices architecture", "private": true, "workspaces": ["services/*", "shared/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "test": "turbo run test", "test:unit": "turbo run test:unit", "test:integration": "turbo run test:integration", "test:coverage": "turbo run test:coverage", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "format": "turbo run format", "format:check": "turbo run format:check", "clean": "turbo run clean", "start": "turbo run start", "start:core": "turbo run start --filter=core-call-service", "start:salesforce": "turbo run start --filter=salesforce-service", "docker:build": "docker-compose build", "docker:up": "docker-compose up", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f"}, "devDependencies": {"turbo": "^1.13.4", "eslint": "^8.56.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "prettier": "^3.5.3", "jest": "^29.7.0", "@types/node": "^20.0.0", "typescript": "^5.0.0"}, "packageManager": "pnpm", "engines": {"node": ">=18.10.0", "pnpm": ">=7.0.0"}, "author": "<PERSON>", "license": "ISC"}