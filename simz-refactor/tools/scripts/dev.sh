#!/bin/bash

# Development script for SimZ Platform
echo "🚀 Starting SimZ Platform in development mode..."

# Check if turbo is available
if ! command -v turbo &> /dev/null; then
    echo "❌ Turbo is not installed. Run npm install -g turbo first."
    exit 1
fi

# Start all services in development mode
echo "🔧 Starting all services..."
turbo run dev --parallel

echo "✅ All services started!"
echo ""
echo "🌐 Services running on:"
echo "  - Core Call Service: http://localhost:3000"
echo "  - Salesforce Service: http://localhost:3002"
echo ""
echo "Press Ctrl+C to stop all services"
