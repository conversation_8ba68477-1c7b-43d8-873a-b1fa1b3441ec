#!/bin/bash

# SimZ Platform Setup Script
echo "🚀 Setting up SimZ Platform..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js version: $(node -v)"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Install Turbo globally if not present
if ! command -v turbo &> /dev/null; then
    echo "📦 Installing Turbo globally..."
    npm install -g turbo
fi

# Create .env files if they don't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env 2>/dev/null || echo "⚠️  No .env.example found. Please create .env manually."
fi

# Build all packages
echo "🔨 Building all packages..."
turbo run build

echo "✅ Setup complete!"
echo ""
echo "🎯 Next steps:"
echo "1. Configure your .env file with the required environment variables"
echo "2. Run 'npm run dev' to start development servers"
echo "3. Run 'npm run docker:up' to start with Docker"
