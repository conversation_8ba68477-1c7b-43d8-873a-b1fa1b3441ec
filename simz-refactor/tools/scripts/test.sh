#!/bin/bash

# Test script for SimZ Platform
echo "🧪 Running tests for SimZ Platform..."

# Parse command line arguments
TEST_TYPE=${1:-"all"}

case $TEST_TYPE in
    "unit")
        echo "🔬 Running unit tests..."
        turbo run test:unit
        ;;
    "integration")
        echo "🔗 Running integration tests..."
        turbo run test:integration
        ;;
    "coverage")
        echo "📊 Running tests with coverage..."
        turbo run test:coverage
        ;;
    "all"|*)
        echo "🧪 Running all tests..."
        turbo run test
        ;;
esac

echo "✅ Tests completed!"
