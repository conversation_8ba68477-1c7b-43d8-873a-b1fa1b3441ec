-- MySQL initialization script for SimZ Platform
USE simz;

-- Create tables
CREATE TABLE IF NOT EXISTS calls (
    id INT AUTO_INCREMENT PRIMARY KEY,
    call_id VARCHAR(255) UNIQUE NOT NULL,
    from_number VA<PERSON>HAR(20),
    to_number VA<PERSON>HAR(20),
    status ENUM('active', 'completed', 'failed') DEFAULT 'active',
    duration INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_call_id (call_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    phone_number <PERSON><PERSON><PERSON><PERSON>(20),
    role ENUM('admin', 'agent', 'user') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_role (role)
);

CREATE TABLE IF NOT EXISTS transcriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    call_id VARCHAR(255) NOT NULL,
    speaker ENUM('caller', 'agent') NOT NULL,
    text TEXT NOT NULL,
    confidence DECIMAL(3,2),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_call_id (call_id),
    INDEX idx_timestamp (timestamp),
    FOREIGN KEY (call_id) REFERENCES calls(call_id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS agents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    agent_id VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    status ENUM('available', 'busy', 'offline') DEFAULT 'offline',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_agent_id (agent_id),
    INDEX idx_status (status)
);

-- Insert sample data
INSERT IGNORE INTO users (email, name, role) VALUES 
('<EMAIL>', 'Admin User', 'admin');

INSERT IGNORE INTO agents (agent_id, name, status) VALUES 
('agent-001', 'AI Agent 1', 'available');

-- Create user for the application
CREATE USER IF NOT EXISTS 'simz_user'@'%' IDENTIFIED BY 'simz_password';
GRANT ALL PRIVILEGES ON simz.* TO 'simz_user'@'%';
FLUSH PRIVILEGES;
