// MongoDB initialization script for SimZ Platform
db = db.getSiblingDB('simz');

// Create collections
db.createCollection('calls');
db.createCollection('users');
db.createCollection('transcriptions');
db.createCollection('agents');

// Create indexes for better performance
db.calls.createIndex({ "callId": 1 }, { unique: true });
db.calls.createIndex({ "createdAt": 1 });
db.calls.createIndex({ "status": 1 });

db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "phoneNumber": 1 });

db.transcriptions.createIndex({ "callId": 1 });
db.transcriptions.createIndex({ "timestamp": 1 });

db.agents.createIndex({ "agentId": 1 }, { unique: true });
db.agents.createIndex({ "status": 1 });

// Insert sample data (optional)
db.users.insertOne({
  email: "<EMAIL>",
  name: "Admin User",
  role: "admin",
  createdAt: new Date()
});

print("MongoDB initialization completed for SimZ Platform");
