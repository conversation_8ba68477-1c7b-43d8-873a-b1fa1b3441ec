# SimZ Platform Environment Configuration

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/simz
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=simz
MYSQL_USERNAME=root
MYSQL_PASSWORD=

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# API Configuration
PORT=3000
SALESFORCE_SERVICE_PORT=3002
CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# Twilio Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# Salesforce Configuration
SALESFORCE_LOGIN_URL=https://login.salesforce.com
SALESFORCE_USERNAME=your_salesforce_username
SALESFORCE_PASSWORD=your_salesforce_password
SALESFORCE_SECURITY_TOKEN=your_salesforce_security_token

# Azure Configuration (if using Azure services)
AZURE_SUBSCRIPTION_ID=your_azure_subscription_id
AZURE_CLIENT_ID=your_azure_client_id
AZURE_CLIENT_SECRET=your_azure_client_secret
AZURE_TENANT_ID=your_azure_tenant_id

# Google Cloud Configuration (if using Google services)
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account-key.json

# Email Configuration
SENDGRID_API_KEY=your_sendgrid_api_key
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# JWT Configuration
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=24h

# Environment
NODE_ENV=development
