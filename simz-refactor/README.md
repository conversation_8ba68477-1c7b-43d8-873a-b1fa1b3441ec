# SimZ Platform

A modern monorepo architecture for the SimZ call processing platform using microservices and Turbo for build orchestration.

## Architecture

This project follows a monorepo + microservices architecture:

```
simz-platform/
├── services/                    # Microservices
│   ├── core-call-service/      # Main call processing service
│   └── salesforce-service/     # CRM integration service
├── shared/                     # Shared libraries
│   ├── types/                  # TypeScript type definitions
│   ├── utils/                  # Common utility functions
│   ├── config/                 # Shared configuration
│   └── middleware/             # Reusable middleware
└── tools/                      # Build and deployment tools
    ├── scripts/                # Utility scripts
    └── docker/                 # Docker configurations
```

## Quick Start

### Prerequisites

- Node.js 18+
- npm 8+
- Docker (optional, for containerized deployment)

### Setup

1. **Clone and setup the project:**
   ```bash
   git clone <repository-url>
   cd simz-platform
   ./tools/scripts/setup.sh
   ```

2. **Configure environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start development servers:**
   ```bash
   npm run dev
   # or
   ./tools/scripts/dev.sh
   ```

## Available Scripts

### Root Level Commands

- `npm run build` - Build all packages
- `npm run dev` - Start all services in development mode
- `npm run test` - Run all tests
- `npm run lint` - Lint all packages
- `npm run format` - Format all code

### Service-Specific Commands

- `npm run start:core` - Start only core-call-service
- `npm run start:salesforce` - Start only salesforce-service

### Docker Commands

- `npm run docker:build` - Build all Docker images
- `npm run docker:up` - Start all services with Docker Compose
- `npm run docker:down` - Stop all Docker services

## Services

### Core Call Service (Port 3000)

The main call processing service that handles:
- Twilio media streams
- Speech-to-text processing
- AI integration
- Call management

**Endpoints:**
- `GET /health` - Health check
- `POST /api/calls` - Create new call
- `GET /api/calls/:id` - Get call details

### Salesforce Service (Port 3002)

CRM integration microservice that handles:
- Lead management
- Contact synchronization
- Salesforce API integration

**Endpoints:**
- `GET /health` - Health check
- `POST /api/salesforce/leads` - Create lead
- `GET /api/salesforce/leads/:id` - Get lead

## Development

### Adding a New Service

1. Create a new directory in `services/`
2. Add `package.json` with required scripts
3. Update root `package.json` workspaces
4. Add service to `docker-compose.yml`

### Using Shared Libraries

```javascript
// Import shared utilities
const { formatPhoneNumber } = require('@simz-platform/utils');

// Import shared config
const config = require('@simz-platform/config');

// Import shared middleware
const { corsMiddleware } = require('@simz-platform/middleware');
```

### Testing

```bash
# Run all tests
npm run test

# Run specific test types
./tools/scripts/test.sh unit
./tools/scripts/test.sh integration
./tools/scripts/test.sh coverage
```

## Deployment

### Docker Deployment

```bash
# Build and start all services
npm run docker:up

# View logs
npm run docker:logs

# Stop services
npm run docker:down
```

### Production Deployment

1. Build all packages: `npm run build`
2. Set production environment variables
3. Deploy using your preferred method (PM2, Docker, Kubernetes, etc.)

## Contributing

1. Create a feature branch
2. Make your changes
3. Run tests: `npm run test`
4. Run linting: `npm run lint:fix`
5. Submit a pull request

## License

ISC
